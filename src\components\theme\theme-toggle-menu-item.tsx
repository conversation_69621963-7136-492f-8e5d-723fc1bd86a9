"use client";

import { <PERSON>, <PERSON> } from "lucide-react";
import { useTheme } from "next-themes";
import * as React from "react";
import { useEffect, useState } from "react";

import { SidebarMenuButton } from "@/components/ui/sidebar";

export function ThemeToggleMenuItem() {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [currentTheme, setCurrentTheme] = useState<string>("Light");

  // useEffect to handle hydration mismatch
  useEffect(() => {
    setMounted(true);
    setCurrentTheme(theme === "dark" ? "Dark" : "Light");
  }, [theme]);

  const toggleTheme = () => {
    const newTheme = theme === "light" ? "dark" : "light";
    setTheme(newTheme);
    setCurrentTheme(newTheme === "dark" ? "Dark" : "Light");
  };

  // Prevent hydration mismatch
  if (!mounted) {
    return (
      <SidebarMenuButton asChild className="cursor-pointer" tooltip="Theme">
        <button className="flex w-full cursor-pointer items-center gap-2">
          <div className="flex h-5 w-5 items-center justify-center">
            {/* Placeholder for hydration */}
            <div className="h-5 w-5" />
          </div>
          <span>Theme</span>
        </button>
      </SidebarMenuButton>
    );
  }

  return (
    <SidebarMenuButton asChild className="cursor-pointer" tooltip="Theme">
      <button className="flex w-full cursor-pointer items-center gap-2" onClick={toggleTheme}>
        <div className="flex h-5 w-5 items-center justify-center">
          <Sun className="dark:-rotate-90 h-5 w-5 rotate-0 scale-100 transition-all dark:scale-0" />
          <Moon className="absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
        </div>
        <span>{currentTheme}</span>
      </button>
    </SidebarMenuButton>
  );
}
