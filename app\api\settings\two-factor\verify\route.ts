import crypto from "node:crypto";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { z } from "zod/v4";
import { verifyTOTP } from "@/lib/auth";
import { authOptions } from "@/lib/auth/auth-options";
import { prisma } from "@/lib/db/prisma";
import { clearQRCodeCache } from "@/lib/utils/qr-code";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";

// Validation schema for verification request
const verifySchema = z.object({
  token: z.string().length(6).regex(/^\d+$/, "Token must contain only digits"),
});

// Function to generate backup codes
function generateBackupCodes(count = 10): string[] {
  const codes = [];
  for (let i = 0; i < count; i++) {
    // Generate a random 6-character alphanumeric code
    const code = crypto.randomBytes(3).toString("hex").toUpperCase();
    codes.push(code);
  }
  return codes;
}

// POST endpoint to verify token and enable 2FA
export async function POST(request: Request) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const t = await getTranslations({ locale, namespace: "errors" });
  const tAdmin = await getTranslations({ locale, namespace: "admin" });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require authentication for verifying 2FA
    if (!session || session.user.role !== "admin") {
      return NextResponse.json({ error: t("unauthorized") }, { status: 401 });
    }

    // Parse and validate request body
    const data = await request.json();
    const validatedData = verifySchema.parse(data);

    // Get admin ID from session
    const adminId = Number.parseInt(session.user.id, 10);

    // Get admin from database
    const admin = await prisma.admin.findUnique({
      where: { id: adminId },
    });

    if (!admin) {
      return NextResponse.json({ error: t("adminNotFound") }, { status: 404 });
    }

    // Check if admin has a 2FA secret
    if (!admin.twoFactorSecret) {
      return NextResponse.json({ error: t("twoFactorNotSetup") }, { status: 400 });
    }

    // Verify the token
    const isValid = verifyTOTP(validatedData.token, admin.twoFactorSecret);

    if (!isValid) {
      return NextResponse.json({ error: tAdmin("invalidVerificationCode") }, { status: 400 });
    }

    // Generate backup codes
    const backupCodes = generateBackupCodes();

    // Enable 2FA and set backup codes
    await prisma.admin.update({
      where: { id: adminId },
      data: {
        twoFactorEnabled: true,
        twoFactorBackupCodes: JSON.stringify(backupCodes),
      },
    });

    // Clear 2FA QR code from cache for security
    clearQRCodeCache("2fa", admin.username);

    // Log the action
    try {
      await prisma.auditLog.create({
        data: {
          userType: "admin",
          userId: adminId,
          action: "enable-2fa",
          entityType: "admins",
          recordId: adminId,
          newValues: JSON.stringify({
            twoFactorEnabled: true,
            backupCodesGenerated: true,
          }),
        },
      });
    } catch (_logError) {}

    // Return success and backup codes
    return NextResponse.json({
      success: true,
      message: "Two-factor authentication enabled",
      backupCodes,
    });
  } catch (error) {
    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.issues[0].message }, { status: 400 });
    }

    return NextResponse.json({ error: t("failedToVerifyTwoFactor") }, { status: 500 });
  }
}
