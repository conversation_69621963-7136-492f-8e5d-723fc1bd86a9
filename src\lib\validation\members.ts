import { getTranslations } from 'next-intl/server';
import { z } from 'zod/v4';
import { getTodayInSydney, startOfDay } from '@/lib/utils/date-time';

/**
 * Server-side validation schemas for member management
 * These schemas now support translations using next-intl's errorMap pattern
 * For client-side validation with translations, use the client validation utilities
 */

/**
 * Create member schema with translations
 */
export async function createMemberSchemaWithTranslations(
  locale: 'en' | 'zh-CN' = 'en'
) {
  const t = await getTranslations({ locale, namespace: 'validation' });

  return z.object({
    firstName: z
      .string()
      .min(1, { error: t('firstNameRequired') })
      .max(50, { error: t('firstNameTooLong') }),
    lastName: z
      .string()
      .min(1, { error: t('lastNameRequired') })
      .max(50, { error: t('lastNameTooLong') }),
    dateOfBirth: z.union([
      z.date().refine(
        (date) => {
          const today = getTodayInSydney();
          const dateToCheck = startOfDay(date);
          return dateToCheck <= today;
        },
        { error: t('dateOfBirthCannotBeInTheFuture') }
      ),
      z.string().refine(
        (val) => {
          const parsedDate = new Date(val);
          if (isNaN(parsedDate.getTime())) return false; // Invalid date format
          const today = getTodayInSydney();
          const dateToCheck = startOfDay(parsedDate);
          return dateToCheck <= today;
        },
        { error: t('dateOfBirthCannotBeInTheFuture') }
      ),
    ]),
    gender: z.enum(['male', 'female', 'other'], { error: t('genderRequired') }),
    mobilePhone: z
      .string()
      .min(10, { error: t('mobilePhoneMustBeAtLeast10Digi') })
      .max(10, { error: t('mobilePhoneCannotExceed10Digit') })
      .regex(/^\d+$/, { error: t('mobilePhoneCanOnlyContainNumbe') }),
    hobby: z
      .string()
      .max(100, { error: t('hobbyTooLong') })
      .optional()
      .or(z.literal('')),
    occupation: z
      .string()
      .max(100, { error: t('occupationTooLong') })
      .optional()
      .or(z.literal('')),
    relationship: z.enum(
      ['head', 'spouse', 'child', 'parent', 'relative', 'other'],
      { error: t('relationshipRequired') }
    ),
  });
}

/**
 * Validation schema for member search and filtering parameters
 */
export const memberSearchSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(20),
  searchTerm: z.string().optional(),
  sortBy: z
    .enum([
      'id',
      'firstName',
      'lastName',
      'age',
      'gender',
      'mobilePhone',
      'suburb',
      'relationship',
      'census_year',
      'sacrament_count',
      'createdAt',
    ])
    .default('id'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
  gender: z.enum(['male', 'female', 'other']).optional(),
  relationship: z
    .enum(['head', 'spouse', 'child', 'parent', 'relative', 'other'])
    .optional(),
  censusYearId: z.number().optional(),
  sacramentStatus: z.enum(['none', 'partial', 'complete']).optional(),
  minAge: z.number().min(0).max(150).optional(),
  maxAge: z.number().min(0).max(150).optional(),
});

export type MemberSearchParams = z.infer<typeof memberSearchSchema>;

/**
 * Create update member schema with translations
 */
export async function createUpdateMemberSchema(locale: 'en' | 'zh-CN' = 'en') {
  const t = await getTranslations({ locale, namespace: 'validation' });

  return z.object({
    firstName: z
      .string()
      .min(1, { error: t('firstNameRequired') })
      .max(50, { error: t('firstNameTooLong') }),
    lastName: z
      .string()
      .min(1, { error: t('lastNameRequired') })
      .max(50, { error: t('lastNameTooLong') }),
    dateOfBirth: z.union([
      z.date().refine(
        (date) => {
          const today = getTodayInSydney();
          const dateToCheck = startOfDay(date);
          return dateToCheck <= today;
        },
        {
          error: t('dateOfBirthCannotBeInTheFuture'),
        }
      ),
      z.string().refine(
        (val) => {
          const parsedDate = new Date(val);
          if (isNaN(parsedDate.getTime())) return false; // Invalid date format
          const today = getTodayInSydney();
          const dateToCheck = startOfDay(parsedDate);
          return dateToCheck <= today;
        },
        {
          error: t('dateOfBirthCannotBeInTheFuture'),
        }
      ),
    ]),
    gender: z.enum(['male', 'female', 'other'], {
      error: t('genderRequired'),
    }),
    mobilePhone: z
      .string()
      .min(10, { error: t('mobilePhoneMustBeAtLeast10Digi') })
      .max(10, { error: t('mobilePhoneCannotExceed10Digit') })
      .regex(/^\d+$/, { error: t('mobilePhoneCanOnlyContainNumbe') }),
    hobby: z
      .string()
      .max(100, { error: t('hobbyTooLong') })
      .optional()
      .or(z.literal('')),
    occupation: z
      .string()
      .max(100, { error: t('occupationTooLong') })
      .optional()
      .or(z.literal('')),
    relationship: z.enum(
      ['head', 'spouse', 'child', 'parent', 'relative', 'other'],
      {
        error: t('relationshipRequired'),
      }
    ),
  });
}

/**
 * Create member schema with translations (for creating new members)
 */
export async function createCreateMemberSchema(locale: 'en' | 'zh-CN' = 'en') {
  const t = await getTranslations({ locale, namespace: 'validation' });
  const updateSchema = await createUpdateMemberSchema(locale);

  return updateSchema.extend({
    householdId: z.number().min(1, { error: t('householdIdRequired') }),
    censusYearId: z.number().min(1, { error: t('censusYearIdRequired') }),
  });
}

// Type exports for server-side validation
export type ServerUpdateMemberFormValues = z.infer<
  Awaited<ReturnType<typeof createUpdateMemberSchema>>
>;
export type ServerCreateMemberFormValues = z.infer<
  Awaited<ReturnType<typeof createCreateMemberSchema>>
>;

/**
 * Create bulk member operation schema with translations
 */
export async function createBulkMemberOperationSchema(
  locale: 'en' | 'zh-CN' = 'en'
) {
  const t = await getTranslations({ locale, namespace: 'validation' });

  return z.object({
    memberIds: z
      .array(z.number())
      .min(1, { error: t('memberSelectionRequired') }),
    operation: z.enum(['delete', 'export', 'update_census_year']),
    newCensusYearId: z.number().optional(),
  });
}

/**
 * Create bulk member validation schema with translations (for validation-only operations)
 */
export async function createBulkMemberValidationSchema(
  locale: 'en' | 'zh-CN' = 'en'
) {
  const t = await getTranslations({ locale, namespace: 'validation' });

  return z.object({
    memberIds: z
      .array(z.number())
      .min(1, { error: t('atLeastOneMemberIdRequired') }),
  });
}

export type ServerBulkMemberOperationData = z.infer<
  Awaited<ReturnType<typeof createBulkMemberOperationSchema>>
>;
