import { useCallback, useEffect, useState } from "react";
import { StateManager } from "../utils/memory/state-manager";

interface UseCollapsibleStateOptions {
	key: string;
	defaultCollapsed?: boolean;
	onToggle?: (isCollapsed: boolean) => void;
}

interface UseCollapsibleStateReturn {
	isCollapsed: boolean;
	toggle: () => void;
	setCollapsed: (collapsed: boolean) => void;
}

/**
 * Custom hook for managing collapsible state with automatic memory cleanup
 * Eliminates code duplication between charts and tables
 */
export function useCollapsibleState({
	key,
	defaultCollapsed = false,
	onToggle,
}: UseCollapsibleStateOptions): UseCollapsibleStateReturn {
	// Initialize state from StateManager or use default
	const [isCollapsed, setIsCollapsedState] = useState(() => {
		return StateManager.getCollapseState(key) ?? defaultCollapsed;
	});

	// Update StateManager when state changes
	useEffect(() => {
		StateManager.setCollapseState(key, isCollapsed);
	}, [key, isCollapsed]);

	// Cleanup on unmount
	useEffect(() => {
		return () => {
			StateManager.cleanup(key);
		};
	}, [key]);

	const toggle = useCallback(() => {
		const newState = !isCollapsed;
		setIsCollapsedState(newState);
		onToggle?.(newState);
	}, [isCollapsed, onToggle]);

	const setCollapsed = useCallback(
		(collapsed: boolean) => {
			setIsCollapsedState(collapsed);
			onToggle?.(collapsed);
		},
		[onToggle],
	);

	return {
		isCollapsed,
		toggle,
		setCollapsed,
	};
}
