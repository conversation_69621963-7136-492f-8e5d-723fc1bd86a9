/**
 * Configuration for suburb autocomplete component
 * Allows easy switching between optimised (load-all) and search-based approaches
 */

export const SUBURB_AUTOCOMPLETE_CONFIG = {
	// Use optimised load-all approach (recommended for 1000+ concurrent users)
	USE_OPTIMIZED: true,

	// Fallback to search API if load-all fails
	ENABLE_FALLBACK: true,

	// Cache duration for load-all endpoint (in seconds)
	CACHE_DURATION: 3600, // 1 hour

	// Maximum results to show in dropdown
	MAX_RESULTS: 50,

	// Minimum characters required for search
	MIN_SEARCH_CHARS: 2,
} as const;

export type SuburbAutocompleteConfig = typeof SUBURB_AUTOCOMPLETE_CONFIG;
