import { defineRouting } from "next-intl/routing";

export const routing = defineRouting({
  // A list of all locales that are supported
  locales: ["en", "zh-CN"],

  // Used when no locale matches
  defaultLocale: "en",

  // SIMPLE MODE: No URL routing - URLs stay the same like PHP
  // This means /admin stays /admin in both languages
  // Language is stored in cookies/session only
  localePrefix: "never", // This prevents locale prefixes in URLs

  // Professional GDPR compliant cookie settings (new in v4.0)
  localeCookie: {
    name: "NEXT_LOCALE",
    // Professional enterprise settings:
    sameSite: "lax", // GDPR compliant
    secure: process.env.NODE_ENV === "production", // HTTPS only in production
    maxAge: 60 * 60 * 24 * 365, // 1 year persistence for better UX
  },
});
