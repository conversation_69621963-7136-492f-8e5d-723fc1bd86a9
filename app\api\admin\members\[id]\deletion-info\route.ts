import { type NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { authOptions } from "@/lib/auth/auth-options";
import { getMemberDeletionInfo } from "@/lib/db/members";
import { getErrorMessage } from "@/lib/utils/error-handling";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";

/**
 * GET /api/admin/members/:id/deletion-info
 *
 * Gets member deletion information for validation and UX
 * Only accessible to admin users
 */
export async function GET(
	_request: NextRequest,
	{ params }: { params: Promise<{ id: string }> },
) {
	try {
		// Check authentication
		const session = await getServerSession(authOptions);

		// Require admin authentication
		if (!session || session.user.role !== "admin") {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// In Next.js 15+, params is a Promise that must be awaited
		const { id: idString } = await params;
		const id = Number.parseInt(idString, 10);

		if (Number.isNaN(id)) {
			return NextResponse.json({ error: "Invalid member ID" }, { status: 400 });
		}

		// Get member deletion info
		const deletionInfo = await getMemberDeletionInfo(id);

		return NextResponse.json(deletionInfo);
	} catch (error) {
		const locale = await getLocaleFromCookies();
		const t = await getTranslations({ locale, namespace: "errors" });
		return NextResponse.json(
			{ error: t("memberDeletionInfoFailed"), details: getErrorMessage(error) },
			{ status: 500 },
		);
	}
}
