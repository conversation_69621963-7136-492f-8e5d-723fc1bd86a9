import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { getServerSession } from "next-auth";
import { isCensusOpen } from "@/lib/census/census-availability";
import { censusAuthOptions } from "./census-auth-options";

/**
 * Server-side authentication check for census participant pages
 * Redirects to home page if user is not authenticated or census is closed
 * Enhanced to detect account deletion similar to middleware logic
 */
export async function requireCensusAuth() {
  // First check if census is open
  const censusStatus = await isCensusOpen();
  if (!censusStatus.isOpen) {
    // Redirect to home page with census closed message using the toast-redirect API
    redirect("/api/census/auth/toast-redirect?reason=census_closed&redirectTo=/");
  }

  // Check for account deletion using the same logic as middleware
  // This handles the case where getServerSession returns null due to ACCOUNT_DELETED error
  const cookieStore = await cookies();
  const sessionCookie = cookieStore.get("census-session-token");

  // Try to get the session
  const session = await getServerSession(censusAuthOptions);

  if (!session) {
    // If there's a session cookie but no session, it's likely account deletion
    if (sessionCookie?.value && sessionCookie.value.length > 0) {
      // Account was likely deleted - redirect with account deletion message
      redirect("/api/census/auth/toast-redirect?reason=account_deleted&redirectTo=/");
    }

    // No cookie or empty cookie - normal unauthenticated case
    redirect("/api/census/auth/toast-redirect?reason=unauthenticated&redirectTo=/");
  }

  // Check if the user has the household role
  if (session?.user?.role !== "household") {
    // Redirect to home page with unauthorized message using the toast-redirect API
    redirect("/api/census/auth/toast-redirect?reason=unauthorized&redirectTo=/");
  }

  return session;
}

/**
 * Get the census session if it exists, without redirecting
 * Useful for optional authentication checks
 */
export async function getCensusSession() {
  return await getServerSession(censusAuthOptions);
}

/**
 * Check if a user is authenticated for census participation
 * Returns boolean without redirecting
 */
export async function isCensusAuthenticated() {
  const session = await getServerSession(censusAuthOptions);
  return !!session && session?.user?.role === "household";
}
