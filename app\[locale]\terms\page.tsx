import type { Metadata } from "next";
import { TermsClient } from "@/components/terms/terms-client";
import { getChurchInfo } from "@/lib/db/settings";

// Force dynamic rendering to prevent DYNAMIC_SERVER_USAGE errors
export const dynamic = "force-dynamic";

export const metadata: Metadata = {
  title: "Terms of Service | WSCCC Census System",
  description: "Terms of Service for the Western Sydney Catholic Chinese Community Census System",
};

// Server component to fetch church information
export default async function TermsPage() {
  // Fetch church information from the database
  let churchInfo = {
    churchName: "",
    email: "",
    contactNumber: "",
    address: "",
  };

  try {
    // Try to get church info directly from the database (now with caching)
    churchInfo = await getChurchInfo();
  } catch (_error) {
    // Fallback to default values if database fetch fails
    churchInfo = {
      churchName: "WSCCC Church",
      email: "<EMAIL>",
      contactNumber: "0412345678",
      address: "123 Main Street, Sydney NSW 2000",
    };
  }

  return <TermsClient churchInfo={churchInfo} />;
}
