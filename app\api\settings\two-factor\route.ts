import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { authOptions } from "@/lib/auth/auth-options";
import { prisma } from "@/lib/db/prisma";
import { getAdminById } from "@/lib/db/users";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";

// GET endpoint to fetch current 2FA status
export async function GET() {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const t = await getTranslations({ locale, namespace: "errors" });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require authentication for accessing 2FA settings
    if (!session || session.user.role !== "admin") {
      return NextResponse.json({ error: t("unauthorized") }, { status: 401 });
    }

    // Get admin ID from session
    const adminId = Number.parseInt(session.user.id, 10);

    // Get admin from database
    const admin = await getAdminById(adminId);

    if (!admin) {
      return NextResponse.json({ error: t("adminNotFound") }, { status: 404 });
    }

    // Return 2FA status
    return NextResponse.json({
      enabled: admin.twoFactorEnabled,
      hasSecret: !!admin.twoFactorSecret,
      hasBackupCodes: !!admin.twoFactorBackupCodes,
    });
  } catch (_error) {
    return NextResponse.json({ error: t("failedToFetchTwoFactorStatus") }, { status: 500 });
  }
}

// DELETE endpoint to disable 2FA
export async function DELETE() {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const t = await getTranslations({ locale, namespace: "errors" });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require authentication for disabling 2FA
    if (!session || session.user.role !== "admin") {
      return NextResponse.json({ error: t("unauthorized") }, { status: 401 });
    }

    // Get admin ID from session
    const adminId = Number.parseInt(session.user.id, 10);

    // Disable 2FA
    await prisma.admin.update({
      where: { id: adminId },
      data: {
        twoFactorEnabled: false,
        twoFactorSecret: null,
        twoFactorBackupCodes: null,
      },
    });

    // Log the action
    try {
      await prisma.auditLog.create({
        data: {
          userType: "admin",
          userId: adminId,
          action: "disable-2fa",
          entityType: "admins",
          entityId: adminId,
          newValues: JSON.stringify({ twoFactorEnabled: false }),
        },
      });
    } catch (_logError) {}

    return NextResponse.json({
      success: true,
      message: "Two-factor authentication disabled",
    });
  } catch (_error) {
    return NextResponse.json({ error: t("failedToDisableTwoFactor") }, { status: 500 });
  }
}
