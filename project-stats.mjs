#!/usr/bin/env node

/**
 * Project Line Counter
 * Counts lines of code in the WSCCC Census System project
 *
 * Usage: node count-lines.mjs
 * Or: npm run count-lines
 * Or: npm run stats
 */

import { readdirSync, readFileSync, statSync } from "fs";
import { extname, join } from "path";

// File extensions to count
const EXTENSIONS = [
	".ts",
	".tsx",
	".js",
	".jsx", // TypeScript/JavaScript
	".css",
	".scss", // Styles
	".md",
	".mdx", // Documentation
	".json", // Configuration
	".sql", // Database
	".prisma", // Prisma schema
	".env.example", // Environment examples
];

// Directories to exclude
const EXCLUDE_DIRS = [
	"node_modules",
	".next",
	".git",
	"dist",
	"build",
	"coverage",
	".nyc_output",
	"public/images",
	"public/icons",
];

// Files to exclude
const EXCLUDE_FILES = [
	"package-lock.json",
	"yarn.lock",
	"pnpm-lock.yaml",
	".DS_Store",
	"Thumbs.db",
];

function shouldIncludeFile(filePath, fileName) {
	// Check if file should be excluded
	if (EXCLUDE_FILES.includes(fileName)) {
		return false;
	}

	// Check extension
	const ext = extname(fileName);
	if (!(EXTENSIONS.includes(ext) || fileName.endsWith(".env.example"))) {
		return false;
	}

	// Check if in excluded directory
	for (const excludeDir of EXCLUDE_DIRS) {
		if (
			filePath.includes(`/${excludeDir}/`) ||
			filePath.includes(`\\${excludeDir}\\`)
		) {
			return false;
		}
	}

	return true;
}

function countLinesInFile(filePath) {
	try {
		const content = readFileSync(filePath, "utf8");
		const lines = content.split("\n");

		// Count different types of lines
		const totalLines = lines.length;
		let codeLines = 0;
		let commentLines = 0;
		let blankLines = 0;

		for (const line of lines) {
			const trimmed = line.trim();

			if (trimmed === "") {
				blankLines++;
			} else if (
				trimmed.startsWith("//") ||
				trimmed.startsWith("/*") ||
				trimmed.startsWith("*") ||
				trimmed.startsWith("#") ||
				trimmed.startsWith("<!--")
			) {
				commentLines++;
			} else {
				codeLines++;
			}
		}

		return { totalLines, codeLines, commentLines, blankLines };
	} catch (error) {
		console.warn(`Warning: Could not read file ${filePath}: ${error.message}`);
		return { totalLines: 0, codeLines: 0, commentLines: 0, blankLines: 0 };
	}
}

function scanDirectory(
	dirPath,
	stats = {
		files: 0,
		totalLines: 0,
		codeLines: 0,
		commentLines: 0,
		blankLines: 0,
	},
	fileTypes = {},
) {
	try {
		const items = readdirSync(dirPath);

		for (const item of items) {
			const itemPath = join(dirPath, item);

			try {
				const stat = statSync(itemPath);

				if (stat.isDirectory()) {
					// Skip excluded directories
					if (!EXCLUDE_DIRS.includes(item)) {
						scanDirectory(itemPath, stats, fileTypes);
					}
				} else if (stat.isFile() && shouldIncludeFile(itemPath, item)) {
					const fileStats = countLinesInFile(itemPath);

					stats.files++;
					stats.totalLines += fileStats.totalLines;
					stats.codeLines += fileStats.codeLines;
					stats.commentLines += fileStats.commentLines;
					stats.blankLines += fileStats.blankLines;

					// Track by file type
					const ext = extname(item) || "other";
					if (!fileTypes[ext]) {
						fileTypes[ext] = { files: 0, lines: 0 };
					}
					fileTypes[ext].files++;
					fileTypes[ext].lines += fileStats.totalLines;
				}
			} catch (error) {
				console.warn(
					`Warning: Could not process ${itemPath}: ${error.message}`,
				);
			}
		}
	} catch (error) {
		console.warn(
			`Warning: Could not read directory ${dirPath}: ${error.message}`,
		);
	}

	return { stats, fileTypes };
}

// Main execution
console.log("🔍 Counting lines in WSCCC Census System...\n");

const startTime = Date.now();
const { stats, fileTypes } = scanDirectory(".");
const endTime = Date.now();

// Display results
console.log("📊 **PROJECT STATISTICS**\n");
console.log(`📁 Total Files: ${stats.files.toLocaleString()}`);
console.log(`📝 Total Lines: ${stats.totalLines.toLocaleString()}`);
console.log(`💻 Code Lines: ${stats.codeLines.toLocaleString()}`);
console.log(`💬 Comment Lines: ${stats.commentLines.toLocaleString()}`);
console.log(`⬜ Blank Lines: ${stats.blankLines.toLocaleString()}`);
console.log(`⏱️  Scan Time: ${endTime - startTime}ms\n`);

console.log("📋 **BY FILE TYPE**\n");
const sortedTypes = Object.entries(fileTypes).sort(
	(a, b) => b[1].lines - a[1].lines,
);

for (const [ext, data] of sortedTypes) {
	const percentage = ((data.lines / stats.totalLines) * 100).toFixed(1);
	console.log(
		`${ext.padEnd(8)} ${data.files.toString().padStart(4)} files  ${data.lines.toString().padStart(6)} lines  (${percentage}%)`,
	);
}

console.log("\n🎉 Line counting completed!");
