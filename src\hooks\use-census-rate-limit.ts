"use client";

import { useCallback, useEffect, useRef, useState } from "react";
import type { EnhancedRateLimitStatus } from "@/lib/auth/rate-limiting-service";

interface UseCensusRateLimitReturn {
  isLocked: boolean;
  remainingTime: number;
  attemptsRemaining: number;
  escalationLevel: number;
  formattedTime: string;
  isLoading: boolean;
  checkStatus: () => Promise<HybridRateLimitState>;
}

interface HybridRateLimitState {
  isLocked: boolean;
  remainingTime: number;
  attemptsRemaining: number;
  escalationLevel: number;
  lockoutExpiry: number | null;
  lastServerSync: number;
}

/**
 * Hybrid rate limiting hook for census authentication
 * Uses local countdown with strategic server validation for optimal UX and security
 *
 * Key Features:
 * - Local countdown timer for smooth UX
 * - Server validation at critical points
 * - Multi-tab synchronization via localStorage
 * - Drift detection and correction
 * - Strategic server checks (on expiry, page load, auth attempts)
 */
export function useCensusRateLimit(): UseCensusRateLimitReturn {
  const [state, setState] = useState<HybridRateLimitState>({
    isLocked: false,
    remainingTime: 0,
    attemptsRemaining: 5,
    escalationLevel: 0,
    lockoutExpiry: null,
    lastServerSync: 0,
  });

  const [isLoading, setIsLoading] = useState(true);
  const timeoutRef = useRef<number | null>(null);
  const isValidatingRef = useRef(false);
  const isMountedRef = useRef(true);
  const checkStatusRef = useRef<(() => Promise<HybridRateLimitState>) | null>(null);
  const localStorageAvailable = useRef<boolean>(true);

  // Storage keys for multi-tab sync
  const STORAGE_KEY = "census-rate-limit-state";
  const STORAGE_EVENT_KEY = "census-rate-limit-update";

  /**
   * Format time in MM:SS format
   */
  const formatTime = (milliseconds: number): string => {
    const totalSeconds = Math.ceil(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  };

  /**
   * Save state to localStorage for multi-tab sync with graceful degradation
   */
  const saveStateToStorage = useCallback((newState: HybridRateLimitState) => {
    if (!localStorageAvailable.current) {
      return; // Skip if localStorage is not available
    }

    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(newState));
      // Trigger storage event for other tabs
      localStorage.setItem(STORAGE_EVENT_KEY, Date.now().toString());
    } catch (error) {
      // Mark localStorage as unavailable and disable multi-tab sync
      if (process.env.NODE_ENV === "development") {
        console.warn("localStorage unavailable, disabling multi-tab sync:", error);
      }
      localStorageAvailable.current = false;
    }
  }, []);

  /**
   * Load state from localStorage with comprehensive validation and graceful degradation
   */
  const loadStateFromStorage = useCallback((): HybridRateLimitState | null => {
    if (!localStorageAvailable.current) {
      return null; // Skip if localStorage is not available
    }

    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored) as HybridRateLimitState;

        // Comprehensive validation of stored state
        if (
          typeof parsed.isLocked === "boolean" &&
          typeof parsed.remainingTime === "number" &&
          typeof parsed.attemptsRemaining === "number" &&
          typeof parsed.escalationLevel === "number" &&
          typeof parsed.lastServerSync === "number" &&
          (parsed.lockoutExpiry === null || typeof parsed.lockoutExpiry === "number") &&
          parsed.remainingTime >= 0 &&
          parsed.attemptsRemaining >= 0 &&
          parsed.escalationLevel >= 0 &&
          parsed.lastServerSync >= 0
        ) {
          return parsed;
        }
        if (process.env.NODE_ENV === "development") {
          console.warn("Invalid rate limit state in localStorage, clearing...");
        }
        localStorage.removeItem(STORAGE_KEY);
      }
    } catch (error) {
      if (process.env.NODE_ENV === "development") {
        console.warn("localStorage error, disabling multi-tab sync:", error);
      }
      localStorageAvailable.current = false;
      // Clear corrupted data if possible
      try {
        localStorage.removeItem(STORAGE_KEY);
      } catch (clearError) {
        // Ignore cleanup errors
      }
    }
    return null;
  }, []);

  /**
   * Start local countdown timer using server's lockout expiry timestamp
   * This provides smooth UX while maintaining server authority
   */
  const startLocalCountdown = useCallback(
    (lockoutExpiry: number, checkStatusFn?: () => Promise<void>) => {
      // Clear any existing timer
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }

      const updateCountdown = () => {
        try {
          // Simple client time - server validates auth attempts anyway
          const now = Date.now();
          const remainingTime = Math.max(0, lockoutExpiry - now);

          if (remainingTime <= 0) {
            // Countdown expired - SECURITY: Do NOT unlock client-side, let server decide
            if (isMountedRef.current) {
              // Only update remaining time to 0, keep locked until server confirms unlock
              setState((prevState) => ({
                ...prevState,
                remainingTime: 0,
                // Keep isLocked and lockoutExpiry unchanged - server will update these
              }));

              // Strategic server check when countdown expires - server will unlock if appropriate
              if (checkStatusFn) {
                checkStatusFn();
              } else if (checkStatusRef.current) {
                checkStatusRef.current();
              }
            }
            return;
          }

          // Update remaining time
          if (isMountedRef.current) {
            setState((prevState) => ({
              ...prevState,
              remainingTime,
            }));
          }

          // Schedule next update with slight randomization to prevent timing attacks
          const baseInterval = Math.min(1000, remainingTime);
          const jitter = Math.random() * 100; // Add 0-100ms jitter
          const nextUpdate = Math.max(100, baseInterval + jitter); // Ensure minimum 100ms

          try {
            timeoutRef.current = setTimeout(updateCountdown, nextUpdate) as unknown as number;
          } catch (timerError) {
            if (process.env.NODE_ENV === "development") {
              console.warn("setTimeout failed, using client-side countdown only:", timerError);
            }
            // If setTimeout fails, just rely on client time - server validates on auth attempts anyway
            // No fallback polling needed since we check server on every authentication attempt
          }
        } catch (error) {
          if (process.env.NODE_ENV === "development") {
            console.error("Error in countdown timer:", error);
          }
          // Recovery: clear timer and validate with server
          if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
            timeoutRef.current = null;
          }
          if (isMountedRef.current) {
            if (checkStatusFn) {
              checkStatusFn();
            } else if (checkStatusRef.current) {
              checkStatusRef.current();
            }
          }
        }
      };

      // Start the countdown
      updateCountdown();
    },
    [],
  );

  /**
   * Fetch enhanced rate limit status from server
   * Used for strategic validation points in hybrid approach
   * Returns the updated state to prevent async race conditions
   */
  const checkStatus = useCallback(async (): Promise<HybridRateLimitState> => {
    // Prevent concurrent validation requests
    if (isValidatingRef.current) {
      return state; // Return current state if already validating
    }

    isValidatingRef.current = true;

    try {
      const response = await fetch("/api/census/auth/rate-limit-status", {
        method: "GET",
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error("Failed to fetch census rate limit status");
      }

      const result = await response.json();

      if (result.success && result.data) {
        const serverData = result.data as EnhancedRateLimitStatus;
        const now = Date.now();

        // Simple calculation - if countdown is slightly off, server validates anyway
        let remainingTime = 0;
        if (serverData.isLocked && serverData.lockoutExpiry) {
          remainingTime = Math.max(0, serverData.lockoutExpiry - now);
        }

        const newState: HybridRateLimitState = {
          isLocked: serverData.isLocked && remainingTime > 0, // Only locked if time remaining
          remainingTime,
          attemptsRemaining: serverData.attemptsRemaining,
          escalationLevel: serverData.escalationLevel,
          lockoutExpiry: serverData.lockoutExpiry,
          lastServerSync: now,
        };

        // Only update state if validation is still current and component is mounted
        if (isValidatingRef.current && isMountedRef.current) {
          setState(newState);
          saveStateToStorage(newState);

          // Start local countdown if locked
          if (newState.isLocked && newState.lockoutExpiry) {
            startLocalCountdown(newState.lockoutExpiry);
          }
        }

        return newState;
      }

      // If no data in response, return current state
      return state;
    } catch (error) {
      if (process.env.NODE_ENV === "development") {
        console.error("Error checking census rate limit status:", error);
      }
      // Return current state on error to avoid disrupting user experience
      return state;
    } finally {
      setIsLoading(false);
      isValidatingRef.current = false;
    }
    // Note: startLocalCountdown is intentionally excluded from dependencies to avoid circular dependency
    // The function is stable and doesn't need to be recreated when startLocalCountdown changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [saveStateToStorage, state]);

  // Update checkStatus ref to avoid circular dependencies
  useEffect(() => {
    checkStatusRef.current = checkStatus;
  }, [checkStatus]);

  /**
   * Stop local countdown timer
   */
  const stopLocalCountdown = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  // Initialize hook - check server and load from storage
  useEffect(() => {
    const initialize = async () => {
      // Try to load from localStorage first for instant UI update
      const storedState = loadStateFromStorage();
      if (storedState && isMountedRef.current) {
        // Validate stored lockout is still active
        const now = Date.now();
        if (storedState.isLocked && storedState.lockoutExpiry && storedState.lockoutExpiry > now) {
          setState(storedState);
          startLocalCountdown(storedState.lockoutExpiry);
        } else if (storedState.isLocked) {
          // Stored lockout expired, clear it
          const clearedState = {
            ...storedState,
            isLocked: false,
            remainingTime: 0,
            lockoutExpiry: null,
          };
          setState(clearedState);
          saveStateToStorage(clearedState);
        } else {
          setState(storedState);
        }
      }

      // Always check server for authoritative status
      if (isMountedRef.current) {
        await checkStatus();
      }
    };

    initialize();
    // Only run once on mount - dependencies removed to prevent infinite loops
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Multi-tab synchronization via localStorage events
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === STORAGE_EVENT_KEY && isMountedRef.current) {
        const storedState = loadStateFromStorage();
        if (storedState) {
          setState(storedState);

          // Sync countdown timer
          if (storedState.isLocked && storedState.lockoutExpiry) {
            startLocalCountdown(storedState.lockoutExpiry);
          } else {
            stopLocalCountdown();
          }
        }
      }
    };

    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, [loadStateFromStorage, startLocalCountdown, stopLocalCountdown]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      stopLocalCountdown();
    };
  }, [stopLocalCountdown]);

  return {
    isLocked: state.isLocked,
    remainingTime: state.remainingTime,
    attemptsRemaining: state.attemptsRemaining,
    escalationLevel: state.escalationLevel,
    formattedTime: formatTime(state.remainingTime),
    isLoading,
    checkStatus, // Exposed for manual validation (e.g., on auth attempts)
  };
}
