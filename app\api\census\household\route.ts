import { type NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { censusAuthOptions } from "@/lib/census-auth/census-auth-options";
import { getHouseholdHead } from "@/lib/db/household-members";
import { getHouseholdById } from "@/lib/db/households";
import { prisma } from "@/lib/db/prisma";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";

/**
 * GET /api/census/household
 *
 * Fetches the household information for the current user
 */
export async function GET(_request: NextRequest) {
	// Get locale from centralized utility at the beginning
	const locale = await getLocaleFromCookies();
	const tErrors = await getTranslations({ locale, namespace: "errors" });
	const tAdmin = await getTranslations({ locale, namespace: "admin" });

	try {
		// Check if user is authenticated
		const session = await getServerSession(censusAuthOptions);

		if (!session) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Check if user has the household role
		if (session.user.role !== "household") {
			return NextResponse.json({ error: "Forbidden" }, { status: 403 });
		}

		// Check if household ID exists in session
		if (!session.user.householdId) {
			return NextResponse.json(
				{
					error: tErrors("householdNotRegistered"),
					message: tErrors("pleaseCompleteHouseholdRegistrationFirst"),
				},
				{ status: 400 },
			);
		}

		const householdId = Number.parseInt(session.user.householdId, 10);
		const censusYearId = Number.parseInt(session.user.censusYearId, 10);

		// Get the household information
		const household = await getHouseholdById(householdId);

		if (!household) {
			return NextResponse.json(
				{ error: tErrors("householdNotFound") },
				{ status: 404 },
			);
		}

		// Get the household head information
		const householdHeadRelation = await getHouseholdHead(
			householdId,
			censusYearId,
		);

		// Get the census form status
		const formStatus = await prisma.censusForm.findFirst({
			where: {
				householdId,
				censusYearId,
			},
		});

		// Get the census year information
		const censusYear = await prisma.censusYear.findUnique({
			where: { id: censusYearId },
		});

		// Get the sacrament types
		const sacramentTypes = await prisma.sacramentType.findMany({
			orderBy: { id: "asc" },
		});

		return NextResponse.json({
			household: household
				? {
						...household,
						firstCensusYearId: household.firstCensusYearId,
						lastCensusYearId: household.lastCensusYearId,
						createdAt: household.createdAt,
						updatedAt: household.updatedAt,
					}
				: null,
			householdHead: householdHeadRelation?.member || null,
			formStatus: formStatus
				? {
						...formStatus,
						householdId: formStatus.householdId,
						censusYearId: formStatus.censusYearId,
						lastUpdated: formStatus.lastUpdated,
						completionDate: formStatus.completionDate,
						createdAt: formStatus.createdAt,
						updatedAt: formStatus.updatedAt,
					}
				: { status: "not_started" },
			censusYear: censusYear
				? {
						...censusYear,
						isActive: censusYear.isActive,
						startDate: censusYear.startDate,
						endDate: censusYear.endDate,
						createdAt: censusYear.createdAt,
						updatedAt: censusYear.updatedAt,
					}
				: null,
			sacramentTypes,
		});
	} catch (error) {
		return NextResponse.json(
			{
				error: tAdmin("failedToFetchHouseholdInformation"),
				details: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 },
		);
	}
}
