/**
 * Minimal tour system types
 */

export type TourType =
  | "add-member"
  | "household-head-edit"
  | "hobby-fields"
  | "occupation-fields"
  | "sacraments"
  | "community-feedback";

export interface TourState {
  isCompleted: boolean;
  currentStep: number;
  hasStarted: boolean;
}

export interface TourTarget {
  primary: string;
  fallback?: string;
  context: string;
}

export interface TourContent {
  description: string;
  title?: string;
}

export interface ActiveTour {
  type: TourType;
  element: HTMLElement;
  selector: string;
  content: TourContent;
  context: string;
}

export interface TourContextValue {
  activeTour: ActiveTour | null;
  setActiveTour: (tour: ActiveTour | null) => void;
  isVisible: boolean;
  setIsVisible: (visible: boolean) => void;
}

export interface UseCensusTourReturn {
  startTour: (tourType?: TourType) => void;
  resetTour: () => void;
  tourState: TourState;
  isTourReady: boolean;
}
