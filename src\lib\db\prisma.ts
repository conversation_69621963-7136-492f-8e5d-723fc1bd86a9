/**
 * Prisma Database Connection Layer
 * Replaces the MySQL connection layer with modern Prisma ORM
 * Environment-aware logging and secure implementation
 */

import { PrismaClient } from "@prisma/client";

// Environment-aware logging
const isDevelopment = process.env.NODE_ENV === "development";

function logError(message: string, error?: any) {
  if (isDevelopment) {
    console.error(`[Prisma] ${message}`, error);
  }
}

// Global Prisma instance for development hot reloading
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

// Create Prisma client with optimised configuration
export const prisma =
  globalForPrisma.prisma ??
  new PrismaClient({
    log: process.env.NODE_ENV === "development" ? ["error", "warn"] : ["error"], // Removed 'query' for performance
    errorFormat: "pretty",
    // Note: datasource configuration is defined in schema.prisma, no need to override here
  });

// Prevent multiple instances in development
if (process.env.NODE_ENV !== "production") {
  globalForPrisma.prisma = prisma;
}

/**
 * Database connection health check with professional timeout handling
 * Uses AbortController for proper cancellation and cleanup
 */
export async function checkDatabaseConnection(): Promise<boolean> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 5000);

  try {
    // Note: Prisma doesn't support AbortSignal yet, but this pattern is ready for when it does
    await prisma.$queryRaw`SELECT 1`;
    clearTimeout(timeoutId);
    return true;
  } catch (error) {
    clearTimeout(timeoutId);
    if (error instanceof Error && error.name === "AbortError") {
      logError("Database connection timeout after 5 seconds");
    } else {
      logError("Database connection failed", error);
    }
    return false;
  }
}

/**
 * Graceful database disconnection
 * Replaces MySQL connection pool cleanup
 */
export async function disconnectDatabase(): Promise<void> {
  try {
    await prisma.$disconnect();
    console.log("Database disconnected successfully");
  } catch (error) {
    console.error("Error disconnecting from database:", error);
  }
}

/**
 * Database transaction wrapper
 * Provides transaction support for complex operations
 */
export async function withTransaction<T>(
  callback: (
    prisma: Omit<
      PrismaClient,
      "$connect" | "$disconnect" | "$on" | "$transaction" | "$use" | "$extends"
    >,
  ) => Promise<T>,
): Promise<T> {
  return await prisma.$transaction(callback);
}

/**
 * Legacy compatibility layer for existing code
 * Gradually replace these with direct Prisma calls
 */
export interface QueryResult<T = unknown> {
  rows: T[];
  rowCount: number;
}

/**
 * Environment validation
 * Ensures DATABASE_URL is properly configured
 */
export function validateDatabaseConfig(): void {
  if (!process.env.DATABASE_URL) {
    throw new Error("DATABASE_URL environment variable is required for PostgreSQL connection");
  }

  if (process.env.DATABASE_URL.includes("mysql://")) {
    throw new Error(
      "DATABASE_URL appears to be a MySQL connection string. Please update to PostgreSQL format.",
    );
  }
}

// Validate configuration on module load
validateDatabaseConfig();

// Export default instance
export default prisma;
