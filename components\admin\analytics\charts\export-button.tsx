'use client';

import { Download, FileSpreadsheet, Image, Loader2 } from 'lucide-react';
import { useTranslations } from 'next-intl';
import type React from 'react';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useMessage } from '@/hooks/useMessage';
import type { ChartData } from '../../../../lib/utils/chart-data-formatter';
import type { ExportFormat, ExportOptions } from './export';
import {
  exportChart,
  getAvailableExportFormats,
  validateExportOptions,
} from './export';

interface ExportButtonProps {
  data: ChartData;
  chartElementRef?: React.RefObject<HTMLElement | null>;
  className?: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  // Props for handling collapsed state
  isCollapsed?: boolean;
  onTemporaryExpand?: () => void;
  onRestoreCollapse?: () => void;
}

const getExportFormatConfig = (t: any) => ({
  csv: {
    icon: FileSpreadsheet,
    label: 'CSV',
    description: t('commaSeparatedValuesForSpreads'),
    requiresChart: false,
  },
  png: {
    icon: Image,
    label: t('pngImage'),
    description: t('highQualityChartImage'),
    requiresChart: true,
  },
});

// Utility function to wait for chart element to be ready for export
const waitForChartReady = (
  chartElementRef: React.RefObject<HTMLElement | null>,
  maxWaitTime = 5000 // 5 second timeout
): Promise<void> => {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();

    const checkChartReady = () => {
      const element = chartElementRef.current;

      // Check if element exists and has measurable dimensions
      if (element && element.offsetWidth > 0 && element.offsetHeight > 0) {
        // Additional check for SVG content (common in chart libraries)
        const svgElements = element.querySelectorAll('svg');
        const hasValidSvg =
          svgElements.length === 0 ||
          Array.from(svgElements).some(
            (svg) =>
              svg.getBoundingClientRect().width > 0 &&
              svg.getBoundingClientRect().height > 0
          );

        if (hasValidSvg) {
          resolve();
          return;
        }
      }

      // Check timeout
      if (Date.now() - startTime > maxWaitTime) {
        reject(new Error(`Chart failed to render within ${maxWaitTime}ms`));
        return;
      }

      // Continue polling
      setTimeout(checkChartReady, 50);
    };

    checkChartReady();
  });
};

export function ExportButton({
  data,
  chartElementRef,
  className,
  variant = 'outline',
  size = 'sm',
  isCollapsed = false,
  onTemporaryExpand,
  onRestoreCollapse,
}: ExportButtonProps) {
  const t = useTranslations('admin');
  const { showSuccess, showError } = useMessage();
  const [isExporting, setIsExporting] = useState(false);
  const [showDialog, setShowDialog] = useState(false);
  const [selectedFormat, setSelectedFormat] = useState<ExportFormat>('csv');
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'csv',
    filename: '',
    includeMetadata: true,
    includeTimestamp: true,
  });

  const availableFormats = getAvailableExportFormats();
  const EXPORT_FORMAT_CONFIG = getExportFormatConfig(t);

  // Generate default filename based on chart data
  const getDefaultFilename = (fileFormat: ExportFormat): string => {
    const chartType = data.type;
    const title =
      data.title?.toLowerCase().replace(/[^a-z0-9]/g, '-') || 'chart';
    const timestamp = new Date().toISOString().split('T')[0];
    return `${title}-${chartType}-${fileFormat}-${timestamp}`;
  };

  const handleQuickExport = async (exportFormat: ExportFormat) => {
    const config = EXPORT_FORMAT_CONFIG[exportFormat];

    setIsExporting(true);
    let wasTemporarilyExpanded = false;

    try {
      // If chart is collapsed and we need the chart element for PNG export
      if (config.requiresChart && isCollapsed && onTemporaryExpand) {
        // Temporarily expand the chart
        onTemporaryExpand();
        wasTemporarilyExpanded = true;

        // Wait until the chartElementRef is non-null & has measurable size
        if (chartElementRef) {
          await waitForChartReady(chartElementRef);
        }
      }

      // Check if chart element is available after potential expansion
      if (config.requiresChart && !chartElementRef?.current) {
        showError('ChartElementNotAvailable');
        return;
      }

      const options: ExportOptions = {
        format: exportFormat,
        filename: getDefaultFilename(exportFormat),
        includeMetadata: true,
        includeTimestamp: true,
      };

      // Export the chart
      await exportChart(data, chartElementRef?.current || null, options);

      showSuccess('chartExported');
    } catch (error) {
      // Provide specific error messages for chart readiness issues
      let _errorMessage = 'Unknown error';
      if (error instanceof Error) {
        if (error.message.includes('Chart failed to render')) {
          _errorMessage =
            'Chart is taking too long to render. Please try again or check if the chart is properly loaded.';
        } else {
          _errorMessage = error.message;
        }
      }

      showError('ChartExportFailed');
    } finally {
      // Restore collapsed state if we temporarily expanded
      if (wasTemporarilyExpanded && onRestoreCollapse) {
        // Small delay to ensure export is complete
        setTimeout(() => {
          onRestoreCollapse();
        }, 200);
      }
      setIsExporting(false);
    }
  };

  const handleCustomExport = () => {
    setSelectedFormat('csv');
    setExportOptions({
      format: 'csv',
      filename: getDefaultFilename('csv'),
      includeMetadata: true,
      includeTimestamp: true,
    });
    // Add delay to ensure dropdown closes before dialogue opens (prevents portal conflicts)
    setTimeout(() => {
      setShowDialog(true);
    }, 150);
  };

  const handleDialogExport = async () => {
    const validation = validateExportOptions(exportOptions);

    if (!validation.isValid) {
      showError('ExportValidationFailed');
      return;
    }

    const config = EXPORT_FORMAT_CONFIG[exportOptions.format];

    setIsExporting(true);
    let wasTemporarilyExpanded = false;

    try {
      // If chart is collapsed and we need the chart element for PNG export
      if (config.requiresChart && isCollapsed && onTemporaryExpand) {
        // Temporarily expand the chart
        onTemporaryExpand();
        wasTemporarilyExpanded = true;

        // Wait until the chartElementRef is non-null & has measurable size
        if (chartElementRef) {
          await waitForChartReady(chartElementRef);
        }
      }

      // Check if chart element is available after potential expansion
      if (config.requiresChart && !chartElementRef?.current) {
        showError('ChartElementNotAvailable');
        return;
      }

      // Export the chart
      await exportChart(data, chartElementRef?.current || null, exportOptions);
      showSuccess('chartExported');
      handleDialogOpenChange(false);
    } catch (error) {
      // Provide specific error messages for chart readiness issues
      let _errorMessage = 'Unknown error';
      if (error instanceof Error) {
        if (error.message.includes('Chart failed to render')) {
          _errorMessage = t('chartFailedToRender');
        } else {
          _errorMessage = error.message;
        }
      }

      showError('ChartExportFailed');
    } finally {
      // Restore collapsed state if we temporarily expanded
      if (wasTemporarilyExpanded && onRestoreCollapse) {
        // Small delay to ensure export is complete
        setTimeout(() => {
          onRestoreCollapse();
        }, 200);
      }
      setIsExporting(false);
    }
  };

  const updateExportOption = (key: keyof ExportOptions, value: unknown) => {
    setExportOptions((prev) => ({ ...prev, [key]: value }));
  };

  const handleFormatChange = (format: ExportFormat) => {
    setSelectedFormat(format);
    updateExportOption('format', format);
    updateExportOption('filename', getDefaultFilename(format));
  };

  // Handle dialogue state change exactly like working dialogues
  const handleDialogOpenChange = (open: boolean) => {
    setShowDialog(open);
    if (!open) {
      // Reset state when dialogue is closed
      setIsExporting(false);
      setSelectedFormat('csv');
      setExportOptions({
        format: 'csv',
        filename: '',
        includeMetadata: true,
        includeTimestamp: true,
      });
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            className={`cursor-pointer ${className || ''}`}
            disabled={isExporting}
            size={size}
            variant={variant}
          >
            {isExporting ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Download className="h-4 w-4" />
            )}
            {size !== 'sm' && <span className="ml-2">Export</span>}
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel>{t('quickExport')}</DropdownMenuLabel>

          {availableFormats.map((format) => {
            const config = EXPORT_FORMAT_CONFIG[format];
            const Icon = config.icon;

            return (
              <DropdownMenuItem
                className="cursor-pointer hover:bg-accent hover:text-accent-foreground"
                disabled={isExporting}
                key={format}
                onClick={() => handleQuickExport(format)}
              >
                <Icon className="mr-2 h-4 w-4" />
                <div>
                  <div className="font-medium">{config.label}</div>
                  <div className="text-slate-500 text-xs">
                    {config.description}
                  </div>
                </div>
              </DropdownMenuItem>
            );
          })}

          <DropdownMenuSeparator />

          <DropdownMenuItem
            className="cursor-pointer hover:bg-accent hover:text-accent-foreground"
            disabled={isExporting}
            onClick={handleCustomExport}
          >
            <Download className="mr-2 h-4 w-4" />
            <div>
              <div className="font-medium">{t('customExport')}</div>
              <div className="text-slate-500 text-xs">
                {t('moreOptionsAndFormats')}
              </div>
            </div>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Custom Export Dialog */}
      <Dialog onOpenChange={handleDialogOpenChange} open={showDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{t('exportChart')}</DialogTitle>
            <DialogDescription>
              Choose export format and customize options
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Format Selection */}
            <div>
              <Label className="font-medium text-sm">{t('exportFormat')}</Label>
              <div className="mt-2 grid grid-cols-1 gap-2">
                {availableFormats.map((format) => {
                  const config = EXPORT_FORMAT_CONFIG[format];
                  const Icon = config.icon;
                  const isDisabled =
                    config.requiresChart && !chartElementRef?.current;

                  return (
                    <button
                      className={`flex items-center rounded-lg border p-3 text-left transition-colors ${
                        selectedFormat === format
                          ? 'border-[#FF6308] bg-[#FF6308]/5'
                          : 'border-slate-200 hover:bg-slate-50 dark:border-slate-700 dark:hover:bg-slate-800'
                      } ${isDisabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}
                      disabled={isDisabled}
                      key={format}
                      onClick={() => handleFormatChange(format)}
                    >
                      <Icon className="mr-3 h-5 w-5 text-slate-600 dark:text-slate-400" />
                      <div>
                        <div className="font-medium text-sm">
                          {config.label}
                        </div>
                        <div className="text-slate-500 text-xs">
                          {config.description}
                        </div>
                        {isDisabled && (
                          <div className="mt-1 text-red-500 text-xs">
                            {t('chartElementRequired')}
                          </div>
                        )}
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Filename */}
            <div>
              <Label className="font-medium text-sm" htmlFor="filename">
                Filename
              </Label>
              <Input
                className="mt-1"
                id="filename"
                onChange={(e) => updateExportOption('filename', e.target.value)}
                placeholder={t('enterFilename')}
                value={exportOptions.filename}
              />
            </div>

            {/* Options */}
            <div className="space-y-3">
              <Label className="font-medium text-sm">Options</Label>

              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={exportOptions.includeMetadata}
                  id="includeMetadata"
                  onCheckedChange={(checked) =>
                    updateExportOption('includeMetadata', checked)
                  }
                />
                <Label className="text-sm" htmlFor="includeMetadata">
                  Include metadata
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={exportOptions.includeTimestamp}
                  id="includeTimestamp"
                  onCheckedChange={(checked) =>
                    updateExportOption('includeTimestamp', checked)
                  }
                />
                <Label className="text-sm" htmlFor="includeTimestamp">
                  Include timestamp
                </Label>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              className="cursor-pointer"
              onClick={() => handleDialogOpenChange(false)}
              variant="outline"
            >
              Cancel
            </Button>
            <Button
              className="cursor-pointer"
              disabled={isExporting}
              onClick={handleDialogExport}
            >
              {isExporting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}

export default ExportButton;
