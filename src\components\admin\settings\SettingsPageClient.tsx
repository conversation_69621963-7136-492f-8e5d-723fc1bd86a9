"use client";

import { Calendar, ChevronDown, Database, Settings, Shield } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { CensusControlsSettings } from "@/components/admin/settings/CensusControlsSettings";
import { CensusYearSettings } from "@/components/admin/settings/CensusYearSettings";
import { ChurchInfoSettings } from "@/components/admin/settings/ChurchInfoSettings";
import { DatabaseManagementSettings } from "@/components/admin/settings/DatabaseManagementSettings";
import { DatabaseServiceSettings } from "@/components/admin/settings/DatabaseServiceSettings";
import { HomepageAnnouncementSettings } from "@/components/admin/settings/HomepageAnnouncementSettings";
import { SecuritySettings } from "@/components/admin/settings/SecuritySettings";
import { SiteUrlSettings } from "@/components/admin/settings/SiteUrlSettings";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export function SettingsPageClient() {
  const t = useTranslations("admin");
  const [activeCategory, setActiveCategory] = useState("general");

  // Define the category types and their properties
  const categories = [
    {
      id: "general",
      label: t("settingsGeneral"),
      icon: Settings,
    },
    {
      id: "census",
      label: t("settingsCensusControls"),
      icon: Calendar,
    },
    {
      id: "security",
      label: t("settingsSecurity"),
      icon: Shield,
    },
    {
      id: "database",
      label: t("settingsDatabase"),
      icon: Database,
    },
  ];

  // Find the active category object
  const currentCategory = categories.find((cat) => cat.id === activeCategory) || categories[0];
  const CategoryIcon = currentCategory.icon;

  return (
    <div className="mx-auto w-full max-w-7xl">
      <div className="sticky top-0 z-10 flex justify-center bg-background">
        <div className="w-full border-b px-4 py-3 sm:w-[95%] md:w-[90%] lg:max-w-4xl">
          <div className="flex items-center justify-between">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  className="flex h-11 cursor-pointer items-center gap-2 border-primary/20 px-4 py-2 shadow-sm transition-all hover:border-primary/40 hover:bg-primary/5"
                  variant="outline"
                >
                  <CategoryIcon className="h-5 w-5 text-primary" />
                  <span className="font-medium text-base">{currentCategory.label}</span>
                  <ChevronDown className="ml-2 h-4 w-4 opacity-70 transition-transform duration-200 group-data-[state=open]:rotate-180" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="start"
                className="fade-in-50 zoom-in-95 w-56 animate-in rounded-md border-primary/10 p-2 shadow-md duration-200"
                sideOffset={8}
              >
                {categories.map((category) => {
                  const Icon = category.icon;
                  const isActive = category.id === activeCategory;

                  return (
                    <DropdownMenuItem
                      className={`mb-1 flex cursor-pointer items-center gap-3 px-3 py-2.5 font-medium text-sm transition-colors last:mb-0 ${
                        isActive
                          ? "bg-primary/10 text-primary"
                          : "hover:bg-muted hover:text-primary"
                      }`}
                      key={category.id}
                      onClick={() => setActiveCategory(category.id)}
                    >
                      <Icon
                        className={`h-4 w-4 ${isActive ? "text-primary" : "text-muted-foreground"}`}
                      />
                      <span>{category.label}</span>
                    </DropdownMenuItem>
                  );
                })}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      <div className="flex justify-center py-8">
        <div className="w-full px-2 sm:w-[95%] sm:px-4 md:w-[90%] lg:max-w-4xl">
          <div
            className="fade-in-50 slide-in-from-bottom-5 animate-in duration-300"
            key={activeCategory}
          >
            {activeCategory === "general" && (
              <div className="flex flex-col gap-8">
                <ChurchInfoSettings />
                <CensusYearSettings />
                <SiteUrlSettings />
                <HomepageAnnouncementSettings />
              </div>
            )}

            {activeCategory === "census" && <CensusControlsSettings />}

            {activeCategory === "security" && <SecuritySettings />}

            {activeCategory === "database" && (
              <div className="flex flex-col gap-8">
                <DatabaseServiceSettings />
                <DatabaseManagementSettings />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
