/**
 * Utility functions for census rate limiting
 * Provides consistent IP-based token generation for census authentication
 */

/**
 * Generate consistent IP-based rate limiting token for census authentication
 * This ensures the same IP address always gets the same token for proper rate limiting accumulation
 */
export function generateCensusRateLimitToken(
	headers: Record<string, string | string[] | undefined> | undefined,
): string {
	// Extract client IP from headers
	let clientIP =
		headers?.["x-forwarded-for"] || headers?.["x-real-ip"] || "unknown-ip";

	// Handle array case for x-forwarded-for
	if (Array.isArray(clientIP)) {
		clientIP = clientIP[0] || "unknown-ip";
	}

	// Ensure the IP is valid and not empty
	if (
		!clientIP ||
		typeof clientIP !== "string" ||
		clientIP.trim().length === 0
	) {
		clientIP = "unknown-ip";
	}

	// Create consistent IP-based token for rate limiting
	let sessionToken = `census-ip-${clientIP}`;

	// Ensure the token meets minimum length requirement (10 characters)
	if (sessionToken.length < 10) {
		sessionToken = `census-ip-fallback-${clientIP}`;
	}

	return sessionToken;
}

/**
 * Generate census rate limiting token from NextRequest object
 */
export function generateCensusRateLimitTokenFromRequest(request: {
	headers: { get: (name: string) => string | null };
}): string {
	const headers: Record<string, string | string[] | undefined> = {
		"x-forwarded-for": request.headers.get("x-forwarded-for") || undefined,
		"x-real-ip": request.headers.get("x-real-ip") || undefined,
	};

	return generateCensusRateLimitToken(headers);
}
