import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { censusAuthOptions } from "@/lib/census-auth/census-auth-options";
import { prisma } from "@/lib/db/prisma";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";

// Interface for suburb data (used in the API response)
// interface _SuburbData {
//   id: number;
//   display_name: string;
//   suburb_name: string;
//   state_code: string;
// }

/**
 * GET /api/census/suburbs/all
 * Load all suburbs at once for frontend filtering
 * Optimized for high concurrency (1000+ users)
 * Protected by census authentication middleware
 */
export async function GET(_request: Request) {
	// Get locale from centralized utility at the beginning
	const locale = await getLocaleFromCookies();
	const tErrors = await getTranslations({ locale, namespace: "errors" });
	const tAdmin = await getTranslations({ locale, namespace: "admin" });

	try {
		// Authentication is handled by middleware, but we can still check session for additional validation
		const session = await getServerSession(censusAuthOptions);

		if (!session) {
			return NextResponse.json(
				{ error: tErrors("unauthorized") },
				{ status: 401 },
			);
		}

		// Load all suburbs with Prisma
		const suburbs = await prisma.suburb.findMany({
			select: {
				id: true,
				displayName: true,
				suburbName: true,
				stateCode: true,
			},
			orderBy: {
				displayName: "asc",
			},
		});

		// Transform to match expected interface
		const suburbData = suburbs.map((suburb) => ({
			id: suburb.id,
			displayName: suburb.displayName,
			suburbName: suburb.suburbName,
			stateCode: suburb.stateCode,
		}));

		// Log load activity in development
		if (process.env.NODE_ENV === "development") {
		}

		// Return with cache headers for optimization
		const response = NextResponse.json({
			success: true,
			suburbs: suburbData,
			count: suburbData.length,
			timestamp: new Date().toISOString(),
		});

		// Add cache headers for performance (cache for 1 hour)
		// Note: Cache-Control with authentication might be handled by CDN/proxy
		response.headers.set("Cache-Control", "private, max-age=3600");
		response.headers.set("Content-Type", "application/json");

		// Add compression hint (actual compression handled by Next.js/server)
		response.headers.set("Vary", "Accept-Encoding");

		return response;
	} catch (error) {
		// Return user-friendly error message
		return NextResponse.json(
			{
				error: tAdmin("failedToLoadSuburbs"),
				details:
					process.env.NODE_ENV === "development"
						? error instanceof Error
							? error.message
							: "Unknown error"
						: "Please try again later",
			},
			{ status: 500 },
		);
	}
}
