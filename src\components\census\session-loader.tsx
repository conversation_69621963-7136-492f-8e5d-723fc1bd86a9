"use client";

import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { useCensusAuth } from "@/hooks/useCensusAuth";

interface SessionLoaderProps {
  children: React.ReactNode;
}

/**
 * SessionLoader component
 *
 * This component ensures that the session data is fully loaded before rendering its children.
 * It shows a loading indicator while the session data is being loaded.
 */
export function SessionLoader({ children }: SessionLoaderProps) {
  const { isLoading, session, status } = useCensusAuth();
  const [isReady, setIsReady] = useState(false);
  const tNavigation = useTranslations("navigation");

  // Ensure session is fully loaded before rendering children
  useEffect(() => {
    if (status === "authenticated" && session) {
      console.log("Session data fully loaded:", session.user.name);

      // Add a small delay to ensure all components have access to the session data
      const timer = setTimeout(() => {
        setIsReady(true);
      }, 200);

      return () => clearTimeout(timer);
    }
  }, [session, status]);

  if (isLoading || !isReady) {
    return (
      <div className="flex min-h-[200px] items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
          <p className="text-muted-foreground text-sm">{tNavigation("loadingHouseholdData")}</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
