import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { authOptions } from "@/lib/auth/auth-options";
import { prisma } from "@/lib/db/prisma";
import { getErrorMessage } from "@/lib/utils/error-handling";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";

/**
 * POST /api/admin/database/cleanup
 *
 * Clean up old database records to prevent database growth
 * Only accessible to admin users
 */
export async function POST(request: Request) {
  // Check authentication
  const session = await getServerSession(authOptions);

  // Require admin authentication
  if (!session || session.user.role !== "admin") {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Get locale for translations
  const locale = await getLocaleFromCookies();
  const t = await getTranslations({ locale, namespace: "admin" });

  try {
    interface CleanupResult {
      deleted?: number;
      success: boolean;
      error?: string;
      message?: string;
    }

    const results: Record<string, CleanupResult> = {};

    // 1. Clean up expired sessions (older than 7 days)
    try {
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      const deletedSessions = await prisma.userSession.deleteMany({
        where: {
          expiresAt: {
            lt: sevenDaysAgo,
          },
        },
      });

      results.sessions = {
        deleted: deletedSessions.count,
        success: true,
      };
    } catch (sessionError) {
      results.sessions = {
        error: getErrorMessage(sessionError),
        success: false,
      };
    }

    // 2. Clean up old audit logs (audit_logs table not implemented yet)
    results.auditLogs = {
      deleted: 0,
      success: true,
      message: t("auditLogsNotImplemented"),
    };

    // 3. Clean up old inactive sessions (older than 30 days)
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const deletedInactiveSessions = await prisma.userSession.deleteMany({
        where: {
          lastActivity: {
            lt: thirtyDaysAgo,
          },
        },
      });

      results.inactiveSessions = {
        deleted: deletedInactiveSessions.count,
        success: true,
      };
    } catch (inactiveError) {
      results.inactiveSessions = {
        error: getErrorMessage(inactiveError),
        success: false,
      };
    }

    // Get IP address for audit log
    const _ipAddress = request.headers.get("x-forwarded-for") || "unknown";

    // Log the action to console (audit_logs table not implemented yet)
    if (process.env.NODE_ENV === "development") {
    }

    return NextResponse.json({
      success: true,
      message: t("databaseCleanupCompleted"),
      details: results,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    // Log errors only in development for security
    if (process.env.NODE_ENV === "development") {
    }
    return NextResponse.json(
      { error: t("databaseCleanupFailed"), details: getErrorMessage(error) },
      { status: 500 },
    );
  }
}
