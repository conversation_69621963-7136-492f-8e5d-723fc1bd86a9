import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { censusAuthOptions } from "@/lib/census-auth/census-auth-options";
import { prisma } from "@/lib/db/prisma";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";

/**
 * API route to check the census session status
 * This is used by client components to determine if the user is authenticated
 */
export async function GET(_request: Request) {
	// Get locale from centralized utility at the beginning
	const locale = await getLocaleFromCookies();
	const tErrors = await getTranslations({ locale, namespace: "errors" });

	try {
		// Get the session
		const session = await getServerSession(censusAuthOptions);

		// If no session, return unauthenticated
		if (!session) {
			return NextResponse.json({
				isAuthenticated: false,
				session: null,
			});
		}

		// Additional validation: check if the unique code still exists
		try {
			const uniqueCode = await prisma.uniqueCode.findUnique({
				where: { id: Number.parseInt(session.user.id, 10) },
				select: { householdId: true },
			});

			// FIXED: Only treat as deleted if the unique code doesn't exist
			// New codes legitimately have householdId: null and should not be treated as deleted
			if (!uniqueCode) {
				return NextResponse.json(
					{
						isAuthenticated: false,
						error: tErrors("accountDeleted"),
						message: tErrors("householdRemovedByAdmin"),
					},
					{ status: 403 },
				);
			}

			// Note: We don't check householdId === null here because:
			// 1. New codes legitimately have null householdId
			// 2. The JWT callback already handles household deletion detection
			// 3. This API should only check if the unique code itself exists
		} catch (_dbError) {
			// Continue with existing session if database check fails
		}

		// Return the session status
		return NextResponse.json({
			isAuthenticated: true,
			session: {
				user: {
					id: session.user.id,
					name: session.user.name,
					role: session.user.role,
					censusYearId: session.user.censusYearId,
					householdId: session.user.householdId,
					// Don't include sensitive information like code
				},
			},
		});
	} catch (_error) {
		if (process.env.NODE_ENV === "development") {
		}

		// Return a default response
		return NextResponse.json(
			{
				isAuthenticated: false,
				error: tErrors("authenticationFailed"),
			},
			{ status: 500 },
		);
	}
}
