import type { Metadata } from "next";
import { getTranslations } from "next-intl/server";
import { requireAdmin } from "@/lib/auth/auth-utils";
import { getCensusYears } from "@/lib/db/census-years";
import { getMembersWithDetails } from "@/lib/db/members";
import { MembersClient } from "./members-client";

export async function generateMetadata(): Promise<Metadata> {
	const t = await getTranslations("metadata");

	return {
		title: t("membersManagementTitle"),
		description: t("membersManagementDescription"),
	};
}

export default async function MembersPage() {
	// Server-side authentication check
	await requireAdmin();

	// Fetch census years for filters
	const censusYears = await getCensusYears();

	// Fetch initial member data directly from database
	// This provides immediate data without API calls and cache delays
	const pageSize = 20;
	const initialMembersData = await getMembersWithDetails({
		page: 1,
		pageSize,
		searchTerm: "",
		sortBy: "memberId",
		sortOrder: "asc",
	});

	return (
		<MembersClient
			censusYears={censusYears}
			initialMembers={initialMembersData.members}
			initialPageSize={pageSize}
			initialTotal={initialMembersData.total}
		/>
	);
}
