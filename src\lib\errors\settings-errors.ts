/**
 * Settings error translation mappings
 * Maps error codes to translation keys in the 'errors' namespace
 * Used by useMessage hook and server-side translation routes
 */
export const settingsErrorKeys: Record<string, string> = {
  // Church information errors
  InvalidChurchName: 'invalidChurchName',
  InvalidEmail: 'invalidEmail',
  InvalidContactNumber: 'invalidContactNumber',
  InvalidAddress: 'invalidAddress',

  // Settings update errors
  UpdateFailed: 'updateFailed',

  // Network and loading errors
  requestTimeout: 'requestTimeout',
  householdSearchFailed: 'householdSearchFailed',
  AiResponseFailed: 'aiResponseFailed',
  failedToLoadCensusControlsSettings: 'failedToLoadCensusControlsSettings',
  failedToLoadRateLimitSettings: 'failedToLoadRateLimitSettings',
  failedToLoadChurchInfo: 'failedToLoadChurchInfo',
  failedToLoadCensusYearSettings: 'failedToLoadCensusYearSettings',

  // Member management errors
  failedToUpdateMember: 'failedToUpdateMember',
  failedToDeleteMember: 'failedToDeleteMember',

  // Site URL errors
  errorOccurredUpdatingSiteUrl: 'errorOccurredUpdatingSiteUrl',

  // Unique code errors
  cannotDeleteAssignedCodesWithList: 'cannotDeleteAssignedCodesWithList',
  failedToDeleteUniqueCodes: 'failedToDeleteUniqueCodes',
  failedToPrepareCodesForPrinting: 'failedToPrepareCodesForPrinting',
  anErrorOccurredWhileDeletingUniqueCodes:
    'anErrorOccurredWhileDeletingUniqueCodes',

  // Default error message
  default: 'settingsError',
};
