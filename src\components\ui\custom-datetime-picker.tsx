"use client";

/**
 * This file provides a modular date-time picker system with three main components:
 *
 * 1. Calendar - A standalone calendar component for date selection
 *    Import from: import { Calendar } from "@/components/ui/calendar"
 *
 * 2. TimePicker - A standalone time picker component
 *    Import from: import { TimePicker } from "@/components/ui/time-picker"
 *
 * 3. CustomDateTimePicker - A component that combines both Calendar and TimePicker
 *    Import from: import { CustomDateTimePicker } from "@/components/ui/custom-datetime-picker"
 *
 * Usage examples:
 *
 * // For census participants (date only):
 * <Calendar date={date} onDateChange={setDate} />
 *
 * // For admin users (full date and time):
 * <CustomDateTimePicker date={date} setDate={setDate} />
 *
 * // For custom time-only selection:
 * <TimePicker date={date} onTimeChange={handleTimeChange} showSeconds={true} />
 */

import { CalendarIcon } from "lucide-react";
import * as React from "react";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import { TimePicker } from "@/components/ui/time-picker";
import { cn } from "@/lib/utils";
import { formatDate } from "@/lib/utils/date-time";

interface CustomDateTimePickerProps {
	date: Date | null;
	setDate: (date: Date | null) => void;
	disabled?: boolean;
	className?: string;
	placeholderText?: string;
	showSeconds?: boolean;
	showTime?: boolean;
}

export function CustomDateTimePicker({
	date,
	setDate,
	disabled = false,
	className,
	placeholderText = "Select date and time",
	showSeconds = false,
	showTime = true,
}: CustomDateTimePickerProps) {
	const [isOpen, setIsOpen] = React.useState(false);
	const [temporaryDate, setTemporaryDate] = React.useState<Date | null>(null);
	const [calendarView, setCalendarView] = React.useState<
		"day" | "month" | "year"
	>("day");
	const [currentMonth, setCurrentMonth] = React.useState<Date>(new Date());

	// Update temporaryDate when date changes (client-side only)
	React.useEffect(() => {
		setTemporaryDate(date);
		if (date) {
			setCurrentMonth(date);
		}
	}, [date]);

	// Handle popover close - update the actual date ONLY if isOpen changes from true to false
	React.useEffect(() => {
		// Only run when the popover closes
		if (isOpen === false) {
			// Update the date if it has changed
			if (temporaryDate !== date) {
				// Use a timeout to prevent state updates during render
				const timer = setTimeout(() => {
					setDate(temporaryDate);
				}, 0);
				return () => clearTimeout(timer);
			}
		}
	}, [isOpen, temporaryDate, date, setDate]);

	// Handle date selection from Calendar component
	const handleDateChange = (newDate: Date) => {
		// If we already have a temporaryDate, preserve its time
		if (temporaryDate) {
			const updatedDate = new Date(newDate);
			updatedDate.setHours(temporaryDate.getHours());
			updatedDate.setMinutes(temporaryDate.getMinutes());
			updatedDate.setSeconds(temporaryDate.getSeconds());
			setTemporaryDate(updatedDate);
		} else {
			// Otherwise just use the new date
			setTemporaryDate(newDate);
		}

		// Update current month when date changes
		setCurrentMonth(new Date(newDate));
	};

	// Handle time selection from TimePicker component
	const handleTimeChange = (newDate: Date) => {
		setTemporaryDate(newDate);
	};

	// Check if the current month being displayed is the current month
	const isCurrentMonthToday = () => {
		const today = new Date();
		return (
			currentMonth.getMonth() === today.getMonth() &&
			currentMonth.getFullYear() === today.getFullYear()
		);
	};

	// Handle the "Done" button click
	const handleDone = () => {
		setDate(temporaryDate);
		setIsOpen(false);
	};

	// Apply temporary date or date for display
	const displayDate = temporaryDate || date;

	// Format the date for display
	const formattedDate = displayDate
		? showTime
			? formatDate(
					displayDate,
					showSeconds ? "MMMM d, yyyy h:mm:ss aa" : "MMMM d, yyyy h:mm aa",
				)
			: formatDate(displayDate, "MMMM d, yyyy") // Only show date if showTime is false
		: "";

	return (
		<div className={cn("relative", className)}>
			<Popover onOpenChange={setIsOpen} open={isOpen}>
				<PopoverTrigger asChild>
					<Button
						className={cn(
							"w-full cursor-pointer justify-start text-left font-normal",
							!date && "text-muted-foreground",
							"h-10",
						)}
						disabled={disabled}
						variant="outline"
					>
						<CalendarIcon className="mr-2 h-4 w-4 flex-shrink-0" />
						<span className="truncate">{formattedDate || placeholderText}</span>
					</Button>
				</PopoverTrigger>
				<PopoverContent
					align="start"
					className="w-auto p-0"
					onWheel={(e) => {
						// Allow wheel events to propagate to calendar for proper scrolling
						e.stopPropagation();
					}}
				>
					<div className="flex flex-col md:flex-row">
						{/* Calendar Component */}
						<Calendar
							className="md:border-r"
							date={temporaryDate}
							onDateChange={handleDateChange}
							onDone={handleDone} // Hide the footer in Calendar
							onMonthChange={setCurrentMonth} // Hide the Today button in Calendar
							onToday={() => {
								const today = new Date();
								// Preserve time if we already have a date
								if (temporaryDate) {
									today.setHours(temporaryDate.getHours());
									today.setMinutes(temporaryDate.getMinutes());
									today.setSeconds(temporaryDate.getSeconds());
								}
								setTemporaryDate(today);
								setCurrentMonth(today); // Update current month to today
								setCalendarView("day"); // Always return to day view when Today button is clicked
							}} // This is not a standalone calendar
							onViewChange={setCalendarView}
							showFooter={false} // Control the view from outside
							showTodayButton={false} // Track view changes
							standalone={false} // Track month changes
							view={calendarView} // Handle Today button click
						/>

						{/* Time Picker Component (only if showTime is true) */}
						{showTime && temporaryDate && (
							<TimePicker
								date={temporaryDate}
								onTimeChange={handleTimeChange}
								showSeconds={showSeconds}
							/>
						)}
					</div>

					{/* Footer with Today and Done buttons */}
					<div className="flex justify-between border-t p-3">
						{/* Today button - show in month/year views OR when not viewing current month */}
						{calendarView === "month" ||
						calendarView === "year" ||
						!isCurrentMonthToday() ? (
							<Button
								className="cursor-pointer"
								onClick={() => {
									const today = new Date();
									// Preserve time if we already have a date
									if (temporaryDate) {
										today.setHours(temporaryDate.getHours());
										today.setMinutes(temporaryDate.getMinutes());
										today.setSeconds(temporaryDate.getSeconds());
									}
									setTemporaryDate(today);
									setCurrentMonth(today); // Update current month to today
									setCalendarView("day"); // Always return to day view when Today button is clicked
								}}
								size="sm"
								variant="ghost"
							>
								Today
							</Button>
						) : (
							<div /> /* Empty div to maintain layout when Today button is not shown */
						)}

						{/* Done button */}
						<Button className="cursor-pointer" onClick={handleDone} size="sm">
							Done
						</Button>
					</div>
				</PopoverContent>
			</Popover>
		</div>
	);
}

/**
 * Export the modular components for individual use
 *
 * These exports allow you to import all components from a single file:
 *
 * import {
 *   CustomDateTimePicker,  // Combined date and time picker
 *   Calendar,              // Standalone calendar
 *   TimePicker            // Standalone time picker
 * } from "@/components/ui/custom-datetime-picker"
 */
export { Calendar } from "@/components/ui/calendar";
export { TimePicker } from "@/components/ui/time-picker";
