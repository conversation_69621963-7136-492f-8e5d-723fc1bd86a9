"use client";

import { <PERSON><PERSON><PERSON>, CalendarOff, HelpCircle, Info } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import { HomepageAnnouncement } from "@/components/home/<USER>";
import { Button } from "@/components/ui/button";
import type { CensusData } from "@/lib/homepage/placeholder-processor";

interface CensusClosedMessageProps {
  censusData?: CensusData;
  locale?: string;
}

export function CensusClosedMessage({
  censusData,
  locale = "en-AU",
}: CensusClosedMessageProps = {}) {
  const router = useRouter();

  // Translation hooks
  const t = useTranslations("census");
  const tNav = useTranslations("navigation");

  return (
    <div className="mx-auto w-full max-w-md">
      <div className="flex flex-col items-center space-y-6 p-8">
        {/* Icon with simple background like the 500 error page */}
        <div className="mb-2 inline-flex items-center justify-center rounded-full bg-destructive/10 p-4 text-destructive">
          <CalendarOff className="h-10 w-10" />
        </div>

        {/* Main content */}
        <div className="space-y-2 text-center">
          <h1 className="font-bold text-3xl tracking-tight">{t("censusCurrentlyClosed")}</h1>

          {/* Inline announcement between title and Bible quote */}
          {censusData && <HomepageAnnouncement censusData={censusData} locale={locale} />}
        </div>

        {/* Bible Quote */}
        <div className="w-full max-w-sm">
          <div className="flex items-start gap-3">
            <BookOpen className="mt-1 h-5 w-5 flex-shrink-0 text-primary" />
            <div>
              <p className="mb-1 text-base italic">
                &quot;For everything there is a season, and a time for every matter under
                heaven.&quot;
              </p>
              <p className="text-right text-muted-foreground text-sm">— Ecclesiastes 3:1</p>
            </div>
          </div>
        </div>

        {/* Divider with icon */}
        <div className="flex w-full items-center gap-4 px-6">
          <div className="h-px flex-1 bg-border" />
          <Info className="h-4 w-4 text-muted-foreground" />
          <div className="h-px flex-1 bg-border" />
        </div>

        {/* Contact options */}
        <div className="w-full space-y-4">
          <Button
            className="flex w-full items-center justify-center gap-2"
            onClick={() => router.push("/help")}
          >
            <HelpCircle className="h-5 w-5" />
            <span className="font-medium">
              {tNav("help")} & {tNav("faq")}
            </span>
          </Button>

          <p className="text-center text-muted-foreground text-sm">{t("checkBackNextPeriod")}</p>

          <div className="flex justify-center">
            <Link
              className="inline-flex items-center gap-2 text-muted-foreground text-sm transition-colors hover:text-primary"
              href="/admin/login"
            >
              <span className="text-muted-foreground underline underline-offset-4 hover:text-primary">
                {tNav("adminLogin")}
              </span>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
