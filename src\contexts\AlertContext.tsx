"use client";

import { createContext, useContext, useRef } from "react";
import { toast } from "sonner";

type AlertType = "success" | "error" | "info" | "warning";

interface AlertContextType {
  showAlert: (
    type: AlertType,
    message: string,
    options?: AlertOptions,
  ) => ReturnType<typeof toast> | undefined;
}

interface AlertOptions {
  // Set to true to bypass deduplication for critical messages
  bypassDeduplication?: boolean;
  // Common Sonner toast options - simplified to avoid type conflicts
  [key: string]: unknown;
}

// Configuration options that could be moved to environment variables
const DEDUPLICATION_WINDOW_MS = 2000; // 2 seconds
const CLEANUP_WINDOW_MS = 10_000; // 10 seconds

interface RecentAlert {
  type: AlertType;
  message: string;
  timestamp: number;
}

const AlertContext = createContext<AlertContextType | undefined>(undefined);

export function AlertProvider({ children }: { children: React.ReactNode }) {
  // Track recent alerts to prevent duplicates
  const recentAlerts = useRef<RecentAlert[]>([]);

  const showAlert = (type: AlertType, message: string, options?: AlertOptions) => {
    const now = Date.now();

    // Clean up old alerts (older than cleanup window)
    recentAlerts.current = recentAlerts.current.filter(
      (alert) => now - alert.timestamp < CLEANUP_WINDOW_MS,
    );

    // Extract bypassDeduplication from options and create a new options object without it
    const { bypassDeduplication, ...toastOptions } = options || {};

    // Check if this is a duplicate (same type and message within deduplication window)
    // Skip check if bypassDeduplication is true
    const isDuplicate =
      !bypassDeduplication &&
      recentAlerts.current.some(
        (alert) =>
          alert.type === type &&
          alert.message === message &&
          now - alert.timestamp < DEDUPLICATION_WINDOW_MS,
      );

    if (!isDuplicate) {
      // Add to recent alerts
      recentAlerts.current.push({ type, message, timestamp: now });

      // Show the toast using sonner
      switch (type) {
        case "success":
          return toast.success(message, toastOptions);
        case "error":
          return toast.error(message, toastOptions);
        case "info":
          return toast.info(message, toastOptions);
        case "warning":
          return toast.warning(message, toastOptions);
        default:
          return toast(message, toastOptions);
      }
    }

    return;
  };

  return <AlertContext.Provider value={{ showAlert }}>{children}</AlertContext.Provider>;
}

export function useAlert() {
  const context = useContext(AlertContext);
  if (context === undefined) {
    throw new Error("useAlert must be used within an AlertProvider");
  }
  return context;
}
