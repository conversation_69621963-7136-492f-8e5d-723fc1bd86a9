"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Info, Refresh<PERSON><PERSON>, Shield } from "lucide-react";
import Image from "next/image";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod/v4";
import { FormFieldWithTooltip } from "@/components/admin/settings/FormFieldWithTooltip";
import { RateLimitSettings } from "@/components/admin/settings/RateLimitSettings";
import { SettingsCard } from "@/components/admin/settings/SettingsCard";
import { OTPInputField } from "@/components/forms/common/OTPInputField";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { useFormSubmit } from "@/hooks/useFormSubmit";
import { useMessage } from "@/hooks/useMessage";
import { zod<PERSON>esol<PERSON> } from "@/lib/utils/zod-resolver-compat";
import {
	type ClientPasswordChangeFormValues,
	createClientPasswordChangeSchema,
} from "@/lib/validation/client/auth-client";

// Function to create 2FA verification schema with translations
function createTwoFactorVerificationSchema(tValidation: any) {
	return z.object({
		enabled: z.boolean(),
		verificationCode: z
			.string()
			.optional()
			.refine(
				(val) => {
					// Only validate if verificationCode is provided
					if (val && val.length > 0) {
						return /^\d{6}$/.test(val);
					}
					return true;
				},
				{
					error: tValidation("verificationCodeMustBe6Digits"),
				},
			),
	});
}

type TwoFactorFormValues = {
	enabled: boolean;
	verificationCode?: string;
};

// Define the setup mode type
type SetupMode = "not_started" | "in_progress" | "completed";

export function SecuritySettings() {
	const t = useTranslations("admin");
	const tValidation = useTranslations("validation");
	const tCommon = useTranslations("common");
	const { showSuccess, showError, showWarning, showDirect } = useMessage();

	// Create client-side validation schema with translations
	const passwordChangeSchema = createClientPasswordChangeSchema(tValidation);

	// State for 2FA setup
	const [setupMode, setSetupMode] = useState<SetupMode>("not_started");
	const [qrCodeUrl, setQrCodeUrl] = useState<string>("");
	const [backupCodes, setBackupCodes] = useState<string[]>([]);
	const [isLoading, setIsLoading] = useState(false);
	const [showBackupCodes, setShowBackupCodes] = useState(false);

	// Password change form
	const passwordForm = useForm<ClientPasswordChangeFormValues>({
		resolver: zodResolver(passwordChangeSchema),
		defaultValues: {
			currentPassword: "",
			newPassword: "",
			confirmPassword: "",
		},
	});

	// Create translated schema
	const translatedTwoFactorSchema =
		createTwoFactorVerificationSchema(tValidation);

	// 2FA form
	const twoFactorForm = useForm<TwoFactorFormValues>({
		resolver: zodResolver(translatedTwoFactorSchema),
		defaultValues: {
			enabled: false,
			verificationCode: "",
		},
	});

	// Watch 2FA enabled state
	const twoFactorEnabled = twoFactorForm.watch("enabled");

	// Fetch 2FA status on component mount
	useEffect(() => {
		const fetchTwoFactorStatus = async () => {
			try {
				const response = await fetch("/api/settings/two-factor");

				if (!response.ok) {
					if (response.status === 401) {
						showWarning("needAdminLoginToView2FASettings");
					} else {
						showError("failedToLoad2FASettings");
					}
					return;
				}

				const data = await response.json();

				// Update form with current 2FA status
				twoFactorForm.setValue("enabled", data.enabled);

				// If 2FA is enabled, set setup mode to completed
				if (data.enabled) {
					setSetupMode("completed");
				}
			} catch (error) {
				console.error("Error fetching 2FA status:", error);
				showError("failedToLoad2FASettings");
			}
		};

		// Fetch 2FA status
		fetchTwoFactorStatus();
	}, [twoFactorForm, t, showError, showWarning]); // showError, showWarning are stable due to useCallback memoization

	// Use our custom hook for password change submission
	const {
		handleSubmit: submitPasswordChange,
		isSubmitting: isChangingPassword,
	} = useFormSubmit<ClientPasswordChangeFormValues>({
		onSubmit: async (data) => {
			try {
				const response = await fetch("/api/settings/change-password", {
					method: "PUT",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify(data),
				});

				const responseData = await response.json();

				if (!response.ok) {
					// If unauthorized, return a specific message
					if (response.status === 401) {
						return {
							success: false,
							message: t("needAdminLoginToChangePassword"),
						};
					}

					// If current password is incorrect
					if (
						response.status === 400 &&
						responseData.error === "Current password is incorrect"
					) {
						return {
							success: false,
							message: t("currentPasswordIncorrect"),
						};
					}

					// Handle validation errors
					if (responseData.details) {
						return {
							success: false,
							message: tValidation("checkFormErrors"),
						};
					}

					throw new Error(responseData.error || t("failedToChangePassword"));
				}

				// Success - use centralized alert system
				showSuccess("passwordChanged");
				return {
					success: true,
					suppressAlert: true,
				};
			} catch (error) {
				console.error("Error changing password:", error);
				showError("UpdateFailed", "settings");
				return {
					success: false,
					suppressAlert: true,
				};
			}
		},
		onSuccess: () => {
			passwordForm.reset();
		},
	});

	// Handle starting 2FA setup
	const handleStartSetup = async () => {
		setIsLoading(true);
		try {
			const response = await fetch("/api/settings/two-factor/setup", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
			});

			if (!response.ok) {
				showError("TwoFactorSetupFailed");
				return;
			}

			const data = await response.json();

			// Set QR code URL and update setup mode
			setQrCodeUrl(data.qrCode);
			setSetupMode("in_progress");

			// Show success message
			showSuccess("qrCodeGenerated");
		} catch (error) {
			console.error("Error starting 2FA setup:", error);
			showError("TwoFactorSetupFailed");
		} finally {
			setIsLoading(false);
		}
	};

	// Handle verifying 2FA
	const handleVerify2FA = async (verificationCode: string) => {
		if (!verificationCode || verificationCode.trim() === "") {
			showError("VerificationCodeRequired");
			return false;
		}

		setIsLoading(true);
		try {
			const response = await fetch("/api/settings/two-factor/verify", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ token: verificationCode }),
			});

			if (!response.ok) {
				showError("TwoFactorVerificationFailed");
				return false;
			}

			const data = await response.json();

			// Update setup mode and store backup codes
			setSetupMode("completed");
			setBackupCodes(data.backupCodes);
			setShowBackupCodes(true);

			// Show success message
			showSuccess("twoFactorEnabled");
		} catch (error) {
			console.error("Error verifying 2FA:", error);
			showError("TwoFactorVerificationFailed");
			return false;
		} finally {
			setIsLoading(false);
		}
		return true;
	};

	// Handle disabling 2FA
	const handleDisable2FA = async () => {
		setIsLoading(true);
		try {
			const response = await fetch("/api/settings/two-factor", {
				method: "DELETE",
				headers: {
					"Content-Type": "application/json",
				},
			});

			if (!response.ok) {
				showError("TwoFactorDisableFailed");
				return;
			}

			// Reset state
			setSetupMode("not_started");
			setQrCodeUrl("");
			setBackupCodes([]);
			setShowBackupCodes(false);
			twoFactorForm.setValue("enabled", false);
			twoFactorForm.setValue("verificationCode", "");

			// Show success message
			showSuccess("twoFactorDisabled");
		} catch (error) {
			console.error("Error disabling 2FA:", error);
			showError("TwoFactorDisableFailed");
		} finally {
			setIsLoading(false);
		}
	};

	// Handle generating new backup codes
	const handleGenerateNewBackupCodes = async () => {
		setIsLoading(true);
		try {
			const response = await fetch("/api/settings/two-factor/backup-codes", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
			});

			if (!response.ok) {
				showError("BackupCodeGenerationFailed");
				return;
			}

			const data = await response.json();

			// Update backup codes
			setBackupCodes(data.backupCodes);
			setShowBackupCodes(true);

			// Show success message
			showSuccess("backupCodesGenerated");
		} catch (error) {
			console.error("Error generating backup codes:", error);
			showError("BackupCodeGenerationFailed");
		} finally {
			setIsLoading(false);
		}
	};

	// Handle copying backup codes to clipboard
	const handleCopyBackupCodes = () => {
		navigator.clipboard
			.writeText(backupCodes.join("\n"))
			.then(() => showSuccess("backupCodesCopied"))
			.catch(() => showError("BackupCodeCopyFailed"));
	};

	// Use our custom hook for 2FA form submission
	const { handleSubmit: submitTwoFactor, isSubmitting: isSubmittingTwoFactor } =
		useFormSubmit<TwoFactorFormValues>({
			onSubmit: async (data) => {
				// If 2FA is being disabled
				if (!data.enabled && setupMode === "completed") {
					await handleDisable2FA();
					return {
						success: true,
						suppressAlert: true,
					};
				}

				// If 2FA is being enabled and we're in setup mode
				if (data.enabled && setupMode === "in_progress") {
					// Check if verification code is provided
					if (!data.verificationCode || data.verificationCode.trim() === "") {
						return {
							success: false,
							message: tCommon("pleaseEnterTheVerificationCode"),
						};
					}

					const success = await handleVerify2FA(data.verificationCode);
					return {
						success,
						suppressAlert: true,
					};
				}

				// If 2FA is being enabled but setup hasn't started
				if (data.enabled && setupMode === "not_started") {
					await handleStartSetup();
					return {
						success: true,
						suppressAlert: true,
					};
				}

				// Success - use centralized alert system
				showSuccess("twoFactorSettingsUpdated");
				return {
					success: true,
					suppressAlert: true,
				};
			},
		});

	// Handle 2FA toggle
	const handleTwoFactorToggle = (checked: boolean) => {
		twoFactorForm.setValue("enabled", checked, { shouldValidate: true });

		// If toggling off, disable 2FA
		if (!checked && setupMode === "completed") {
			handleDisable2FA();
		}

		// If toggling on, start setup
		if (checked && setupMode === "not_started") {
			handleStartSetup();
		}
	};

	return (
		<div className="space-y-6">
			{/* Rate Limiting Settings Card */}
			<RateLimitSettings />

			{/* Password Change Card */}
			<SettingsCard
				description={t("updateAccountPassword")}
				form={passwordForm}
				isSubmitting={isChangingPassword}
				onFormSubmit={submitPasswordChange}
				saveButtonText={t("updatePassword")}
				title={t("changePassword")}
			>
				<div className="space-y-6">
					<FormFieldWithTooltip
						error={passwordForm.formState.errors.currentPassword}
						id="currentPassword"
						label={t("currentPassword")}
						placeholder={t("enterCurrentPassword")}
						register={passwordForm.register}
						required
						tooltip={t("enterCurrentPasswordToVerify")}
						type="password"
					/>

					<FormFieldWithTooltip
						error={passwordForm.formState.errors.newPassword}
						id="newPassword"
						label={t("newPassword")}
						placeholder={t("enterNewPassword")}
						register={passwordForm.register}
						required
						tooltip={t("passwordMustBeAtLeast8Characters")}
						type="password"
					/>

					<FormFieldWithTooltip
						error={passwordForm.formState.errors.confirmPassword}
						id="confirmPassword"
						label={t("confirmNewPassword")}
						placeholder={t("confirmNewPassword")}
						register={passwordForm.register}
						required
						tooltip={t("reenterNewPasswordToConfirm")}
						type="password"
					/>
				</div>
			</SettingsCard>

			{/* Two-Factor Authentication Card */}
			<SettingsCard
				description={t("addExtraLayerSecurity")}
				form={twoFactorForm}
				isSubmitting={isSubmittingTwoFactor || isLoading}
				onFormSubmit={submitTwoFactor}
				saveButtonText={
					setupMode === "in_progress" ? t("verify") : tCommon("save")
				}
				title={t("twoFactorAuthentication")}
			>
				<div className="space-y-6">
					{/* 2FA Toggle */}
					<FormFieldWithTooltip
						helperText={t("twoFactorAddsExtraLayer")}
						id="enabled"
						label={t("enable2FA")}
						tooltip={t("whenEnabledNeedCodeFromApp")}
					>
						<div className="flex items-center space-x-2">
							<Switch
								checked={twoFactorEnabled}
								disabled={
									isLoading ||
									isSubmittingTwoFactor ||
									setupMode === "in_progress"
								}
								id="enabled"
								onCheckedChange={handleTwoFactorToggle}
							/>
							<span className="font-medium text-sm">
								{twoFactorEnabled
									? t("twoFactorEnabled")
									: t("twoFactorDisabled")}
							</span>
						</div>
					</FormFieldWithTooltip>

					{/* Authenticator App Recommendations */}
					<Alert className="border bg-muted/50">
						<Info className="h-4 w-4" />
						<AlertTitle>{tCommon("recommendedAuthenticatorApps")}</AlertTitle>
						<AlertDescription className="mt-2">
							<p className="mb-2 text-sm">
								{tCommon("youCanUseAnyOfTheseAuthenticat")}
							</p>
							<ul className="list-disc space-y-1 pl-5 text-sm">
								<li>
									<strong>{tCommon("googleAuthenticator")}</strong>{" "}
									{tCommon("simpleAndWidelyUsed")}
								</li>
								<li>
									<strong>{tCommon("microsoftAuthenticator")}</strong>{" "}
									{tCommon("includesCloudBackup")}
								</li>
								<li>
									<strong>Authy</strong> {tCommon("offersMultideviceSync")}
								</li>
							</ul>
						</AlertDescription>
					</Alert>

					{/* Setup in Progress - Show QR Code and Verification Input */}
					{setupMode === "in_progress" && (
						<div className="mt-4 space-y-4 rounded-md border bg-muted/50 p-4">
							<p className="font-medium text-sm">
								{tCommon("scanThisQrCodeWithYourAuthenti")}
							</p>

							<div className="mx-auto h-48 w-48 overflow-hidden bg-white">
								{qrCodeUrl ? (
									<div className="flex h-full w-full items-center justify-center">
										<Image
											alt={tCommon("2faQrCode")}
											className="h-full w-full"
											height={192}
											priority
											src={qrCodeUrl}
											style={{ objectFit: "fill" }}
											unoptimized
											width={192}
										/>
									</div>
								) : (
									<div className="flex h-full w-full items-center justify-center">
										<p className="text-center text-muted-foreground text-sm">
											{tCommon("loadingQrCode")}
										</p>
									</div>
								)}
							</div>

							<p className="text-muted-foreground text-sm">
								{t("enterSixDigitCode")}
							</p>

							<FormProvider {...twoFactorForm}>
								<OTPInputField
									className="mb-2"
									error={twoFactorForm.formState.errors.verificationCode}
									helperText={t("enterSixDigitCode")}
									id="verificationCode"
									label={t("verificationCode")}
									length={6}
									required
								/>
							</FormProvider>
						</div>
					)}

					{/* Setup Completed - Show Backup Codes */}
					{setupMode === "completed" &&
						showBackupCodes &&
						backupCodes.length > 0 && (
							<div className="mt-4 space-y-4 rounded-md border bg-muted/50 p-4">
								<div className="flex items-center justify-between">
									<p className="font-medium text-sm">
										{tCommon("backupCodes")}
									</p>
									<div className="flex space-x-2">
										<Button
											className="h-8 px-2 text-xs"
											onClick={handleCopyBackupCodes}
											size="sm"
											variant="outline"
										>
											<Copy className="mr-1 h-3.5 w-3.5" />
											Copy
										</Button>
										<Button
											className="h-8 px-2 text-xs"
											disabled={isLoading}
											onClick={handleGenerateNewBackupCodes}
											size="sm"
											variant="outline"
										>
											<RefreshCw className="mr-1 h-3.5 w-3.5" />
											{t("generate")}
										</Button>
									</div>
								</div>

								<Alert
									className="border-destructive/20 bg-destructive/5 text-destructive"
									variant="destructive"
								>
									<AlertCircle className="h-4 w-4" />
									<AlertTitle>Important</AlertTitle>
									<AlertDescription className="mt-2">
										<p className="text-sm">
											{tCommon("saveTheseBackupCodesInASecureL")}
										</p>
									</AlertDescription>
								</Alert>

								<div className="grid grid-cols-2 gap-2">
									{backupCodes.map((code, index) => (
										<div
											className="rounded border bg-background p-2 font-mono text-sm"
											key={index}
										>
											{code}
										</div>
									))}
								</div>
							</div>
						)}

					{/* Setup Completed - Show Manage Backup Codes Button */}
					{setupMode === "completed" && !showBackupCodes && (
						<div className="mt-4">
							<Button
								className="w-full"
								disabled={isLoading}
								onClick={async () => {
									setIsLoading(true);
									try {
										const response = await fetch(
											"/api/settings/two-factor/get-backup-codes",
										);

										if (!response.ok) {
											showError("BackupCodeFetchFailed");
											return;
										}

										const data = await response.json();
										setBackupCodes(data.backupCodes);
										setShowBackupCodes(true);
									} catch (error) {
										console.error("Error fetching backup codes:", error);
										showError("BackupCodeFetchFailed");
									} finally {
										setIsLoading(false);
									}
								}}
								variant="outline"
							>
								{isLoading ? (
									<>
										<RefreshCw className="mr-2 h-4 w-4 animate-spin" />
										{tCommon("loading")}
									</>
								) : (
									<>
										<Shield className="mr-2 h-4 w-4" />
										{tCommon("backupCodes")}
									</>
								)}
							</Button>
						</div>
					)}
				</div>
			</SettingsCard>
		</div>
	);
}
