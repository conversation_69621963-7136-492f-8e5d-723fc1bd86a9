/**
 * Enhanced in-memory cache utility for the application
 * Provides TTL-based expiration, prefix-based operations, and size management
 * Helps reduce database connections and improve performance by caching frequently accessed data
 */

interface CacheItem<T> {
	value: T;
	expiry: number;
}

class Cache {
	private cache: Map<string, CacheItem<unknown>>;
	private defaultTTL: number;
	private maxSize: number | null;

	/**
	 * Create a new cache instance
	 * @param defaultTTL Default time to live in milliseconds (default: 60 seconds)
	 * @param maxSize Maximum number of items in the cache (default: null, no limit)
	 */
	constructor(defaultTTL: number = 60 * 1000, maxSize: number | null = null) {
		this.cache = new Map();
		this.defaultTTL = defaultTTL;
		this.maxSize = maxSize;
	}

	/**
	 * Get a value from the cache
	 * @param key The cache key
	 * @returns The cached value or null if not found or expired
	 */
	get<T>(key: string): T | null {
		const item = this.cache.get(key);

		// Return null if item doesn't exist
		if (!item) {
			return null;
		}

		// Check if the item has expired
		if (Date.now() > item.expiry) {
			this.cache.delete(key);
			return null;
		}

		return item.value as T;
	}

	/**
	 * Set a value in the cache
	 * @param key The cache key
	 * @param value The value to cache
	 * @param ttl Time to live in milliseconds (optional, uses default if not provided)
	 */
	set<T>(key: string, value: T, ttl?: number): void {
		// Check if we need to enforce size limits
		if (
			this.maxSize !== null &&
			this.cache.size >= this.maxSize &&
			!this.cache.has(key)
		) {
			// Remove the item with the earliest expiry
			this.removeEarliestExpiry();
		}

		const expiry = Date.now() + (ttl || this.defaultTTL);
		this.cache.set(key, { value, expiry });
	}

	/**
	 * Delete a value from the cache
	 * @param key The cache key
	 * @returns true if the item was deleted, false if it didn't exist
	 */
	delete(key: string): boolean {
		return this.cache.delete(key);
	}

	/**
	 * Clear all values from the cache
	 */
	clear(): void {
		this.cache.clear();
	}

	/**
	 * Get a value from the cache or compute it if not found
	 * @param key The cache key
	 * @param fn Function to compute the value if not in cache
	 * @param ttl Time to live in milliseconds (optional)
	 * @returns The cached or computed value
	 */
	async getOrSet<T>(
		key: string,
		fn: () => Promise<T>,
		ttl?: number,
	): Promise<T> {
		const cachedValue = this.get<T>(key);

		if (cachedValue !== null) {
			return cachedValue;
		}

		const value = await fn();
		this.set(key, value, ttl);
		return value;
	}

	/**
	 * Get all keys in the cache
	 * @returns Array of all cache keys
	 */
	getAllKeys(): string[] {
		return Array.from(this.cache.keys());
	}

	/**
	 * Get all keys with a specific prefix
	 * @param prefix The prefix to match
	 * @returns Array of matching cache keys
	 */
	getKeysByPrefix(prefix: string): string[] {
		return this.getAllKeys().filter((key) => key.startsWith(prefix));
	}

	/**
	 * Delete all cache entries with a specific prefix
	 * @param prefix The prefix to match
	 * @returns Number of entries deleted
	 */
	deleteByPrefix(prefix: string): number {
		const keys = this.getKeysByPrefix(prefix);
		let count = 0;

		keys.forEach((key) => {
			if (this.cache.delete(key)) {
				count++;
			}
		});

		return count;
	}

	/**
	 * Count cache entries with a specific prefix
	 * @param prefix The prefix to match
	 * @returns Number of matching entries
	 */
	countByPrefix(prefix: string): number {
		return this.getKeysByPrefix(prefix).length;
	}

	/**
	 * Get cache statistics
	 * @returns Object with cache statistics
	 */
	getStats(): { size: number; maxSize: number | null } {
		return {
			size: this.cache.size,
			maxSize: this.maxSize,
		};
	}

	/**
	 * Remove the item with the earliest expiry time
	 * @returns true if an item was removed, false if the cache is empty
	 * @private
	 */
	private removeEarliestExpiry(): boolean {
		if (this.cache.size === 0) {
			return false;
		}

		let earliestKey: string | null = null;
		let earliestExpiry = Number.POSITIVE_INFINITY;

		// Find the item with the earliest expiry
		for (const [key, item] of this.cache.entries()) {
			if (item.expiry < earliestExpiry) {
				earliestKey = key;
				earliestExpiry = item.expiry;
			}
		}

		// Remove the item
		if (earliestKey !== null) {
			return this.cache.delete(earliestKey);
		}

		return false;
	}
}

// Create a singleton instance with a default TTL of 60 seconds
const appCache = new Cache(60 * 1000);

export default appCache;
