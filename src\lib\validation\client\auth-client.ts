import { z } from "zod/v4";

/**
 * Client-side validation schemas for admin authentication forms
 * These schemas use translation functions for user-facing error messages
 * Use with useTranslations('validation') hook in client components
 */

/**
 * Create login form validation schema with translations
 * @param t - Translation function from useTranslations('validation')
 */
export function createClientLoginSchema(t: any) {
  return z.object({
    username: z.string().min(1, { error: t("usernameRequired") }),
    password: z.string().min(1, { error: t("passwordRequired") }),
  });
}

/**
 * Create TOTP/2FA form validation schema with translations
 * @param t - Translation function from useTranslations('validation')
 */
export function createClientTotpSchema(t: any) {
  return z.object({
    token: z
      .string()
      .min(6, { error: t("codeMinLength") })
      .max(6, { error: t("codeMaxLength") })
      .refine(
        (val) => {
          // Either a 6-digit TOTP code or a 6-character alphanumeric backup code
          return /^[A-Z0-9]{6}$/i.test(val);
        },
        {
          error: t("codeInvalidFormat"),
        },
      ),
  });
}

/**
 * Create password change form validation schema with translations
 * @param t - Translation function from useTranslations('validation')
 */
export function createClientPasswordChangeSchema(t: any) {
  return z
    .object({
      currentPassword: z.string().min(1, { error: t("currentPasswordRequired") }),
      newPassword: z.string().min(8, { error: t("newPasswordMinLength") }),
      confirmPassword: z.string().min(8, { error: t("confirmPasswordMinLength") }),
    })
    .refine((data) => data.newPassword === data.confirmPassword, {
      error: t("passwordsDoNotMatch"),
      path: ["confirmPassword"],
    });
}

// Type exports for client-side forms
export type ClientLoginFormValues = z.infer<ReturnType<typeof createClientLoginSchema>>;
export type ClientTotpFormValues = z.infer<ReturnType<typeof createClientTotpSchema>>;
export type ClientPasswordChangeFormValues = z.infer<
  ReturnType<typeof createClientPasswordChangeSchema>
>;
