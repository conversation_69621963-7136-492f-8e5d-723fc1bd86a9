"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import { signIn } from "next-auth/react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/forms/common/Form";
import { FormField } from "@/components/forms/common/FormField";
import { useMessage } from "@/hooks/useMessage";
import { zodResolver } from "@/lib/utils/zod-resolver-compat";
import {
	type ClientLoginFormValues,
	createClientLoginSchema,
} from "@/lib/validation/client/auth-client";

// Metadata needs to be in a separate file for client components
// This is now in layout.tsx

// AuthRedirectAlert component has been removed as it's not needed

export default function AdminLoginPage() {
	const [isLoading, setIsLoading] = useState(false);
	const router = useRouter();
	const { showError, showSuccess, showDirect } = useMessage();

	// Translation hooks
	const t = useTranslations("auth");
	const tNav = useTranslations("navigation");
	const tForms = useTranslations("forms");
	const tCommon = useTranslations("common");
	const tValidation = useTranslations("validation");
	const tBrand = useTranslations("brand");

	// Create client-side validation schema with translations
	const loginSchema = createClientLoginSchema(tValidation);

	const form = useForm<ClientLoginFormValues>({
		resolver: zodResolver(loginSchema),
		defaultValues: {
			username: "",
			password: "",
		},
	});

	const onSubmit = async (data: ClientLoginFormValues) => {
		setIsLoading(true);

		try {
			const result = await signIn("credentials", {
				username: data.username,
				password: data.password,
				redirect: false,
			});

			if (result?.error) {
				// Use our centralized error handling
				showError(result.error, "auth");
			} else if (result?.ok) {
				showSuccess("loginSuccessful");
				// Add a small delay to allow the toast to show
				setTimeout(() => {
					router.push("/admin/dashboard");
				}, 1000);
			} else {
				showError("authenticationError", "auth");
			}
		} catch (error) {
			showError("authenticationError", "auth");
			console.error("Login error:", error);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div className="grid h-full items-stretch">
			{/* AuthRedirectAlert component has been removed */}
			{/* Admin Login Form */}
			<div className="flex h-full flex-col gap-4 p-6 md:p-10">
				<div className="flex justify-center gap-2 md:justify-start">
					<Link className="flex items-center gap-2 font-medium" href="/">
						<div className="flex h-6 w-6 items-center justify-center rounded-md bg-primary text-primary-foreground">
							<svg
								fill="none"
								height="16"
								stroke="currentColor"
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth="2"
								viewBox="0 0 24 24"
								width="16"
								xmlns="http://www.w3.org/2000/svg"
							>
								<path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z" />
								<path d="M3 9V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v4" />
								<path d="M13 13h4" />
								<path d="M13 17h4" />
								<path d="M7 13h2v4H7z" />
							</svg>
						</div>
						{tBrand("name")}
					</Link>
				</div>
				<div className="flex flex-1 items-center justify-center">
					<div className="w-full max-w-sm">
						<div className="flex flex-col gap-6 p-4 md:p-0">
							<div className="flex flex-col items-center gap-2 text-center">
								<h1 className="font-bold text-2xl">{t("login")}</h1>
								<p className="text-balance text-muted-foreground text-sm">
									{t("signInToAccessDashboard")}
								</p>
							</div>
							<div className="grid gap-6">
								<Form
									className="space-y-4"
									form={form}
									isLoading={isLoading}
									onSubmit={onSubmit}
									submitText={t("signIn")}
								>
									<FormField
										error={form.formState.errors.username}
										id="username"
										label={t("username")}
										placeholder={tForms("enterUsername")}
										register={form.register}
										required
									/>
									<FormField
										error={form.formState.errors.password}
										id="password"
										label={t("password")}
										placeholder={tForms("enterPassword")}
										register={form.register}
										required
										type="password"
									/>
								</Form>
								<div className="text-center text-sm">
									<Link
										className="flex items-center justify-center gap-1 text-muted-foreground underline underline-offset-4 hover:text-primary"
										href="/"
									>
										{/* Add the arrow icon
                    <ArrowLeft size={16} />*/}
										{tCommon("back")} {tNav("home")}
									</Link>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
