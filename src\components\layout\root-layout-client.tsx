"use client";

import { usePathname } from "next/navigation";
import { Footer } from "@/components/common/footer";

interface RootLayoutClientProps {
  children: React.ReactNode;
}

export function RootLayoutClient({ children }: RootLayoutClientProps) {
  const pathname = usePathname();
  // Show footer on all pages except authenticated admin pages
  // Admin login page should still have the footer
  const isAuthenticatedAdminPage = pathname?.startsWith("/admin") && pathname !== "/admin/login";

  return (
    <div className="flex min-h-screen flex-col">
      <div className="flex flex-1 flex-col">{children}</div>
      {!isAuthenticatedAdminPage && <Footer />}
    </div>
  );
}
