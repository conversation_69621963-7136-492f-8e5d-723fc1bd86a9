"use client";

import { type BaseChartProps, registerChart } from "./chart-registry";
import { CHART_COLORS } from "./constants";

interface WaffleDataPoint {
	name: string;
	value: number;
	[key: string]: unknown;
}

function WaffleChartComponent({
	data,
	isAnimationActive = true,
	className,
}: BaseChartProps) {
	const total = data.data.reduce(
		(sum, item) => sum + (item as WaffleDataPoint).value,
		0,
	);

	// Calculate squares for each category
	// Calculate squares with proper remainder distribution to ensure exactly 100 squares
	const squaresWithRemainder = data.data.map((item, index) => {
		const waffleItem = item as WaffleDataPoint;
		const exact = (waffleItem.value / total) * 100;
		return {
			...waffleItem,
			squares: Math.floor(exact),
			remainder: exact % 1,
			color: CHART_COLORS.PALETTE[index % CHART_COLORS.PALETTE.length],
		};
	});

	// Calculate how many squares we need to distribute
	const allocatedSquares = squaresWithRemainder.reduce(
		(sum, item) => sum + item.squares,
		0,
	);
	const remainingSquares = 100 - allocatedSquares;

	// Sort by remainder (descending) and distribute remaining squares
	const sortedByRemainder = [...squaresWithRemainder].sort(
		(a, b) => b.remainder - a.remainder,
	);

	// Distribute remaining squares to items with largest remainders
	for (
		let i = 0;
		i < Math.min(remainingSquares, sortedByRemainder.length);
		i++
	) {
		sortedByRemainder[i].squares += 1;
	}

	// Remove remainder property and restore original order
	const squares = squaresWithRemainder.map((originalItem) => {
		const updatedItem = sortedByRemainder.find(
			(item) =>
				item.name === originalItem.name && item.value === originalItem.value,
		);
		return {
			name: originalItem.name,
			value: originalItem.value,
			squares: updatedItem?.squares || originalItem.squares,
			color: originalItem.color,
		};
	});

	// Create grid array
	const grid = [];
	let currentIndex = 0;
	let currentSquareCount = 0;

	for (let i = 0; i < 100; i++) {
		if (
			currentIndex < squares.length &&
			currentSquareCount >= squares[currentIndex].squares
		) {
			currentIndex++;
			currentSquareCount = 0;
		}

		grid.push({
			index: i,
			category: currentIndex < squares.length ? squares[currentIndex] : null,
			delay: isAnimationActive ? i * 10 : 0, // Staggered animation
		});

		if (currentIndex < squares.length) {
			currentSquareCount++;
		}
	}

	return (
		<div className={`flex flex-col items-center gap-4 ${className || ""}`}>
			{/* Waffle Grid */}
			<div className="grid grid-cols-10 gap-1 p-4">
				{grid.map((square) => (
					<div
						className="h-6 w-6 rounded-sm transition-all duration-300 ease-out"
						key={square.index}
						style={{
							backgroundColor: square.category?.color || "#e5e7eb",
							animationDelay: `${square.delay}ms`,
							animation: isAnimationActive
								? "fadeInScale 0.3s ease-out forwards"
								: "none",
							opacity: isAnimationActive ? 0 : 1,
							transform: isAnimationActive ? "scale(0)" : "scale(1)",
						}}
						title={
							square.category
								? `${square.category.name}: ${square.category.value}`
								: "Empty"
						}
					/>
				))}
			</div>

			{/* Legend */}
			<div className="flex flex-wrap justify-center gap-4 text-sm">
				{squares.map((item, index) => (
					<div className="flex items-center gap-2" key={index}>
						<div
							className="h-3 w-3 rounded-sm"
							style={{ backgroundColor: item.color }}
						/>
						<span className="text-foreground">
							{item.name}: {item.value} (
							{((item.value / total) * 100).toFixed(1)}%)
						</span>
					</div>
				))}
			</div>

			{/* Add CSS animation */}
			<style jsx>{`
        @keyframes fadeInScale {
          from {
            opacity: 0;
            transform: scale(0);
          }
          to {
            opacity: 1;
            transform: scale(1);
          }
        }
      `}</style>
		</div>
	);
}

// Register the component
registerChart("waffle", WaffleChartComponent);

export default WaffleChartComponent;
