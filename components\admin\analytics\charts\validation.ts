// Comprehensive chart data validation system

import type { ChartData } from "../../../../lib/utils/chart-data-formatter";

// Validation result interface
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Field validation utilities
export function validateRequiredFields(data: unknown[], fields: string[]): string[] {
  const errors: string[] = [];

  if (!data || data.length === 0) {
    errors.push("Data array cannot be empty");
    return errors;
  }

  for (const [rowIdx, item] of data.entries()) {
    const record = item as Record<string, unknown>;
    for (const field of fields) {
      if (!(field in record)) {
        errors.push(`Row ${rowIdx}: required field '${field}' is missing`);
      } else if (record[field] === null || record[field] === undefined) {
        errors.push(`Row ${rowIdx}: required field '${field}' cannot be null or undefined`);
      }
    }
  }

  return errors;
}

export function validateNumericFields(data: unknown[], fields: string[]): string[] {
  const errors: string[] = [];

  for (const [index, item] of data.entries()) {
    const record = item as Record<string, unknown>;
    for (const field of fields) {
      if (field in record) {
        const value = record[field];
        if (
          value === null ||
          value === undefined ||
          (typeof value !== "number" &&
            !(typeof value === "string" && value.trim() !== "" && !Number.isNaN(Number(value))))
        ) {
          errors.push(`Row ${index}: field '${field}' must be numeric and not null/undefined`);
        }
      }
    }
  }

  return errors;
}

export function validateStringFields(data: unknown[], fields: string[]): string[] {
  const errors: string[] = [];

  for (const item of data) {
    const record = item as Record<string, unknown>;
    for (const field of fields) {
      if (field in record) {
        const value = record[field];
        if (typeof value !== "string" && value !== null && value !== undefined) {
          errors.push(`Field '${field}' must be a string`);
        }
      }
    }
  }

  return errors;
}

// Chart-specific validation functions
export function validateBarChartData(data: ChartData): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields validation
  errors.push(...validateRequiredFields(data.data, ["name", "value"]));

  // Type validation
  errors.push(...validateStringFields(data.data, ["name"]));
  errors.push(...validateNumericFields(data.data, ["value"]));

  // Business logic validation
  const values = data.data
    .map((item) => Number((item as Record<string, unknown>).value))
    .filter((v) => !Number.isNaN(v));
  if (values.length > 0) {
    const negativeValues = values.filter((v) => v < 0);
    if (negativeValues.length > 0) {
      warnings.push(`${negativeValues.length} negative values found in bar chart`);
    }
  }

  // Performance warnings
  if (data.data.length > 50) {
    warnings.push(
      "Large dataset detected. Consider pagination or aggregation for better performance",
    );
  }

  return { isValid: errors.length === 0, errors, warnings };
}

export function validatePieChartData(data: ChartData): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields validation
  errors.push(...validateRequiredFields(data.data, ["name", "value"]));

  // Type validation
  errors.push(...validateStringFields(data.data, ["name"]));
  errors.push(...validateNumericFields(data.data, ["value"]));

  // Pie-specific validation
  const values = data.data
    .map((item) => Number((item as Record<string, unknown>).value))
    .filter((v) => !Number.isNaN(v));
  if (values.length > 0) {
    const negativeValues = values.filter((v) => v < 0);
    if (negativeValues.length > 0) {
      errors.push("Pie charts cannot display negative values");
    }

    const zeroValues = values.filter((v) => v === 0);
    if (zeroValues.length > 0) {
      warnings.push(
        `${zeroValues.length} zero values found. These will not be visible in pie chart`,
      );
    }
  }

  // Too many slices warning
  if (data.data.length > 10) {
    warnings.push(
      "Too many slices may make pie chart difficult to read. Consider grouping smaller values",
    );
  }

  return { isValid: errors.length === 0, errors, warnings };
}

export function validateLineChartData(data: ChartData): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields validation
  errors.push(...validateRequiredFields(data.data, ["name", "value"]));

  // Type validation
  errors.push(...validateStringFields(data.data, ["name"]));
  errors.push(...validateNumericFields(data.data, ["value"]));

  // Line chart specific validation
  if (data.data.length < 2) {
    warnings.push("Line charts work best with at least 2 data points");
  }

  return { isValid: errors.length === 0, errors, warnings };
}

export function validateScatterChartData(data: ChartData): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields validation
  errors.push(...validateRequiredFields(data.data, ["x", "y"]));

  // Type validation
  errors.push(...validateNumericFields(data.data, ["x", "y"]));

  // Optional fields validation
  if (data.data.some((item) => "size" in item)) {
    errors.push(...validateNumericFields(data.data, ["size"]));
  }

  if (data.data.some((item) => "category" in item)) {
    errors.push(...validateStringFields(data.data, ["category"]));
  }

  // Configuration validation
  if (!(data.xKey || data.yKey)) {
    warnings.push("Consider setting xKey and yKey for better axis labels");
  }

  return { isValid: errors.length === 0, errors, warnings };
}

export function validateHeatmapData(data: ChartData): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields validation
  errors.push(...validateRequiredFields(data.data, ["x", "y", "value"]));

  // Type validation
  errors.push(...validateNumericFields(data.data, ["value"]));

  // Heatmap specific validation
  const xValues = new Set(data.data.map((item) => (item as Record<string, unknown>).x));
  const yValues = new Set(data.data.map((item) => (item as Record<string, unknown>).y));

  if (xValues.size < 2 || yValues.size < 2) {
    warnings.push("Heatmaps work best with at least 2 unique values for both x and y axes");
  }

  // Check for missing combinations
  const expectedCombinations = xValues.size * yValues.size;
  if (data.data.length < expectedCombinations) {
    warnings.push("Some x,y combinations are missing. Heatmap may have gaps");
  }

  return { isValid: errors.length === 0, errors, warnings };
}

export function validateRadarChartData(data: ChartData): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields validation
  errors.push(...validateRequiredFields(data.data, ["name"]));

  // Type validation
  errors.push(...validateStringFields(data.data, ["name"]));

  // Radar specific validation
  if (data.config?.metrics && Array.isArray(data.config.metrics)) {
    const metrics = data.config.metrics as string[];
    if (metrics.length < 3) {
      errors.push("Radar chart requires at least 3 metrics");
    }

    // Validate that all metrics exist in data
    for (const metric of metrics) {
      errors.push(...validateNumericFields(data.data, [metric]));
    }
  } else {
    errors.push("Radar chart requires metrics array in config");
  }

  return { isValid: errors.length === 0, errors, warnings };
}

export function validateTreemapData(data: ChartData): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields validation
  errors.push(...validateRequiredFields(data.data, ["name", "value"]));

  // Type validation
  errors.push(...validateStringFields(data.data, ["name"]));
  errors.push(...validateNumericFields(data.data, ["value"]));

  // Treemap specific validation
  const values = data.data
    .map((item) => Number((item as Record<string, unknown>).value))
    .filter((v) => !Number.isNaN(v));
  if (values.length > 0) {
    const negativeValues = values.filter((v) => v < 0);
    if (negativeValues.length > 0) {
      errors.push("Treemap cannot display negative values");
    }
  }

  return { isValid: errors.length === 0, errors, warnings };
}

export function validateWaffleData(data: ChartData): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields validation
  errors.push(...validateRequiredFields(data.data, ["name", "value"]));

  // Type validation
  errors.push(...validateStringFields(data.data, ["name"]));
  errors.push(...validateNumericFields(data.data, ["value"]));

  // Waffle specific validation
  const values = data.data
    .map((item) => Number((item as Record<string, unknown>).value))
    .filter((v) => !Number.isNaN(v));
  if (values.length > 0) {
    const negativeValues = values.filter((v) => v < 0);
    if (negativeValues.length > 0) {
      errors.push("Waffle chart cannot display negative values");
    }
  }

  // Too many categories warning
  if (data.data.length > 8) {
    warnings.push("Too many categories may make waffle chart difficult to read");
  }

  return { isValid: errors.length === 0, errors, warnings };
}

export function validateAreaChartData(data: ChartData): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields validation
  errors.push(...validateRequiredFields(data.data, ["date"]));

  // Type validation
  errors.push(...validateStringFields(data.data, ["date"]));

  // Area chart specific validation
  if (data.data.length < 2) {
    warnings.push("Area charts work best with at least 2 data points");
  }

  // Validate numeric fields (all fields except date should be numeric for stacking)
  const firstItem = data.data[0] as Record<string, unknown>;
  const numericFields = Object.keys(firstItem).filter(
    (key) => key !== "date" && !Number.isNaN(Number(firstItem[key] as never)),
  );

  if (numericFields.length === 0) {
    errors.push("Area chart requires at least one numeric field besides date");
  } else {
    errors.push(...validateNumericFields(data.data, numericFields));
  }

  return { isValid: errors.length === 0, errors, warnings };
}

// Main validation function with chart type routing
export function validateChartData(data: ChartData): ValidationResult {
  if (!data) {
    return { isValid: false, errors: ["Chart data is required"], warnings: [] };
  }

  if (!data.type) {
    return { isValid: false, errors: ["Chart type is required"], warnings: [] };
  }

  if (!(data.data && Array.isArray(data.data))) {
    return {
      isValid: false,
      errors: ["Chart data must be an array"],
      warnings: [],
    };
  }

  if (data.data.length === 0) {
    return {
      isValid: false,
      errors: ["Chart data cannot be empty"],
      warnings: [],
    };
  }

  // Route to specific validation function
  switch (data.type) {
    case "bar":
      return validateBarChartData(data);
    case "pie":
      return validatePieChartData(data);
    case "line":
      return validateLineChartData(data);
    case "scatter":
      return validateScatterChartData(data);
    case "heatmap":
      return validateHeatmapData(data);
    case "radar":
      return validateRadarChartData(data);
    case "treemap":
      return validateTreemapData(data);
    case "waffle":
      return validateWaffleData(data);
    case "area":
      return validateAreaChartData(data);
    default:
      return {
        isValid: false,
        errors: [`Unknown chart type: ${data.type}`],
        warnings: [],
      };
  }
}
