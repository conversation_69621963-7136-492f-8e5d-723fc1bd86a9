"use client";

import { Users } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { devLog } from "@/lib/utils";

interface GenderDistributionData {
  genderDistribution: {
    male: number;
    female: number;
    other: number;
  };
  totalMembers: number;
}

export function GenderDistributionChart() {
  const t = useTranslations();
  const tGenders = useTranslations("genders");
  const tCommon = useTranslations("common");
  const [data, setData] = useState<GenderDistributionData | null>(null);
  const [loading, setLoading] = useState(true);
  const [hoveredSquare, setHoveredSquare] = useState<{
    index: number;
    gender: string;
    count: number;
    percentage: string;
  } | null>(null);

  useEffect(() => {
    const fetchDemographics = async () => {
      try {
        const response = await fetch("/api/admin/dashboard/demographics");

        if (response.ok) {
          const demographicData = await response.json();

          const extractedData = {
            genderDistribution: demographicData.genderDistribution,
            totalMembers: demographicData.totalMembers,
          };
          setData(extractedData);
        }
      } catch (error) {
        devLog.error("Error fetching gender distribution:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchDemographics();
  }, []);

  if (loading || !data) {
    return null;
  }

  return (
    <div className="relative">
      {/* Background glow effect */}
      <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-[#97A4FF]/5 via-[#FF6308]/5 to-[#BDC9E6]/5 blur-2xl dark:from-[#97A4FF]/10 dark:via-[#FF6308]/10 dark:to-[#BDC9E6]/10" />

      {/* Main card */}
      <div className="relative rounded-2xl border border-white/30 bg-white/80 p-8 shadow-lg shadow-slate-200/30 backdrop-blur-xl dark:border-slate-700/30 dark:bg-slate-800/80 dark:shadow-slate-900/30">
        {/* Card header */}
        <div className="mb-8 space-y-4">
          <div className="flex items-center gap-4">
            <div className="rounded-xl bg-gradient-to-r from-[#97A4FF]/10 to-[#FF6308]/10 p-3 dark:from-[#97A4FF]/20 dark:to-[#FF6308]/20">
              <div className="rounded-lg bg-gradient-to-r from-[#97A4FF] to-[#FF6308] p-2 text-white">
                <Users className="h-6 w-6" />
              </div>
            </div>
            <div className="space-y-1">
              <h2 className="font-light text-2xl text-slate-800 tracking-tight dark:text-slate-100">
                {t("common.genderDistribution")}
              </h2>
              <p className="font-medium text-slate-600 dark:text-slate-300">
                {t("admin.genderRepresentationByPercentage")}
              </p>
            </div>
          </div>
        </div>

        {/* Waffle Chart - Restored Original Layout */}
        <div className="flex h-[300px] flex-col">
          {/* Chart Area */}
          <div className="relative flex flex-1 flex-col items-center justify-center">
            <div className="mb-6 grid max-w-[320px] grid-cols-10 gap-1">
              {Array.from({ length: 100 }, (_, index) => {
                const maleCount = Math.round(
                  (data.genderDistribution.male / data.totalMembers) * 100,
                );
                const femaleCount = Math.round(
                  (data.genderDistribution.female / data.totalMembers) * 100,
                );
                const otherCount = Math.round(
                  (data.genderDistribution.other / data.totalMembers) * 100,
                );

                let color = "#f1f5f9"; // default light gray
                let gender = tCommon("empty");
                let count = 0;
                let percentage = "0";

                if (index < maleCount) {
                  color = "#97A4FF";
                  gender = tGenders("male");
                  count = data.genderDistribution.male;
                  percentage = ((data.genderDistribution.male / data.totalMembers) * 100).toFixed(
                    1,
                  );
                } else if (index < maleCount + femaleCount) {
                  color = "#FF6308";
                  gender = tGenders("female");
                  count = data.genderDistribution.female;
                  percentage = ((data.genderDistribution.female / data.totalMembers) * 100).toFixed(
                    1,
                  );
                } else if (index < maleCount + femaleCount + otherCount) {
                  color = "#BDC9E6";
                  gender = tGenders("other");
                  count = data.genderDistribution.other;
                  percentage = ((data.genderDistribution.other / data.totalMembers) * 100).toFixed(
                    1,
                  );
                }

                return (
                  <div
                    className="group relative h-6 w-6 cursor-pointer rounded-sm transition-all duration-200 hover:scale-110"
                    key={index}
                    onMouseEnter={() => {
                      if (gender !== tCommon("empty")) {
                        setHoveredSquare({
                          index,
                          gender,
                          count,
                          percentage,
                        });
                      }
                    }}
                    onMouseLeave={() => setHoveredSquare(null)}
                    style={{ backgroundColor: color }}
                  >
                    {/* Tooltip - Modern Dark Theme Aware */}
                    {hoveredSquare && hoveredSquare.index === index && (
                      <div className="-translate-x-1/2 absolute bottom-full left-1/2 z-10 mb-2 transform whitespace-nowrap rounded-lg border border-slate-200 bg-white px-3 py-2 font-medium text-slate-900 text-xs shadow-lg dark:border-slate-700 dark:bg-slate-800 dark:text-slate-100">
                        <div className="text-center">
                          <div className="font-semibold">{hoveredSquare.gender}</div>
                          <div className="text-slate-600 dark:text-slate-400">
                            {hoveredSquare.count} {tCommon("membersLowercase")} (
                            {hoveredSquare.percentage}%)
                          </div>
                        </div>
                        {/* Arrow */}
                        <div className="-translate-x-1/2 absolute top-full left-1/2 h-0 w-0 transform border-transparent border-t-[6px] border-t-white border-r-[6px] border-l-[6px] dark:border-t-slate-800" />
                      </div>
                    )}
                  </div>
                );
              })}
            </div>

            {/* Legend - Restored original positioning */}
            <div className="flex flex-wrap justify-center gap-4">
              <div className="flex items-center gap-2">
                <div className="h-3 w-3 rounded-sm bg-[#97A4FF]" />
                <span className="font-medium text-slate-600 text-xs dark:text-slate-400">
                  {tGenders("male")} ({data.genderDistribution.male})
                </span>
              </div>
              <div className="flex items-center gap-2">
                <div className="h-3 w-3 rounded-sm bg-[#FF6308]" />
                <span className="font-medium text-slate-600 text-xs dark:text-slate-400">
                  {tGenders("female")} ({data.genderDistribution.female})
                </span>
              </div>
              {data.genderDistribution.other > 0 && (
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-sm bg-[#BDC9E6]" />
                  <span className="font-medium text-slate-600 text-xs dark:text-slate-400">
                    {tGenders("other")} ({data.genderDistribution.other})
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
