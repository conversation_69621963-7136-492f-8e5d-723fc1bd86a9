/**
 * Census Year Settings Database Operations with Prisma
 * Replaces MySQL-based implementation with Prisma ORM
 */

import {
	createCensusYear,
	getCensusYearByYear,
	setActiveCensusYear,
} from "./census-years";
import { prisma } from "./prisma";

/**
 * Get census year settings
 */
export async function getCensusYearSettings(): Promise<{
	currentCensusYear: string;
	lastCensusYear: string;
}> {
	const settings = await prisma.systemSettings.findMany({
		where: {
			settingKey: {
				in: ["current_census_year", "last_census_year"],
			},
		},
	});

	// Create a map of settings
	const settingsMap: Record<string, string> = {};
	settings.forEach((setting) => {
		settingsMap[setting.settingKey] = setting.settingValue || "";
	});

	return {
		currentCensusYear: settingsMap["current_census_year"] || "2023",
		lastCensusYear: settingsMap["last_census_year"] || "2022",
	};
}

/**
 * Synchronize the current census year across both system_settings and census_years tables - Prisma version
 * This ensures that:
 * 1. The current_census_year in system_settings is updated
 * 2. The year exists in the census_years table
 * 3. The year is marked as active in the census_years table
 *
 * @param currentCensusYear The new current census year value
 * @returns The ID of the census year in the census_years table
 */
export async function syncCurrentCensusYear(
	currentCensusYear: string,
): Promise<number> {
	try {
		// Get current settings to determine the previous current year
		const previousSettings = await getCensusYearSettings();

		// Prepare transaction operations
		const updates = [
			// 1. Update the current_census_year in system_settings
			prisma.systemSettings.upsert({
				where: { settingKey: "current_census_year" },
				update: { settingValue: currentCensusYear },
				create: {
					settingKey: "current_census_year",
					settingValue: currentCensusYear,
				},
			}),
			// 2. Update the last_census_year with the previous current_census_year
			prisma.systemSettings.upsert({
				where: { settingKey: "last_census_year" },
				update: { settingValue: previousSettings.currentCensusYear },
				create: {
					settingKey: "last_census_year",
					settingValue: previousSettings.currentCensusYear,
				},
			}),
		];

		// Execute the transaction
		await prisma.$transaction(updates);

		// 3. Check if the year exists in census_years table
		const yearValue = Number.parseInt(currentCensusYear);
		const existingYear = await getCensusYearByYear(yearValue);

		let censusYearId: number;

		if (existingYear) {
			// If it exists, use its ID
			censusYearId = existingYear.id;

			// Set it as active
			await setActiveCensusYear(censusYearId);
		} else {
			// If it doesn't exist, create it
			// Calculate default start and end dates (Jan 1 to Dec 31 of the year)
			const startDate = new Date(yearValue, 0, 1); // January 1st
			const endDate = new Date(yearValue, 11, 31); // December 31st

			const newCensusYear = await createCensusYear({
				year: yearValue,
				isActive: true,
				startDate,
				endDate,
			});

			censusYearId = newCensusYear.id;
		}

		return censusYearId;
	} catch (error) {
		console.error("Error synchronizing census year:", error);
		throw error;
	}
}
