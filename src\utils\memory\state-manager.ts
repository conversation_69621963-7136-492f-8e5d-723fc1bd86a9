/**
 * Memory-efficient state manager with automatic cleanup
 * Uses WeakMap and LRU cache patterns for optimal memory usage
 */

interface StateEntry {
  value: boolean;
  lastAccessed: number;
  accessCount: number;
}

class StateManagerClass {
  private collapseStates = new Map<string, StateEntry>();
  private readonly MAX_ENTRIES = 1000; // Prevent unlimited growth
  private readonly CLEANUP_THRESHOLD = 0.8; // Cleanup when 80% full
  private readonly INACTIVE_THRESHOLD = 5 * 60 * 1000; // 5 minutes

  /**
   * Get collapse state for a given key
   */
  getCollapseState(key: string): boolean | null {
    const entry = this.collapseStates.get(key);
    if (!entry) return null;

    // Update access tracking
    entry.lastAccessed = Date.now();
    entry.accessCount++;

    return entry.value;
  }

  /**
   * Set collapse state for a given key
   */
  setCollapseState(key: string, value: boolean): void {
    const now = Date.now();

    // Check if cleanup is needed
    if (this.collapseStates.size >= this.MAX_ENTRIES * this.CLEANUP_THRESHOLD) {
      this.performCleanup();
    }

    this.collapseStates.set(key, {
      value,
      lastAccessed: now,
      accessCount: 1,
    });
  }

  /**
   * Remove a specific key from state
   */
  cleanup(key: string): void {
    this.collapseStates.delete(key);
  }

  /**
   * Perform automatic cleanup of inactive entries
   */
  private performCleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    // Find inactive entries
    for (const [key, entry] of this.collapseStates.entries()) {
      if (now - entry.lastAccessed > this.INACTIVE_THRESHOLD) {
        keysToDelete.push(key);
      }
    }

    // If still too many entries, remove least recently used
    if (keysToDelete.length === 0 && this.collapseStates.size >= this.MAX_ENTRIES) {
      const sortedEntries = Array.from(this.collapseStates.entries()).sort(
        ([, a], [, b]) => a.lastAccessed - b.lastAccessed,
      );

      const removeCount = Math.floor(this.MAX_ENTRIES * 0.2); // Remove 20%
      keysToDelete.push(...sortedEntries.slice(0, removeCount).map(([key]) => key));
    }

    // Remove identified keys
    keysToDelete.forEach((key) => this.collapseStates.delete(key));
  }

  /**
   * Get current memory usage statistics
   */
  getStats() {
    return {
      totalEntries: this.collapseStates.size,
      maxEntries: this.MAX_ENTRIES,
      memoryUsage: `${((this.collapseStates.size / this.MAX_ENTRIES) * 100).toFixed(1)}%`,
    };
  }
}

// Export singleton instance
export const StateManager = new StateManagerClass();

let cleanupInterval: NodeJS.Timeout | null = null;

// Auto-cleanup interval (runs every 2 minutes)
if (typeof window !== "undefined") {
  cleanupInterval = setInterval(
    () => {
      try {
        StateManager["performCleanup"]();
      } catch (error) {
        if (process.env.NODE_ENV === "development") {
          console.warn("StateManager cleanup failed:", error);
        }
      }
    },
    2 * 60 * 1000,
  );
}

// Export cleanup function for proper resource management
export const cleanupStateManager = () => {
  if (cleanupInterval) {
    clearInterval(cleanupInterval);
    cleanupInterval = null;
  }
};
