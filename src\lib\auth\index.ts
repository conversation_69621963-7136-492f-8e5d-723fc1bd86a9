import { hash, verify } from "@node-rs/argon2";
import { authenticator } from "otplib";
import { generate2FAQR } from "@/lib/utils/qr-code";

// Hash a password using Argon2id (2025 OWASP recommendation)
export async function hashPassword(password: string): Promise<string> {
  return await hash(password, {
    memoryCost: 65_536, // 64 MB
    timeCost: 3, // 3 iterations
    outputLen: 32, // 32 bytes
    parallelism: 1, // 1 thread
  });
}

// Verify a password against an Argon2 hash
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return await verify(hashedPassword, password);
}

// Generate a new TOTP secret
export function generateTOTPSecret(): string {
  return authenticator.generateSecret();
}

// Verify a TOTP token
export function verifyTOTP(token: string, secret: string): boolean {
  return authenticator.verify({ token, secret });
}

// Generate a QR code for TOTP setup
export async function generateTOTPQRCode(
  secret: string,
  username: string,
  issuer = "WSCCC Census",
): Promise<string> {
  // Use the new QR code utility for 2FA
  return generate2FAQR(secret, username, issuer);
}

// Verify a backup code
export async function verifyBackupCode(token: string, backupCodes: string[]): Promise<boolean> {
  if (!backupCodes || backupCodes.length === 0) {
    return false;
  }

  // Normalize the token (remove spaces and convert to uppercase)
  const normalizedToken = token.replace(/\s+/g, "").toUpperCase();

  // Check if the token matches any backup code
  return backupCodes.includes(normalizedToken);
}
