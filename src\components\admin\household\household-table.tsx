"use client";

import {
  Ch<PERSON>ronDown,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  ChevronUp,
  Eye,
  MoreVertical,
  SquarePen,
  Trash2,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { BulkDeleteHouseholdDialog } from "@/components/admin/household/bulk-delete-household-dialog";

import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Pagination, PaginationContent, PaginationItem } from "@/components/ui/pagination";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import type { IHouseholdWithDetails } from "@/lib/db/households";
import { highlightText } from "@/lib/utils/highlight-text";
import type { ICensusYear } from "@/types";

// Use the database interface directly for type consistency
type HouseholdWithDetails = IHouseholdWithDetails;

interface PaginationState {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

interface HouseholdTableProps {
  households: HouseholdWithDetails[];
  loading: boolean;
  pagination: PaginationState;
  sortBy: string;
  sortOrder: "asc" | "desc";
  activeCensusYear: ICensusYear | null;
  searchTerm?: string;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  onSortChange: (column: string) => void;
  onEdit: (household: HouseholdWithDetails) => void;
  onView: (household: HouseholdWithDetails) => void;
  onDeleteSelected: (message?: string) => void;
}

export function HouseholdTable({
  households,
  loading,
  pagination,
  sortBy,
  sortOrder,
  activeCensusYear,
  searchTerm = "",
  onPageChange,
  onPageSizeChange,
  onSortChange,
  onEdit,
  onView,
  onDeleteSelected,
}: HouseholdTableProps) {
  const t = useTranslations("admin");
  const tCommon = useTranslations("common");
  const tEmptyStates = useTranslations("emptyStates");
  const tPagination = useTranslations("pagination");
  const [selectedHouseholds, setSelectedHouseholds] = useState<number[]>([]);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [pageInputValue, setPageInputValue] = useState(pagination.page.toString());
  // Handle selection of all households
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      // Select all households on current page (like members page)
      const allHouseholdIds = households.map((household) => household.id);
      setSelectedHouseholds(allHouseholdIds);
    } else {
      // Deselect all households
      setSelectedHouseholds([]);
    }
  };

  // Handle selection of a single household
  const handleSelectHousehold = (householdId: number, checked: boolean) => {
    // Allow selection of any household (like members page)
    if (checked) {
      setSelectedHouseholds((prev) => [...prev, householdId]);
    } else {
      setSelectedHouseholds((prev) => prev.filter((id) => id !== householdId));
    }
  };

  // Handle deletion of selected households
  const handleDeleteSelected = () => {
    setIsDeleteDialogOpen(true);
  };

  // Confirm deletion of selected households
  const confirmDeleteSelected = (message?: string) => {
    onDeleteSelected(message);
    setIsDeleteDialogOpen(false);
    setSelectedHouseholds([]);
  };

  // Handle page size change
  const handlePageSizeChange = (size: number) => {
    onPageSizeChange(size);
    setPageInputValue("1");
  };

  // Handle page input change
  const handlePageInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPageInputValue(e.target.value);
  };

  // Handle go to page
  const handleGoToPage = () => {
    const pageNumber = Number.parseInt(pageInputValue);
    if (!isNaN(pageNumber) && pageNumber >= 1 && pageNumber <= pagination.totalPages) {
      onPageChange(pageNumber);
    } else {
      // Reset input to current page if invalid
      setPageInputValue(pagination.page.toString());
    }
  };

  // Render sort indicator
  const renderSortIndicator = (column: string) => {
    if (sortBy !== column) {
      return (
        <div className="ml-0.5 flex flex-col">
          <ChevronUp className="h-3 w-3 text-muted-foreground" />
          <ChevronDown className="-mt-1 h-3 w-3 text-muted-foreground" />
        </div>
      );
    }

    return sortOrder === "asc" ? (
      <ChevronUp className="ml-0.5 h-4 w-4 text-primary" />
    ) : (
      <ChevronDown className="ml-0.5 h-4 w-4 text-primary" />
    );
  };

  return (
    <div className="space-y-6">
      {/* Modern header section */}
      <div className="flex flex-col items-start justify-between gap-2 pb-2 sm:flex-row sm:items-center">
        <div className="space-y-1">
          <h2 className="font-semibold text-xl tracking-tight">{t("households")}</h2>
          <p className="text-muted-foreground text-sm">
            {pagination.total} {pagination.total === 1 ? t("household") : t("households")}{" "}
            {tCommon("found")}
          </p>
        </div>
        {selectedHouseholds.length > 0 && (
          <Button
            className="cursor-pointer"
            onClick={handleDeleteSelected}
            size="sm"
            variant="destructive"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            {t("deleteSelected")} ({selectedHouseholds.length})
          </Button>
        )}
      </div>

      {/* Table section */}
      <div className="space-y-4">
        {/* Table */}
        <div className="rounded-lg border border-border/50 bg-background/50 backdrop-blur-sm">
          <Table>
            <TableHeader>
              <TableRow className="border-b">
                <TableHead className="w-12">
                  <Checkbox
                    aria-label={t("selectAllHouseholds")}
                    checked={
                      households.length > 0 && selectedHouseholds.length === households.length
                    }
                    className="cursor-pointer"
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
                <TableHead
                  className="cursor-pointer text-center hover:bg-muted/50"
                  onClick={() => onSortChange("id")}
                >
                  <div className="flex items-center justify-center">
                    {tCommon("id")}
                    {renderSortIndicator("id")}
                  </div>
                </TableHead>
                <TableHead
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => onSortChange("unique_code")}
                >
                  <div className="flex items-center">
                    {t("uniqueCode")}
                    {renderSortIndicator("unique_code")}
                  </div>
                </TableHead>
                <TableHead
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => onSortChange("head_name")}
                >
                  <div className="flex items-center">
                    {t("householdHead")}
                    {renderSortIndicator("head_name")}
                  </div>
                </TableHead>
                <TableHead
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => onSortChange("head_contact")}
                >
                  <div className="flex items-center">
                    {tCommon("contact")}
                    {renderSortIndicator("head_contact")}
                  </div>
                </TableHead>
                <TableHead
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => onSortChange("suburb")}
                >
                  <div className="flex items-center">
                    {tCommon("suburb")}
                    {renderSortIndicator("suburb")}
                  </div>
                </TableHead>
                <TableHead
                  className="cursor-pointer text-center hover:bg-muted/50"
                  onClick={() => onSortChange("member_count")}
                >
                  <div className="flex items-center justify-center">
                    {t("members")}
                    {renderSortIndicator("member_count")}
                  </div>
                </TableHead>
                <TableHead
                  className="cursor-pointer text-center hover:bg-muted/50"
                  onClick={() => onSortChange("census_year")}
                >
                  <div className="flex items-center justify-center">
                    {t("censusYear")}
                    {renderSortIndicator("census_year")}
                  </div>
                </TableHead>
                <TableHead className="text-center">{tCommon("actions")}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                // Loading skeleton
                Array.from({
                  length: pagination.pageSize > 5 ? 5 : pagination.pageSize,
                }).map((_, index) => (
                  <TableRow className="border-b-0" key={index}>
                    <TableCell>
                      <Skeleton className="h-4 w-4" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-12" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-32" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-32" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-24" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-20" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-12" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-16" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-8" />
                    </TableCell>
                  </TableRow>
                ))
              ) : households.length === 0 ? (
                <TableRow className="border-b-0">
                  <TableCell className="py-8 text-center" colSpan={9}>
                    <div className="flex flex-col items-center gap-2">
                      <p className="text-muted-foreground">{tEmptyStates("noHouseholdsFound")}</p>
                      {searchTerm && (
                        <p className="text-muted-foreground text-sm">
                          {tEmptyStates("tryAdjustingSearch")}
                        </p>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                households.map((household) => (
                  <TableRow className="border-b-0 hover:bg-muted/50" key={household.id}>
                    <TableCell>
                      <Checkbox
                        aria-label={`Select household ${household.id}`}
                        checked={selectedHouseholds.includes(household.id)}
                        className="cursor-pointer"
                        onCheckedChange={(checked) =>
                          handleSelectHousehold(household.id, checked === true)
                        }
                      />
                    </TableCell>
                    <TableCell className="text-center">{household.id}</TableCell>
                    <TableCell className="font-medium">
                      <span className="font-mono text-sm">
                        {searchTerm
                          ? highlightText(
                              household.uniqueCode || tCommon("notAvailable"),
                              searchTerm,
                            )
                          : household.uniqueCode || tCommon("notAvailable")}
                      </span>
                    </TableCell>
                    <TableCell>
                      {searchTerm
                        ? highlightText(household.headName || tCommon("notAssigned"), searchTerm)
                        : household.headName || tCommon("notAssigned")}
                    </TableCell>
                    <TableCell>
                      {searchTerm
                        ? highlightText(
                            household.headContact || tCommon("notAvailable"),
                            searchTerm,
                          )
                        : household.headContact || tCommon("notAvailable")}
                    </TableCell>
                    <TableCell>
                      {searchTerm ? highlightText(household.suburb, searchTerm) : household.suburb}
                    </TableCell>
                    <TableCell className="text-center">{household.memberCount}</TableCell>
                    <TableCell className="text-center">
                      {activeCensusYear ? activeCensusYear.year : tCommon("notAvailable")}
                    </TableCell>
                    <TableCell className="text-center">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            aria-label={t("openActionsMenu")}
                            className="h-8 w-8 cursor-pointer"
                            size="icon"
                            variant="ghost"
                          >
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent
                          align="end"
                          onCloseAutoFocus={(e) => {
                            // Prevent focus returning to the trigger when menu closes
                            e.preventDefault();
                          }}
                        >
                          <DropdownMenuItem
                            className="cursor-pointer"
                            onClick={() => {
                              // Close the dropdown menu before opening the dialogue
                              document.body.click(); // Force close the dropdown
                              setTimeout(() => onView(household), 10);
                            }}
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            {tCommon("view")}
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="cursor-pointer"
                            onClick={() => {
                              // Close the dropdown menu before opening the dialogue
                              document.body.click(); // Force close the dropdown
                              setTimeout(() => onEdit(household), 10);
                            }}
                          >
                            <SquarePen className="mr-2 h-4 w-4" />
                            {tCommon("edit")}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Mobile-responsive pagination with rows per page selector */}
        {pagination.total > 0 && (
          <div className="w-full space-y-4 px-4 py-4 sm:flex sm:items-center sm:justify-between sm:space-y-0">
            {/* Rows per page selector */}
            <div className="flex items-center justify-center gap-2 sm:justify-start">
              <Label className="hidden font-medium text-sm sm:block" htmlFor="pageSize">
                {tCommon("rows")}:
              </Label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    className="flex h-9 items-center gap-2 bg-background/50 px-3 transition-colors hover:bg-background/80"
                    size="sm"
                    variant="outline"
                  >
                    {pagination.pageSize}
                    <ChevronDown className="h-4 w-4 opacity-70" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start">
                  <DropdownMenuItem
                    className="cursor-pointer"
                    onClick={() => handlePageSizeChange(10)}
                  >
                    10
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="cursor-pointer"
                    onClick={() => handlePageSizeChange(20)}
                  >
                    20
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="cursor-pointer"
                    onClick={() => handlePageSizeChange(50)}
                  >
                    50
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="cursor-pointer"
                    onClick={() => handlePageSizeChange(100)}
                  >
                    100
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Pagination controls */}
            <div className="flex justify-center sm:flex-1 sm:justify-center">
              <Pagination>
                <PaginationContent className="flex-wrap justify-center">
                  {/* First page button */}
                  <PaginationItem>
                    <Button
                      className="h-8 w-8"
                      disabled={pagination.page === 1}
                      onClick={() => onPageChange(1)}
                      size="icon"
                      title={tPagination("firstPage")}
                      variant="outline"
                    >
                      <span className="sr-only">{tPagination("firstPage")}</span>
                      <ChevronsLeft className="h-4 w-4" />
                    </Button>
                  </PaginationItem>

                  {/* Previous page button */}
                  <PaginationItem>
                    <Button
                      className="h-8 w-8"
                      disabled={pagination.page === 1}
                      onClick={() => onPageChange(Math.max(1, pagination.page - 1))}
                      size="icon"
                      title={tPagination("previousPage")}
                      variant="outline"
                    >
                      <span className="sr-only">{tPagination("previousPage")}</span>
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                  </PaginationItem>

                  {/* Page input and info */}
                  <PaginationItem>
                    <div className="flex items-center gap-1 sm:gap-2">
                      <Input
                        className="h-8 w-10 text-center text-xs sm:w-12 sm:text-sm"
                        onChange={handlePageInputChange}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            handleGoToPage();
                          }
                        }}
                        type="text"
                        value={pageInputValue}
                      />
                      <span className="whitespace-nowrap text-muted-foreground text-xs sm:text-sm">
                        of {pagination.totalPages}
                      </span>
                      <Button
                        className="h-8 px-2 text-xs sm:px-3 sm:text-sm"
                        onClick={handleGoToPage}
                        size="sm"
                        variant="outline"
                      >
                        Go
                      </Button>
                    </div>
                  </PaginationItem>

                  {/* Next page button */}
                  <PaginationItem>
                    <Button
                      className="h-8 w-8"
                      disabled={pagination.page === pagination.totalPages}
                      onClick={() =>
                        onPageChange(Math.min(pagination.totalPages, pagination.page + 1))
                      }
                      size="icon"
                      title={tPagination("nextPage")}
                      variant="outline"
                    >
                      <span className="sr-only">{tPagination("nextPage")}</span>
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </PaginationItem>

                  {/* Last page button */}
                  <PaginationItem>
                    <Button
                      className="h-8 w-8"
                      disabled={pagination.page === pagination.totalPages}
                      onClick={() => onPageChange(pagination.totalPages)}
                      size="icon"
                      title={tPagination("lastPage")}
                      variant="outline"
                    >
                      <span className="sr-only">{tPagination("lastPage")}</span>
                      <ChevronsRight className="h-4 w-4" />
                    </Button>
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          </div>
        )}
      </div>

      {/* Enhanced Bulk Delete Dialog */}
      <BulkDeleteHouseholdDialog
        householdIds={selectedHouseholds}
        onHouseholdsDeleted={confirmDeleteSelected}
        onOpenChange={setIsDeleteDialogOpen}
        open={isDeleteDialogOpen}
      />
    </div>
  );
}
