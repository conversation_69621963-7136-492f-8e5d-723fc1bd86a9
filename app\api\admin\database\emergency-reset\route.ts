import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { authOptions } from "@/lib/auth/auth-options";
import { prisma } from "@/lib/db/prisma";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";

/**
 * POST /api/admin/database/emergency-reset
 *
 * Emergency endpoint to safely reset the database connection pool
 * Uses a graceful approach without direct connections or KILL commands
 * Only accessible to admin users
 */
export async function POST(request: Request) {
	// Check authentication
	const session = await getServerSession(authOptions);

	// Require admin authentication
	if (!session || session.user.role !== "admin") {
		return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
	}

	// Get locale for translations
	const locale = await getLocaleFromCookies();
	const t = await getTranslations({ locale, namespace: "admin" });

	try {
		// Get IP address for audit log
		const ipAddress = request.headers.get("x-forwarded-for") || "unknown";

		// Log the action in the audit log using Prisma
		try {
			await prisma.auditLog
				?.create({
					data: {
						userType: "admin",
						userId: Number.parseInt(session.user.id, 10),
						action: "emergency-connection-reset",
						entityType: "database",
						entityId: 0,
						ipAddress,
					},
				})
				.catch(() => null);
		} catch (_logError) {}

		// Disconnect and reconnect Prisma client
		await prisma.$disconnect();

		// Test the connection with a simple query
		let connectionTest = "failed";
		let connectionMessage = "";

		try {
			await prisma.$queryRaw`SELECT 1 as test`;
			connectionTest = "success";
			connectionMessage =
				"Database connection has been successfully reset and tested";
		} catch (testError) {
			connectionMessage = `Connection test failed: ${testError instanceof Error ? testError.message : t("unknownError")}`;
		}

		return NextResponse.json({
			success: true,
			message: connectionMessage,
			connectionTest,
			timestamp: new Date().toISOString(),
		});
	} catch (error) {
		return NextResponse.json(
			{ error: t("unknownError"), details: (error as Error).message },
			{ status: 500 },
		);
	}
}
