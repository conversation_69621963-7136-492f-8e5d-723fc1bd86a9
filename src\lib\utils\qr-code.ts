/**
 * QR Code Utility
 *
 * This utility provides functions for generating styled QR codes with consistent branding.
 * It uses the qr-code-styling library for browser environments and qrcode for server environments.
 *
 * Features:
 * - Consistent styling with black color and rounded dots
 * - Centralized caching with TTL-based expiration
 * - Support for both server-side and client-side rendering
 * - Batch processing for efficient generation of multiple QR codes
 */

// Import types for the libraries
import type * as QRCodeType from "qrcode";
import appCache from "@/lib/cache";
import {
  TWO_FA_QR_CACHE_PREFIX,
  TWO_FA_QR_CACHE_TTL,
  UNIQUE_CODE_QR_CACHE_PREFIX,
  UNIQUE_CODE_QR_CACHE_TTL,
} from "@/lib/cache/qr-code-cache-config";

// Import qr-code-styling dynamically only in browser environments
// Define a type for QRCodeStyling to avoid using 'any'
interface QRCodeStylingConstructor {
  new (options: {
    width: number;
    height: number;
    type: string;
    data: string;
    dotsOptions?: {
      color: string;
      type: string;
    };
    backgroundOptions?: {
      color: string;
    };
    cornersSquareOptions?: {
      type: string;
      color: string;
    };
    cornersDotOptions?: {
      type: string;
      color: string;
    };
    qrOptions?: {
      errorCorrectionLevel: string;
    };
    [key: string]: unknown;
  }): {
    getRawData: (format: string) => Promise<Blob>;
  };
}

let QRCodeStyling: QRCodeStylingConstructor | undefined;
if (typeof window !== "undefined") {
  import("qr-code-styling").then((module) => {
    QRCodeStyling = module.default as QRCodeStylingConstructor;
  });
}

// Import the standard qrcode library for server-side rendering
// We'll use a dynamic import to avoid the deprecation warning
let QRCode: typeof QRCodeType | undefined;
if (typeof window === "undefined") {
  // Only import in server environment
  import("qrcode").then((module) => {
    QRCode = module.default;
  });
}

/**
 * Convert a Blob to a data URL
 * This is needed because qr-code-styling returns a Blob in browser environments
 */
async function blobToDataURL(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
}

/**
 * Check if we're in a browser environment
 */
function isBrowser(): boolean {
  return typeof window !== "undefined";
}

// Default QR code options
const defaultOptions = {
  width: 256,
  height: 256,
  type: "svg",
  data: "",
  dotsOptions: {
    color: "#000000", // Black color (changed from orange)
    type: "rounded",
  },
  backgroundOptions: {
    color: "#FFFFFF",
  },
  cornersSquareOptions: {
    type: "extra-rounded",
    color: "#000000", // Black color (changed from orange)
  },
  cornersDotOptions: {
    type: "dot",
    color: "#000000", // Black color (changed from orange)
  },
  qrOptions: {
    errorCorrectionLevel: "H",
  },
};

/**
 * Generate QR code for unique codes
 *
 * @param code The unique code
 * @param siteUrl The site URL
 * @param size Optional size override (default: 256)
 * @returns Promise<string> Data URL of the QR code
 */
export async function generateUniqueCodeQR(
  code: string,
  siteUrl: string,
  size?: number,
): Promise<string> {
  const url = `${siteUrl}/?code=${code}`;

  // Check cache first
  const cacheKey = `${UNIQUE_CODE_QR_CACHE_PREFIX}${code}-${size || 256}`;
  const cachedValue = appCache.get<string>(cacheKey);

  if (cachedValue) {
    return cachedValue;
  }

  try {
    // Use different approaches for browser and server
    if (isBrowser()) {
      // Browser environment - use qr-code-styling
      try {
        // Make sure QRCodeStyling is loaded
        if (!QRCodeStyling) {
          // Wait for dynamic import to complete
          QRCodeStyling = (await import("qr-code-styling")).default as QRCodeStylingConstructor;
        }

        // Create QR code with custom size if provided
        if (!QRCodeStyling) {
          throw new Error("QRCodeStyling is not loaded");
        }

        const qrCode = new QRCodeStyling({
          ...defaultOptions,
          data: url,
          width: size || defaultOptions.width,
          height: size || defaultOptions.height,
        });

        // Get data URL (returns a Blob in browser)
        const rawData = await qrCode.getRawData("svg");
        const dataUrl = await blobToDataURL(rawData as Blob);

        // Cache the result with appropriate TTL
        appCache.set(cacheKey, dataUrl, UNIQUE_CODE_QR_CACHE_TTL);

        return dataUrl;
      } catch (error) {
        console.error("Error generating QR code with qr-code-styling:", error);

        // Fallback to a simpler QR code
        const svgQR = `
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${size || 256} ${size || 256}">
            <rect width="${size || 256}" height="${size || 256}" fill="white" />
            <rect x="10%" y="10%" width="80%" height="80%" fill="#000000" />
            <rect x="20%" y="20%" width="60%" height="60%" fill="white" />
            <text x="50%" y="50%" font-family="sans-serif" font-size="12" text-anchor="middle" fill="#000000">
              ${code}
            </text>
          </svg>
        `;

        // Convert SVG to data URL - use Buffer.from instead of btoa for Node.js compatibility
        const dataUrl = `data:image/svg+xml;base64,${
          isBrowser() ? btoa(svgQR) : Buffer.from(svgQR).toString("base64")
        }`;

        // Cache the result with appropriate TTL
        appCache.set(cacheKey, dataUrl, UNIQUE_CODE_QR_CACHE_TTL);

        return dataUrl;
      }
    } else {
      // Server environment - use standard qrcode library
      try {
        // Make sure QRCode is loaded
        if (!QRCode) {
          // Wait for dynamic import to complete
          QRCode = (await import("qrcode")).default;
        }

        // Generate QR code with black color
        const dataUrl = await QRCode.toDataURL(url, {
          width: size || 256,
          margin: 0,
          errorCorrectionLevel: "H",
          color: {
            dark: "#000000", // Black color (changed from orange)
            light: "#FFFFFF",
          },
        });

        // Cache the result with appropriate TTL
        appCache.set(cacheKey, dataUrl, UNIQUE_CODE_QR_CACHE_TTL);

        return dataUrl;
      } catch (error) {
        console.error("Error generating QR code with qrcode library:", error);
        throw error;
      }
    }
  } catch (error) {
    console.error("Error generating QR code:", error);
    throw new Error("Failed to generate QR code");
  }
}

/**
 * Generate QR code for 2FA
 *
 * @param secret The TOTP secret
 * @param username The username
 * @param issuer The issuer name
 * @returns Promise<string> Data URL of the QR code
 */
export async function generate2FAQR(
  secret: string,
  username: string,
  issuer = "WSCCC Census",
): Promise<string> {
  const otpauth = `otpauth://totp/${encodeURIComponent(issuer)}:${encodeURIComponent(username)}?secret=${secret}&issuer=${encodeURIComponent(issuer)}`;

  // Check cache first
  const cacheKey = `${TWO_FA_QR_CACHE_PREFIX}${username}-${secret}`;
  const cachedValue = appCache.get<string>(cacheKey);

  if (cachedValue) {
    return cachedValue;
  }

  try {
    // Use different approaches for browser and server
    if (isBrowser()) {
      // Browser environment - use qr-code-styling
      try {
        // Make sure QRCodeStyling is loaded
        if (!QRCodeStyling) {
          // Wait for dynamic import to complete
          QRCodeStyling = (await import("qr-code-styling")).default as QRCodeStylingConstructor;
        }

        // Create QR code
        if (!QRCodeStyling) {
          throw new Error("QRCodeStyling is not loaded");
        }

        const qrCode = new QRCodeStyling({
          ...defaultOptions,
          data: otpauth,
        });

        // Get data URL (returns a Blob in browser)
        const rawData = await qrCode.getRawData("svg");
        const dataUrl = await blobToDataURL(rawData as Blob);

        // Cache the result with appropriate TTL
        appCache.set(cacheKey, dataUrl, TWO_FA_QR_CACHE_TTL);

        return dataUrl;
      } catch (error) {
        console.error("Error generating 2FA QR code with qr-code-styling:", error);

        // Fallback to a simpler QR code with consistent styling
        const svgQR = `
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256">
            <rect width="256" height="256" fill="white" />
            <rect x="10%" y="10%" width="80%" height="80%" fill="#000000" />
            <rect x="20%" y="20%" width="60%" height="60%" fill="white" />
            <text x="50%" y="50%" font-family="sans-serif" font-size="12" text-anchor="middle" fill="#000000">
              Scan with authenticator app
            </text>
            <text x="50%" y="65%" font-family="sans-serif" font-size="10" text-anchor="middle" fill="#000000">
              Secret: ${secret}
            </text>
          </svg>
        `;

        // Convert SVG to data URL - use Buffer.from instead of btoa for Node.js compatibility
        const dataUrl = `data:image/svg+xml;base64,${
          isBrowser() ? btoa(svgQR) : Buffer.from(svgQR).toString("base64")
        }`;

        // Cache the result with appropriate TTL
        appCache.set(cacheKey, dataUrl, TWO_FA_QR_CACHE_TTL);

        return dataUrl;
      }
    } else {
      // Server environment - use standard qrcode library
      try {
        // Make sure QRCode is loaded
        if (!QRCode) {
          // Wait for dynamic import to complete
          QRCode = (await import("qrcode")).default;
        }

        // Generate QR code with black color
        const dataUrl = await QRCode.toDataURL(otpauth, {
          width: 256,
          margin: 0,
          errorCorrectionLevel: "H",
          color: {
            dark: "#000000", // Black color (changed from orange)
            light: "#FFFFFF",
          },
        });

        // Cache the result with appropriate TTL
        appCache.set(cacheKey, dataUrl, TWO_FA_QR_CACHE_TTL);

        return dataUrl;
      } catch (error) {
        console.error("Error generating 2FA QR code with qrcode library:", error);
        throw error;
      }
    }
  } catch (error) {
    console.error("Error generating 2FA QR code:", error);
    throw new Error("Failed to generate 2FA QR code");
  }
}

/**
 * Generate multiple QR codes in batch
 *
 * @param codes Array of unique codes
 * @param siteUrl The site URL
 * @param size Optional size override (default: 256)
 * @returns Promise<Record<string, string>> Map of code to QR code data URL
 */
export async function generateBatchQRCodes(
  codes: { code: string }[],
  siteUrl: string,
  size?: number,
): Promise<Record<string, string>> {
  try {
    // Process in batches of 10 to avoid overwhelming the browser
    const batchSize = 10;
    const qrCodeMap: Record<string, string> = {};

    // Process codes in batches
    for (let i = 0; i < codes.length; i += batchSize) {
      const batch = codes.slice(i, i + batchSize);

      // Process batch in parallel
      const batchPromises = batch.map(async ({ code }) => {
        try {
          const qrDataUrl = await generateUniqueCodeQR(code, siteUrl, size);
          return { code, qrDataUrl };
        } catch (error) {
          console.error(`Error generating QR code for ${code}:`, error);
          return { code, qrDataUrl: "" };
        }
      });

      // Wait for batch to complete
      const batchResults = await Promise.all(batchPromises);

      // Add results to map
      batchResults.forEach((result) => {
        qrCodeMap[result.code] = result.qrDataUrl;
      });
    }

    return qrCodeMap;
  } catch (error) {
    console.error("Error generating batch QR codes:", error);
    return {};
  }
}

/**
 * Clear QR code cache
 *
 * @param type Optional cache type to clear ('unique', '2fa', or 'all')
 * @param key Optional specific key within the type (e.g., a specific code)
 * @returns Number of cache entries cleared
 */
export function clearQRCodeCache(type: "unique" | "2fa" | "all" = "all", key?: string): number {
  let clearedCount = 0;

  if (type === "unique" || type === "all") {
    if (key) {
      // Clear specific unique code QR
      const uniqueCodeKey = `${UNIQUE_CODE_QR_CACHE_PREFIX}${key}`;
      // Try both with and without size suffix
      if (appCache.delete(`${uniqueCodeKey}-256`)) clearedCount++;
      if (appCache.delete(`${uniqueCodeKey}-128`)) clearedCount++;
    } else {
      // Clear all unique code QRs
      clearedCount += appCache.deleteByPrefix(UNIQUE_CODE_QR_CACHE_PREFIX);
    }
  }

  if (type === "2fa" || type === "all") {
    if (key) {
      // Clear specific 2FA QR by username
      // Note: This will clear all 2FA QRs for this username regardless of secret
      const twoFAKeys = appCache.getKeysByPrefix(`${TWO_FA_QR_CACHE_PREFIX}${key}`);
      twoFAKeys.forEach((k) => {
        if (appCache.delete(k)) clearedCount++;
      });
    } else {
      // Clear all 2FA QRs
      clearedCount += appCache.deleteByPrefix(TWO_FA_QR_CACHE_PREFIX);
    }
  }

  return clearedCount;
}
