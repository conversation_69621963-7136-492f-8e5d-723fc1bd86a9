"use client";

import { usePathname } from "next/navigation";
import { useLocale, useTranslations } from "next-intl";
import { useCallback, useMemo } from "react";
import { useAlert } from "@/contexts/AlertContext";
import { useToastTranslation } from "@/hooks/useToastTranslation";
import { infoMessageKeys } from "@/lib/messages/info-messages";
// Import message key mappings
import { successMessageKeys } from "@/lib/messages/success-messages";
import { warningMessageKeys } from "@/lib/messages/warning-messages";

/**
 * Unified message hook with automatic auth context detection
 * Provides consistent interface for all message types while maintaining
 * complete separation between admin and census authentication systems
 */
export function useMessage() {
	const { showAlert } = useAlert();
	const pathname = usePathname();
	const locale = useLocale();

	// Translation hooks for different namespaces
	const tAuth = useTranslations("auth");
	const tNotifications = useTranslations("notifications");
	const tWarnings = useTranslations("warnings");
	const tCommon = useTranslations("common");
	const tErrors = useTranslations("errors");

	// Enhanced translation system
	const { translateError } = useToastTranslation();

	/**
	 * Automatically detect auth context based on current path
	 * This ensures proper message routing and cookie handling
	 * Memoized to prevent unnecessary re-renders
	 */
	const authContext = useMemo((): "admin" | "census" => {
		// Primary detection: pathname-based
		if (pathname.startsWith("/admin")) return "admin";
		if (pathname.startsWith("/census")) return "census";

		// Fallback: assume census for public pages (safer default)
		return "census";
	}, [pathname]);

	/**
	 * Show success message with automatic translation
	 * @param messageKey - Success message key from successMessageKeys
	 */
	const showSuccess = useCallback(
		(messageKey: string) => {
			const translationKey =
				successMessageKeys[messageKey] || successMessageKeys.default;
			const message = tNotifications(translationKey as any);
			showAlert("success", message);
		},
		[tNotifications, showAlert],
	);

	/**
	 * Show error message with context-aware translation
	 * @param errorCode - Error code to translate
	 * @param errorType - Optional error type override
	 */
	const showError = useCallback(
		(errorCode: string, errorType?: "auth" | "census" | "settings") => {
			// Determine error type from context if not provided
			const type = errorType || (authContext === "admin" ? "auth" : "census");
			const message = translateError(errorCode, type);
			showAlert("error", message);
		},
		[authContext, translateError, showAlert],
	);

	/**
	 * Show warning message with automatic translation
	 * @param messageKey - Warning message key from warningMessageKeys
	 */
	const showWarning = useCallback(
		(messageKey: string) => {
			const translationKey =
				warningMessageKeys[messageKey] || warningMessageKeys.default;
			const message = tWarnings(translationKey as any);
			showAlert("warning", message);
		},
		[tWarnings, showAlert],
	);

	/**
	 * Show info message with automatic translation
	 * @param messageKey - Info message key from infoMessageKeys
	 */
	const showInfo = useCallback(
		(messageKey: string) => {
			const translationKey =
				infoMessageKeys[messageKey] || infoMessageKeys.default;
			const message = tCommon(translationKey as any);
			showAlert("info", message);
		},
		[tCommon, showAlert],
	);

	/**
	 * Legacy compatibility: direct message display
	 * @param type - Message type
	 * @param message - Direct message string
	 */
	const showDirect = useCallback(
		(type: "success" | "error" | "warning" | "info", message: string) => {
			showAlert(type, message);
		},
		[showAlert],
	);

	return {
		// Primary message functions
		showSuccess,
		showError,
		showWarning,
		showInfo,

		// Legacy compatibility
		showDirect,
		showAlert, // Direct access to AlertContext

		// Context information
		authContext,
		locale,

		// Utility functions
		isAdmin: authContext === "admin",
		isCensus: authContext === "census",
	};
}
