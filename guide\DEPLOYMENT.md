# WSCCC Census System Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the WSCCC Census System to various environments, including VPS, cloud platforms, and local servers.

## Prerequisites

### System Requirements
- **Node.js**: v22.14.0 or higher
- **npm**: v10.9.2 or higher
- **PostgreSQL**: v15 or higher
- **Memory**: Minimum 2GB RAM (4GB recommended)
- **Storage**: Minimum 10GB available space

### Required Software
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y nodejs npm postgresql postgresql-contrib nginx certbot python3-certbot-nginx

# CentOS/RHEL
sudo yum install -y nodejs npm postgresql postgresql-server nginx certbot python3-certbot-nginx
```

## Environment Configuration

### Environment Variables

Create `.env.local` file with the following variables:

```bash
# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/wsccc_census"
POSTGRES_URL_NON_POOLING="postgresql://username:password@localhost:5432/wsccc_census"

# Authentication Secrets (Generate unique values)
NEXTAUTH_SECRET_ADMIN="your-admin-secret-here"
NEXTAUTH_SECRET_CENSUS="your-census-secret-here"

# Application URLs
NEXTAUTH_URL="https://your-domain.com"
NEXT_PUBLIC_APP_URL="https://your-domain.com"

# AI Integration (Optional)
GOOGLE_GEMINI_API_KEY="your-gemini-api-key"

# Security Configuration
NODE_ENV="production"
```

### Generate Secrets
```bash
# Generate secure secrets
openssl rand -base64 32  # For NEXTAUTH_SECRET_ADMIN
openssl rand -base64 32  # For NEXTAUTH_SECRET_CENSUS
```

## Database Setup

### PostgreSQL Installation and Configuration

#### 1. Install PostgreSQL
```bash
# Ubuntu/Debian
sudo apt install postgresql postgresql-contrib

# Start and enable PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### 2. Create Database and User
```bash
# Switch to postgres user
sudo -u postgres psql

# Create database and user
CREATE DATABASE wsccc_census;
CREATE USER wsccc_user WITH ENCRYPTED PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE wsccc_census TO wsccc_user;
\q
```

#### 3. Configure PostgreSQL
```bash
# Edit postgresql.conf
sudo nano /etc/postgresql/15/main/postgresql.conf

# Add/modify these settings:
listen_addresses = 'localhost'
max_connections = 100
shared_buffers = 256MB
effective_cache_size = 1GB
```

#### 4. Apply Database Schema
```bash
# Navigate to project directory
cd /path/to/wsccc-census-system

# Apply schema
psql -d wsccc_census -f database-postgresql.sql
```

## Application Deployment

### Method 1: Direct VPS Deployment

#### 1. Clone Repository
```bash
# Clone from GitHub
git clone https://github.com/Ayuyyae/wsccc-census-system.git
cd wsccc-census-system
```

#### 2. Install Dependencies
```bash
npm install
```

#### 3. Build Application
```bash
# For systems with sufficient memory
npm run build

# For memory-constrained systems (Oracle VPS)
# Build locally and upload .next folder via SCP
```

#### 4. Setup PM2 Process Manager
```bash
# Install PM2 globally
npm install -g pm2

# Create PM2 ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'wsccc-census',
    script: 'npm',
    args: 'start',
    cwd: '/var/www/wsccc-census',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G'
  }]
};
EOF

# Start application
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### Method 2: Docker Deployment

#### 1. Create Dockerfile
```dockerfile
FROM node:22-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy application code
COPY . .

# Build application
RUN npm run build

# Expose port
EXPOSE 3000

# Start application
CMD ["npm", "start"]
```

#### 2. Create docker-compose.yml
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=****************************************/wsccc_census
    depends_on:
      - db
    restart: unless-stopped

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=wsccc_census
      - POSTGRES_USER=wsccc_user
      - POSTGRES_PASSWORD=secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database-postgresql.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

volumes:
  postgres_data:
```

#### 3. Deploy with Docker
```bash
# Build and start services
docker-compose up -d

# View logs
docker-compose logs -f app
```

## Web Server Configuration

### Nginx Setup

#### 1. Install and Configure Nginx
```bash
# Install Nginx
sudo apt install nginx

# Create site configuration
sudo nano /etc/nginx/sites-available/wsccc-census
```

#### 2. Nginx Configuration
```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;

    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    # Proxy to Next.js application
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Static file caching
    location /_next/static/ {
        proxy_pass http://localhost:3000;
        add_header Cache-Control "public, max-age=31536000, immutable";
    }
}
```

#### 3. Enable Site and Restart Nginx
```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/wsccc-census /etc/nginx/sites-enabled/

# Test configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
```

### SSL Certificate Setup

#### 1. Install Certbot
```bash
sudo apt install certbot python3-certbot-nginx
```

#### 2. Obtain SSL Certificate
```bash
# Get certificate for your domain
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Test automatic renewal
sudo certbot renew --dry-run
```

## Firewall Configuration

### UFW (Ubuntu Firewall)
```bash
# Install UFW if not present
sudo apt install ufw

# Configure firewall rules
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw allow 5432  # PostgreSQL (if external access needed)

# Enable firewall
sudo ufw enable

# Check status
sudo ufw status
```

## Automated Deployment Script

### Create Deployment Script
```bash
#!/bin/bash
# deploy.sh - Automated deployment script

set -e

PROJECT_DIR="/var/www/wsccc-census"
BACKUP_DIR="/var/backups/wsccc-census"
DATE=$(date +%Y%m%d_%H%M%S)

echo "🚀 Starting WSCCC Census System deployment..."

# Create backup
echo "📦 Creating backup..."
mkdir -p $BACKUP_DIR
tar -czf $BACKUP_DIR/backup_$DATE.tar.gz -C $PROJECT_DIR .

# Stop application
echo "⏹️ Stopping application..."
pm2 stop wsccc-census || true

# Update code
echo "📥 Updating code..."
cd $PROJECT_DIR
git pull origin main

# Install dependencies
echo "📦 Installing dependencies..."
read -p "Reinstall dependencies? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    npm install
fi

# Build application
echo "🔨 Building application..."
read -p "Rebuild application? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    npm run build
fi

# Database migrations
echo "🗄️ Running database migrations..."
npm run db:push

# Set permissions
echo "🔐 Setting permissions..."
chown -R www-data:www-data $PROJECT_DIR
chmod -R 755 $PROJECT_DIR

# Start application
echo "▶️ Starting application..."
pm2 start wsccc-census
pm2 save

# Verify deployment
echo "✅ Verifying deployment..."
sleep 5
if pm2 list | grep -q "wsccc-census.*online"; then
    echo "✅ Deployment successful!"
else
    echo "❌ Deployment failed!"
    exit 1
fi

echo "🎉 Deployment completed successfully!"
```

### Make Script Executable
```bash
chmod +x deploy.sh
sudo ./deploy.sh
```

## Monitoring and Maintenance

### Log Management
```bash
# PM2 logs
pm2 logs wsccc-census

# Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# System logs
journalctl -u nginx -f
```

### Database Backup
```bash
# Create backup script
cat > /usr/local/bin/backup-wsccc-db.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/var/backups/wsccc-census/db"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR

pg_dump -h localhost -U wsccc_user wsccc_census | gzip > $BACKUP_DIR/wsccc_census_$DATE.sql.gz

# Keep only last 30 days of backups
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete
EOF

chmod +x /usr/local/bin/backup-wsccc-db.sh

# Add to crontab for daily backups
echo "0 2 * * * /usr/local/bin/backup-wsccc-db.sh" | sudo crontab -
```

### Health Checks
```bash
# Create health check script
cat > /usr/local/bin/health-check.sh << 'EOF'
#!/bin/bash
if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
    echo "✅ Application is healthy"
else
    echo "❌ Application is down, restarting..."
    pm2 restart wsccc-census
fi
EOF

chmod +x /usr/local/bin/health-check.sh

# Add to crontab for every 5 minutes
echo "*/5 * * * * /usr/local/bin/health-check.sh" | crontab -
```

## Security Considerations

### System Hardening
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Configure automatic security updates
sudo apt install unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades

# Disable root login
sudo passwd -l root

# Configure SSH security
sudo nano /etc/ssh/sshd_config
# Set: PermitRootLogin no, PasswordAuthentication no
sudo systemctl restart ssh
```

### Application Security
- Use strong, unique secrets for authentication
- Enable HTTPS with proper SSL certificates
- Configure proper firewall rules
- Regular security updates
- Monitor application logs for suspicious activity

This comprehensive deployment guide ensures a secure, scalable, and maintainable production deployment of the WSCCC Census System.
