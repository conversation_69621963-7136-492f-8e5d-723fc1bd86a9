"use client";

import {
	Legend,
	PolarAngleAxis,
	PolarGrid,
	PolarRadiusAxis,
	Radar,
	RadarChart,
	ResponsiveContainer,
	Tooltip,
} from "recharts";
import { type BaseChartProps, registerChart } from "./chart-registry";
import { CHART_COLORS, CHART_DEFAULTS } from "./constants";

interface RadarDataPoint {
	name: string;
	[key: string]: unknown;
}

function RadarChartComponent({
	data,
	isAnimationActive = true,
	className,
}: BaseChartProps) {
	const config = data.config || {};
	const { metrics = [], showLegend = true } = config;

	// If no metrics provided, try to infer from data with validation
	const radarMetrics =
		metrics.length > 0
			? metrics
			: (() => {
					const firstItem = data.data[0];
					if (!firstItem || typeof firstItem !== "object") {
						return [];
					}
					return Object.keys(firstItem).filter(
						(key) => key !== "name" && typeof firstItem[key] === "number",
					);
				})();

	// Guard against empty or non-numeric datasets
	if (radarMetrics.length === 0) {
		return (
			<div
				className={`flex h-96 w-full items-center justify-center ${className || ""}`}
			>
				<div className="text-center text-slate-500 dark:text-slate-400">
					<div className="mb-2 font-medium text-lg">No Data Available</div>
					<div className="text-sm">
						No numeric metrics found for radar chart
					</div>
				</div>
			</div>
		);
	}

	// Transform data for radar chart
	// Each metric becomes a point on the radar, each data item becomes a series
	const radarData = radarMetrics.map((metric) => {
		const point: Record<string, unknown> = { metric };
		data.data.forEach((item) => {
			const radarItem = item as RadarDataPoint;
			if (!radarItem || typeof radarItem.name !== "string") {
				return;
			}
			const raw = radarItem[metric];
			point[radarItem.name] = typeof raw === "number" ? raw : 0;
		});
		return point;
	});

	// Get series names (data item names) with validation
	const seriesNames = data.data
		.map((item) => {
			const radarItem = item as RadarDataPoint;
			if (!radarItem || typeof radarItem.name !== "string") {
				return null;
			}
			return radarItem.name;
		})
		.filter((name): name is string => name !== null);

	// Custom tooltip
	const CustomTooltip = ({
		active,
		payload,
		label,
	}: {
		active?: boolean;
		payload?: Array<{ color: string; dataKey: string; value: number | string }>;
		label?: string;
	}) => {
		if (active && payload && payload.length) {
			// Validate that we have valid data entries
			const validEntries = payload.filter(
				(entry) => entry && entry.value !== undefined && entry.value !== null,
			);
			if (validEntries.length === 0) {
				return null;
			}

			return (
				<div className="rounded-lg border border-slate-200 bg-white p-3 shadow-lg dark:border-slate-600 dark:bg-slate-800">
					<p className="mb-2 font-medium text-slate-900 dark:text-slate-100">
						{label}
					</p>
					<div className="space-y-1">
						{validEntries.map(
							(
								entry: {
									color: string;
									dataKey: string;
									value: number | string;
								},
								index: number,
							) => (
								<div className="flex items-center gap-2 text-sm" key={index}>
									<div
										className="h-3 w-3 rounded-sm"
										style={{ backgroundColor: entry.color }}
									/>
									<span className="text-slate-600 dark:text-slate-400">
										{entry.dataKey}:
									</span>
									<span className="font-medium text-slate-900 dark:text-slate-100">
										{typeof entry.value === "number"
											? entry.value.toLocaleString()
											: entry.value}
									</span>
								</div>
							),
						)}
					</div>
				</div>
			);
		}
		return null;
	};

	// Custom legend
	const CustomLegend = ({
		payload,
	}: {
		payload?: Array<{ color: string; value: string }>;
	}) => {
		if (!(showLegend && payload)) {
			return null;
		}

		return (
			<div className="mt-4 flex flex-wrap justify-center gap-4 text-sm">
				{payload.map(
					(entry: { color: string; value: string }, index: number) => (
						<div className="flex items-center gap-2" key={index}>
							<div
								className="h-3 w-3 rounded-sm"
								style={{ backgroundColor: entry.color }}
							/>
							<span className="text-slate-700 dark:text-slate-300">
								{entry.value}
							</span>
						</div>
					),
				)}
			</div>
		);
	};

	return (
		<div className={`w-full ${className || ""}`}>
			<ResponsiveContainer height={CHART_DEFAULTS.HEIGHT} width="100%">
				<RadarChart data={radarData} margin={CHART_DEFAULTS.MARGINS.DEFAULT}>
					<PolarGrid className="opacity-30" />
					<PolarAngleAxis
						className="text-xs"
						dataKey="metric"
						tick={{ fontSize: 11, fill: "currentColor" }}
					/>
					<PolarRadiusAxis
						className="text-xs"
						tick={{ fontSize: 10, fill: "currentColor" }}
						tickCount={5}
					/>
					<Tooltip content={<CustomTooltip />} />
					{showLegend && <Legend content={<CustomLegend />} />}

					{/* Render radar areas for each series */}
					{seriesNames.map((seriesName, index) => (
						<Radar
							animationBegin={index * 200}
							animationDuration={CHART_DEFAULTS.ANIMATION_DURATION}
							dataKey={seriesName}
							fill={CHART_COLORS.PALETTE[index % CHART_COLORS.PALETTE.length]}
							fillOpacity={0.3}
							isAnimationActive={isAnimationActive}
							key={seriesName}
							name={seriesName}
							stroke={CHART_COLORS.PALETTE[index % CHART_COLORS.PALETTE.length]}
							strokeWidth={2}
						/>
					))}
				</RadarChart>
			</ResponsiveContainer>
		</div>
	);
}

// Register the component
registerChart("radar", RadarChartComponent);

export default RadarChartComponent;
