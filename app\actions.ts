"use server";

import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { authErrorKeys } from "@/lib/errors/auth-errors";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";

/**
 * Server action to set an auth toast message and redirect to login page
 */
export async function setAuthToastAndRedirect(
	type: "success" | "error" | "info" | "warning",
	messageKey: string,
	redirectUrl = "/admin/login",
) {
	// Get locale from centralized utility at the beginning
	const locale = await getLocaleFromCookies();

	// Set a secure HTTP-only cookie with the toast message
	const cookieStore = await cookies();
	const t = await getTranslations({ locale, namespace: "auth" });
	const translationKey = authErrorKeys[messageKey] || authErrorKeys.default;
	const translatedMessage = t(translationKey as any);

	cookieStore.set(
		"auth_toast",
		JSON.stringify({
			type,
			message: translatedMessage,
		}),
		{
			httpOnly: true,
			secure: process.env.NODE_ENV === "production",
			maxAge: 10, // 10 seconds - short expiry prevents persistence issues
			path: "/",
		},
	);

	// Redirect to the specified URL
	redirect(redirectUrl);
}
