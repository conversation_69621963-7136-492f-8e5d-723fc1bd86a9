import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

console.log('🚀 Translation File Organizer & Verifier');
console.log('========================================\n');

/**
 * Sort object keys alphabetically (recursive)
 */
function sortObjectKeys(obj) {
  if (typeof obj !== 'object' || obj === null || Array.isArray(obj)) {
    return obj;
  }

  const sortedObj = {};
  const sortedKeys = Object.keys(obj).sort((a, b) =>
    a.localeCompare(b, 'en', { sensitivity: 'base' })
  );

  for (const key of sortedKeys) {
    sortedObj[key] = sortObjectKeys(obj[key]);
  }

  return sortedObj;
}

/**
 * Check if array is sorted alphabetically
 */
function isSorted(arr) {
  for (let i = 1; i < arr.length; i++) {
    if (arr[i].localeCompare(arr[i - 1], 'en', { sensitivity: 'base' }) < 0) {
      return false;
    }
  }
  return true;
}

/**
 * Find unsorted keys
 */
function findUnsortedKeys(keys) {
  const unsorted = [];
  for (let i = 1; i < keys.length; i++) {
    if (keys[i].localeCompare(keys[i - 1], 'en', { sensitivity: 'base' }) < 0) {
      unsorted.push({
        key: keys[i],
        position: i,
        shouldComeAfter: keys[i - 1],
      });
    }
  }
  return unsorted;
}

/**
 * Validate sorting recursively
 */
function validateSorting(obj, path = '') {
  const issues = [];

  if (typeof obj !== 'object' || obj === null || Array.isArray(obj)) {
    return issues;
  }

  const keys = Object.keys(obj);
  const unsortedKeys = findUnsortedKeys(keys);

  if (unsortedKeys.length > 0) {
    issues.push({
      path: path || 'root',
      unsortedKeys,
      allKeys: keys,
    });
  }

  // Check nested objects
  for (const key of keys) {
    const newPath = path ? `${path}.${key}` : key;
    if (
      typeof obj[key] === 'object' &&
      obj[key] !== null &&
      !Array.isArray(obj[key])
    ) {
      issues.push(...validateSorting(obj[key], newPath));
    }
  }

  return issues;
}

/**
 * Get statistics
 */
function getStats(data) {
  const stats = {
    namespaces: Object.keys(data).length,
    totalKeys: 0,
    namespaceDetails: {},
  };

  for (const namespace in data) {
    if (typeof data[namespace] === 'object' && data[namespace] !== null) {
      const keyCount = Object.keys(data[namespace]).length;
      stats.totalKeys += keyCount;
      stats.namespaceDetails[namespace] = keyCount;
    }
  }

  return stats;
}

/**
 * Compare two files
 */
function compareFiles(data1, data2, file1Name, file2Name) {
  const comparison = {
    missingNamespaces: [],
    missingKeys: [],
    structureMatch: true,
  };

  const namespaces1 = new Set(Object.keys(data1));
  const namespaces2 = new Set(Object.keys(data2));

  // Find missing namespaces
  const onlyIn1 = [...namespaces1].filter((ns) => !namespaces2.has(ns));
  const onlyIn2 = [...namespaces2].filter((ns) => !namespaces1.has(ns));

  if (onlyIn1.length > 0) {
    comparison.missingNamespaces.push({ file: file2Name, missing: onlyIn1 });
    comparison.structureMatch = false;
  }

  if (onlyIn2.length > 0) {
    comparison.missingNamespaces.push({ file: file1Name, missing: onlyIn2 });
    comparison.structureMatch = false;
  }

  // Compare common namespaces
  const commonNamespaces = [...namespaces1].filter((ns) => namespaces2.has(ns));

  for (const namespace of commonNamespaces) {
    const keys1 = new Set(Object.keys(data1[namespace] || {}));
    const keys2 = new Set(Object.keys(data2[namespace] || {}));

    const onlyIn1Keys = [...keys1].filter((key) => !keys2.has(key));
    const onlyIn2Keys = [...keys2].filter((key) => !keys1.has(key));

    if (onlyIn1Keys.length > 0) {
      comparison.missingKeys.push({
        namespace,
        file: file2Name,
        missing: onlyIn1Keys,
      });
      comparison.structureMatch = false;
    }

    if (onlyIn2Keys.length > 0) {
      comparison.missingKeys.push({
        namespace,
        file: file1Name,
        missing: onlyIn2Keys,
      });
      comparison.structureMatch = false;
    }
  }

  return comparison;
}

/**
 * Process a single file for sorting
 */
function processFile(filePath) {
  console.log(`📁 Processing: ${filePath}`);

  try {
    // Read original file
    const originalData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    console.log(`✅ Read ${filePath} successfully`);

    // Create backup
    const backupPath = filePath + '.backup';
    fs.copyFileSync(filePath, backupPath);
    console.log(`💾 Backup created: ${backupPath}`);

    // Sort the data
    const sortedData = sortObjectKeys(originalData);
    console.log('🔄 Sorted data structure');

    // Write sorted file
    const jsonString = JSON.stringify(sortedData, null, 2);
    fs.writeFileSync(filePath, jsonString + '\n', 'utf8');
    console.log(`✅ Successfully sorted: ${filePath}`);

    return { success: true, data: sortedData };
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Verify a single file
 */
function verifyFile(filePath, data = null) {
  try {
    const fileData = data || JSON.parse(fs.readFileSync(filePath, 'utf8'));
    const issues = validateSorting(fileData);
    const stats = getStats(fileData);

    return {
      file: filePath,
      issues,
      stats,
      success: true,
      data: fileData,
    };
  } catch (error) {
    return {
      file: filePath,
      issues: [],
      stats: null,
      success: false,
      error: error.message,
    };
  }
}

// Main execution
const files = [
  path.join(projectRoot, 'lang/en.json'),
  path.join(projectRoot, 'lang/zh-CN.json'),
];
const mode = process.argv[2] || 'both'; // 'sort', 'verify', or 'both'

if (mode === 'sort' || mode === 'both') {
  console.log('🔄 STEP 1: Sorting translation files...\n');

  let sortSuccessCount = 0;
  const sortedData = {};

  for (const file of files) {
    const result = processFile(file);
    if (result.success) {
      sortSuccessCount++;
      sortedData[file] = result.data;
    }
    console.log(''); // Empty line for readability
  }

  console.log(
    `🎉 Sorting completed! ${sortSuccessCount}/${files.length} files processed successfully.\n`
  );
}

if (mode === 'verify' || mode === 'both') {
  console.log('🔍 STEP 2: Verifying translation file sorting...\n');

  const results = [];
  const fileData = {};

  for (const filePath of files) {
    console.log(`📁 Validating: ${filePath}`);

    const result = verifyFile(filePath);
    results.push(result);

    if (result.success) {
      fileData[filePath] = result.data;

      if (result.issues.length === 0) {
        console.log(`✅ Perfectly sorted: ${filePath}`);
      } else {
        console.log(
          `⚠️  Found ${result.issues.length} sorting issue(s): ${filePath}`
        );
      }
    } else {
      console.error(`❌ Error reading ${filePath}:`, result.error);
    }
  }

  // Compare files
  let comparison = null;
  if (results.length === 2 && results.every((r) => r.success)) {
    console.log('\n🔄 Comparing file structures...');
    comparison = compareFiles(
      fileData[files[0]],
      fileData[files[1]],
      files[0],
      files[1]
    );

    if (comparison.structureMatch) {
      console.log('✅ Files have identical structure!');
    } else {
      console.log('⚠️  Files have structural differences');
    }
  }

  // Generate report
  console.log('\n📊 Generating detailed report...');

  let report = '# Translation Sorting Verification Report\n\n';
  report += `**Generated:** ${new Date().toISOString()}\n`;
  report += `**Mode:** ${mode}\n\n`;

  // Summary
  report += '## Summary\n\n';
  report += '| File | Status | Namespaces | Total Keys | Issues |\n';
  report += '|------|--------|------------|------------|--------|\n';

  for (const result of results) {
    const status = result.success
      ? result.issues.length === 0
        ? '✅ Perfect'
        : '⚠️ Issues'
      : '❌ Error';
    const namespaces = result.stats ? result.stats.namespaces : 'N/A';
    const totalKeys = result.stats ? result.stats.totalKeys : 'N/A';
    const issues = result.issues.length;

    report += `| ${result.file} | ${status} | ${namespaces} | ${totalKeys} | ${issues} |\n`;
  }

  // Save report
  const reportPath = 'sorting_verification_report.md';
  try {
    fs.writeFileSync(reportPath, report, 'utf8');
    console.log(`📄 Report saved: ${reportPath}`);
  } catch (error) {
    console.error('❌ Failed to save report:', error.message);
  }

  // Final summary
  const totalIssues = results.reduce(
    (sum, result) => sum + result.issues.length,
    0
  );
  const successCount = results.filter((r) => r.success).length;

  console.log('\n🎉 Verification completed!');
  console.log(`📊 Files processed: ${successCount}/${results.length}`);
  console.log(`🔍 Total sorting issues: ${totalIssues}`);
  console.log(
    `🏗️  Structure match: ${comparison?.structureMatch ? 'Yes' : 'No'}`
  );

  if (totalIssues === 0 && comparison?.structureMatch) {
    console.log(
      '\n✨ Perfect! Your translation files are beautifully organized!'
    );
  } else if (totalIssues > 0) {
    console.log(
      '\n💡 Consider running the sorting script again to fix remaining issues.'
    );
  }
}

console.log('\n========================================');
console.log('🎯 Usage:');
console.log(
  '  node translation_sort_and_verify.js        # Sort and verify (default)'
);
console.log('  node translation_sort_and_verify.js sort   # Sort only');
console.log('  node translation_sort_and_verify.js verify # Verify only');
console.log('========================================');
