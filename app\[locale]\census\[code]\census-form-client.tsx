'use client';

import {
  CalendarDays,
  Check,
  CheckCircle,
  Clock,
  Copy,
  Edit,
  FileText,
  Home,
  MapPin,
  MessageSquare,
  Plus,
  Save,
  Trash2,
  Users,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { lazy, Suspense, useEffect, useState } from 'react';
import { CensusDeleteMemberDialog } from '@/components/census/delete-member-dialog';
import { HouseholdRegistrationForm } from '@/components/census/household-registration-form';
import { MemberFormWithSacraments } from '@/components/census/member-form-with-sacraments';
import { useWelcomeModal } from '@/components/census/welcome-modal/useWelcomeModal';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { StatusBadge } from '@/components/ui/status-badge';
import { useCensusProgress } from '@/hooks/use-census-progress';
import { useCensusAuth } from '@/hooks/useCensusAuth';
import { useMessage } from '@/hooks/useMessage';
import type { IHouseholdMemberWithDetails } from '@/lib/db/household-members';
import { formatDateForDatabase, formatForDisplay } from '@/lib/utils/date-time';
import type { ClientCombinedMemberSacramentFormValues } from '@/lib/validation/client/census-client';

// Lazy load the welcome modal for better performance
const CensusWelcomeModal = lazy(() =>
  import('@/components/census/welcome-modal/CensusWelcomeModal').then(
    (module) => ({ default: module.CensusWelcomeModal })
  )
);

interface CensusFormClientProps {
  code: string;
  initialSessionData: {
    id: string;
    name: string;
    censusYearId: string;
    householdId?: string;
  };
}

// Define types for our data
interface Household {
  id: number;
  suburb: string;
  firstCensusYearId: number;
  lastCensusYearId: number;
  createdAt: string;
  updatedAt: string;
}

interface Member {
  id: number;
  memberId: number;
  householdId: number;
  relationship: 'head' | 'spouse' | 'child' | 'parent' | 'relative' | 'other';
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: 'male' | 'female' | 'other';
  mobilePhone: string;
  hobby?: string | null;
  isCurrent: boolean; // Note: Not used for UI display - kept for API compatibility
  censusYearId: number;
}

interface Sacrament {
  id: number;
  memberId: number;
  sacramentTypeId: number;
  sacrament_name: string;
  sacrament_code: string;
  date: string;
  place?: string;
  notes?: string;
  censusYearId: number;
}

interface SacramentType {
  id: number;
  code: string;
  name: string;
  description?: string;
}

interface FormStatus {
  id?: number;
  householdId: number;
  censusYearId: number;
  status: 'not_started' | 'in_progress' | 'completed';
  lastUpdated?: string;
  completionDate?: string;
  householdComment?: string;
}

interface CensusYear {
  id: number;
  year: number;
  isActive: boolean;
  startDate: string;
  endDate: string;
}

export function CensusFormClient({
  code,
  initialSessionData,
}: CensusFormClientProps) {
  const { session, status, updateSession } = useCensusAuth();
  const { showSuccess, showError } = useMessage();
  const t = useTranslations('census');
  const tNotifications = useTranslations('notifications');
  const tForms = useTranslations('forms');

  // Progress tracking for immediate UI updates
  const {
    refreshProgress,
    progress,
    isLoading: isProgressLoading,
  } = useCensusProgress();
  const tGenders = useTranslations('genders');
  const tRelationships = useTranslations('relationships');
  const tSacraments = useTranslations('sacraments');
  const [isRegistered, setIsRegistered] = useState<boolean>(
    !!initialSessionData.householdId
  );
  const [isLoading, setIsLoading] = useState(true);
  const [hasTriggeredWelcome, setHasTriggeredWelcome] = useState(false);

  // Welcome modal state management
  const {
    isOpen: isWelcomeOpen,
    setIsOpen: setWelcomeOpen,
    isDismissed,
    dismissModal,
  } = useWelcomeModal();

  // Handle copying code to clipboard with industry-standard UI pattern
  const handleCopyCode = async () => {
    try {
      await navigator.clipboard.writeText(code);

      // Show success state immediately
      setIsCopied(true);
      showSuccess('uniqueCodeCopied');

      // Revert to copy icon after 2 seconds
      setTimeout(() => {
        setIsCopied(false);
      }, 2000);
    } catch (_error) {
      showError('failedToCopyCode');
    }
  };

  // State for household data
  const [household, setHousehold] = useState<Household | null>(null);
  const [householdHead, setHouseholdHead] = useState<Member | null>(null);
  const [formStatus, setFormStatus] = useState<FormStatus | null>(null);
  const [censusYear, setCensusYear] = useState<CensusYear | null>(null);
  const [sacramentTypes, setSacramentTypes] = useState<SacramentType[]>([]);

  // State for members and sacraments
  const [members, setMembers] = useState<IHouseholdMemberWithDetails[]>([]);
  const [selectedMember, setSelectedMember] =
    useState<IHouseholdMemberWithDetails | null>(null);
  const [sacraments, setSacraments] = useState<Record<number, Sacrament[]>>({});

  // UI state
  const [isAddingMember, setIsAddingMember] = useState(false);
  const [isEditingMember, setIsEditingMember] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [infoExpanded, setInfoExpanded] = useState<string>('');
  const [memberAccordionValues, setMemberAccordionValues] = useState<
    Record<number, string>
  >({});
  const [isCopied, setIsCopied] = useState(false);

  // Delete member dialogue state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [memberToDelete, setMemberToDelete] =
    useState<IHouseholdMemberWithDetails | null>(null);

  // Household comment state
  const [householdComment, setHouseholdComment] = useState<string>('');

  // Update household comment when form status changes
  useEffect(() => {
    if (formStatus) {
      setHouseholdComment(formStatus.householdComment || '');
    }
  }, [formStatus]);

  // Determine if the user has completed registration based on initial data or session
  useEffect(() => {
    // If we have a session and it's loaded, use that to determine registration status
    if (status === 'authenticated' && session?.user) {
      const hasHouseholdId = !!session.user.householdId;

      // Only update if the session has a household ID or if we're not already registered
      // This prevents the form from reappearing if the session update is delayed
      if (hasHouseholdId || !isRegistered) {
        setIsRegistered(hasHouseholdId);
      }
    }
    // If we're not authenticated yet but have initial data, use that
    else if (status === 'unauthenticated' || initialSessionData) {
      const hasHouseholdId = !!initialSessionData.householdId;

      // Only update if the initial data has a household ID or if we're not already registered
      if (hasHouseholdId || !isRegistered) {
        setIsRegistered(hasHouseholdId);
      }
    }
  }, [session, status, initialSessionData, isRegistered]);

  // Fetch household data when registered
  useEffect(() => {
    if (!isRegistered) {
      setIsLoading(false);
      return;
    }

    const fetchHouseholdData = async () => {
      try {
        setIsLoading(true);

        // Fetch household information
        const householdResponse = await fetch('/api/census/household');
        if (!householdResponse.ok) {
          throw new Error(t('failedToFetchHouseholdData'));
        }

        const householdData = await householdResponse.json();
        setHousehold(householdData.household);
        setHouseholdHead(householdData.householdHead);
        setFormStatus(householdData.formStatus);
        setCensusYear(householdData.censusYear);
        setSacramentTypes(householdData.sacramentTypes);

        // Fetch household members
        const membersResponse = await fetch('/api/census/members');
        if (!membersResponse.ok) {
          throw new Error(t('failedToFetchHouseholdMembers'));
        }

        const membersData = await membersResponse.json();
        setMembers(membersData);

        setIsLoading(false);
      } catch (_error) {
        showError('failedToLoadHouseholdData');
        setIsLoading(false);
      }
    };

    fetchHouseholdData();
  }, [isRegistered, showError, t]);

  // Transition-based welcome modal detection - show when isRegistered becomes true
  // Works exactly like the original registration completion trigger
  useEffect(() => {
    // Wait for progress to be loaded before making decision
    if (isProgressLoading) {
      return;
    }

    // Only trigger when isRegistered becomes true for the first time this session
    if (isRegistered && !hasTriggeredWelcome) {
      // Debug: Log progress value to understand the decision (development only)
      if (process.env.NODE_ENV === 'development') {
      }

      // This is the transition moment - just like registration completion
      if (!isDismissed && progress < 100) {
        if (process.env.NODE_ENV === 'development') {
        }
        setWelcomeOpen(true);
      }
      setHasTriggeredWelcome(true); // Prevent multiple triggers this session
    }
  }, [
    isRegistered,
    hasTriggeredWelcome,
    isDismissed,
    progress,
    isProgressLoading,
    setWelcomeOpen,
  ]);

  // Fetch sacraments for a member
  const fetchSacraments = async (memberId: number) => {
    try {
      const response = await fetch(
        `/api/census/sacraments?memberId=${memberId}`
      );
      if (!response.ok) {
        throw new Error('Failed to fetch sacraments');
      }

      const data = await response.json();
      setSacraments((prev) => ({
        ...prev,
        [memberId]: data,
      }));

      return data;
    } catch (_error) {
      showError('failedToLoadSacraments');
      return [];
    }
  };

  // Handle registration completion
  const handleRegistrationComplete = async () => {
    // Set the registered state to true to show the main form
    setIsRegistered(true);

    // Force a session refresh to get the updated JWT token
    try {
      await updateSession();
    } catch {}

    // Show welcome modal for new users (only if not previously dismissed)
    if (!isDismissed) {
      setWelcomeOpen(true);
    }
  };

  // Handle adding a new household member with sacraments
  const handleAddMember = async (
    data: ClientCombinedMemberSacramentFormValues
  ) => {
    try {
      setIsSubmitting(true);

      // Extract sacraments data
      const { sacraments: formSacraments, ...memberData } = data;

      // Format the date properly for the API (preserving local date to prevent timezone shifts)
      const formattedMemberData = {
        ...memberData,
        dateOfBirth:
          memberData.dateOfBirth instanceof Date
            ? formatDateForDatabase(memberData.dateOfBirth)
            : memberData.dateOfBirth,
      };

      // First, create the member
      const memberResponse = await fetch('/api/census/members', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formattedMemberData),
      });

      if (!memberResponse.ok) {
        const errorData = await memberResponse.json();
        throw new Error(
          errorData.error ||
            errorData.details?.error ||
            'Failed to add household member'
        );
      }

      const memberResult = await memberResponse.json();
      const newMember = memberResult.member;

      // Update the members list
      setMembers((prev) => [...prev, newMember]);

      // If there are sacraments, add them (only complete ones with valid sacramentTypeId)
      if (formSacraments && formSacraments.length > 0) {
        // Filter out incomplete sacraments (those without a selected sacrament type)
        const completeSacraments = formSacraments.filter(
          (s) => s.sacramentTypeId && s.sacramentTypeId !== 0
        );

        if (completeSacraments.length > 0) {
          const sacramentPromises = completeSacraments.map(
            async (sacrament) => {
              // Format the sacrament data (preserving local date to prevent timezone shifts)
              const formattedSacrament = {
                ...sacrament,
                memberId: newMember.memberId,
                date:
                  sacrament.date instanceof Date
                    ? formatDateForDatabase(sacrament.date)
                    : sacrament.date,
              };

              // Create the sacrament
              const sacramentResponse = await fetch('/api/census/sacraments', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify(formattedSacrament),
              });

              if (!sacramentResponse.ok) {
                showError('failedToAddSacrament');
                return null;
              }

              return sacramentResponse.json();
            }
          );

          // Wait for all sacrament creations to complete
          const sacramentResults = await Promise.all(sacramentPromises);

          // Filter out failed sacrament creations
          const validSacraments = sacramentResults
            .filter((result) => result !== null)
            .map((result) => result.sacrament);

          // Update the sacraments state
          if (validSacraments.length > 0) {
            setSacraments((prev) => ({
              ...prev,
              [newMember.memberId]: validSacraments,
            }));
          }
        }
      }

      // Reset UI state
      setIsAddingMember(false);

      // Event-driven progress update (member count affects family members completion)
      await refreshProgress(); // Triggered by user action

      // Update form status to in_progress when member is added (suppress toast to avoid duplicate)
      await handleUpdateFormStatus('in_progress', true);

      showSuccess('memberAdded');
    } catch (_error) {
      showError('failedToAddHouseholdMember');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle editing a household member with sacraments
  const handleEditMember = async (
    data: ClientCombinedMemberSacramentFormValues
  ) => {
    try {
      setIsSubmitting(true);

      // Extract sacraments data
      const { sacraments: formSacraments, ...memberData } = data;

      // Format the date properly for the API (preserving local date to prevent timezone shifts)
      const formattedMemberData = {
        ...memberData,
        dateOfBirth:
          memberData.dateOfBirth instanceof Date
            ? formatDateForDatabase(memberData.dateOfBirth)
            : memberData.dateOfBirth,
        id: selectedMember?.memberId,
      };

      // First, update the member
      const memberResponse = await fetch('/api/census/members', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formattedMemberData),
      });

      if (!memberResponse.ok) {
        const errorData = await memberResponse.json();
        throw new Error(
          errorData.error ||
            errorData.details?.error ||
            'Failed to update household member'
        );
      }

      const memberResult = await memberResponse.json();
      const updatedMember = memberResult.member;

      // Update the members list
      setMembers((prev) =>
        prev.map((member) =>
          member.memberId === updatedMember.memberId ? updatedMember : member
        )
      );

      // Check if the updated member is the household head
      const isHouseholdHead = updatedMember.relationship === 'head';
      const originalMember = members.find(
        (m) => m.memberId === updatedMember.memberId
      );

      // If household head was updated, update both the session and local state
      if (isHouseholdHead) {
        // Update the householdHead state to reflect changes in the Household Information section
        setHouseholdHead((prev) =>
          prev
            ? {
                ...prev,
                firstName: updatedMember.firstName,
                lastName: updatedMember.lastName,
                mobilePhone: updatedMember.mobilePhone,
                gender: updatedMember.gender,
              }
            : null
        );

        // If the last name changed, also refresh the session to update the header
        const lastNameChanged =
          originalMember && originalMember.lastName !== updatedMember.lastName;
        if (lastNameChanged) {
          try {
            await updateSession();
            if (process.env.NODE_ENV === 'development') {
            }
          } catch (_error) {
            // Don't block the UI if session refresh fails - the header will update on next page load
          }
        }
      }

      // Get existing sacraments for this member from the current state
      const existingSacraments = sacraments[updatedMember.memberId] || [];

      // Handle sacrament operations (create, update, delete)
      const sacramentOperations: Promise<any>[] = [];

      // If there are sacraments in the form data, handle them (only complete ones with valid sacramentTypeId)
      if (formSacraments && formSacraments.length > 0) {
        // Filter out incomplete sacraments (those without a selected sacrament type)
        const completeSacraments = formSacraments.filter(
          (s) => s.sacramentTypeId && s.sacramentTypeId !== 0
        );

        // For each complete sacrament in the form data
        completeSacraments.forEach((sacrament) => {
          // Check if this sacrament already exists (by sacramentTypeId)
          const existingSacrament = existingSacraments.find(
            (s: Sacrament) => s.sacramentTypeId === sacrament.sacramentTypeId
          );

          // Format the sacrament data (preserving local date to prevent timezone shifts)
          const formattedSacrament = {
            ...sacrament,
            memberId: updatedMember.memberId,
            date:
              sacrament.date instanceof Date
                ? formatDateForDatabase(sacrament.date)
                : sacrament.date,
            id: existingSacrament?.id,
          };

          // If it exists, update it; otherwise, create it
          const method = existingSacrament ? 'PUT' : 'POST';

          const sacramentPromise = fetch('/api/census/sacraments', {
            method,
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(formattedSacrament),
          }).then(async (sacramentResponse) => {
            if (!sacramentResponse.ok) {
              showError('failedToUpdateSacrament');
              return null;
            }
            return sacramentResponse.json();
          });

          sacramentOperations.push(sacramentPromise);
        });
      }

      // Find sacraments that exist in the database but are not in the complete form data (these should be deleted)
      const completeSacraments = (formSacraments || []).filter(
        (s) => s.sacramentTypeId && s.sacramentTypeId !== 0
      );
      const formSacramentTypeIds = completeSacraments.map(
        (s) => s.sacramentTypeId
      );
      const sacramentsToDelete = existingSacraments.filter(
        (existingSacrament: Sacrament) =>
          !formSacramentTypeIds.includes(existingSacrament.sacramentTypeId)
      );

      // Delete sacraments that are no longer in the form
      sacramentsToDelete.forEach((sacramentToDelete: Sacrament) => {
        const deletePromise = fetch(
          `/api/census/sacraments?id=${sacramentToDelete.id}`,
          {
            method: 'DELETE',
          }
        ).then(async (deleteResponse) => {
          if (!deleteResponse.ok) {
            showError('failedToDeleteSacrament');
            return null;
          }
          return deleteResponse.json();
        });

        sacramentOperations.push(deletePromise);
      });

      // Wait for all sacrament operations to complete
      if (sacramentOperations.length > 0) {
        await Promise.all(sacramentOperations);
      }

      // Fetch all sacraments for this member to ensure we have the latest data
      await fetchSacraments(updatedMember.memberId);

      // Reset UI state
      setIsEditingMember(false);
      setSelectedMember(null);

      // Event-driven progress update (hobby/sacrament changes affect completion)
      await refreshProgress(); // Triggered by user action

      // Update form status to in_progress when member is updated (suppress toast to avoid duplicate)
      await handleUpdateFormStatus('in_progress', true);

      showSuccess('memberUpdated');
    } catch (_error) {
      showError('failedToUpdateHouseholdMember');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle opening delete member dialogue
  const handleDeleteMember = (memberId: number) => {
    const member = members.find((m) => m.memberId === memberId);
    if (member) {
      setMemberToDelete(member);
      setDeleteDialogOpen(true);
    }
  };

  // Handle member deletion completion
  const handleMemberDeleted = () => {
    if (!memberToDelete) {
      return;
    }

    const memberId = memberToDelete.memberId;

    // Update the members list
    setMembers((prev) => prev.filter((member) => member.memberId !== memberId));

    // Clean up related state for the deleted member
    setSacraments((prev) => {
      const { [memberId]: deletedSacraments, ...remainingSacraments } = prev;
      return remainingSacraments;
    });

    setMemberAccordionValues((prev) => {
      const { [memberId]: deletedAccordion, ...remainingAccordions } = prev;
      return remainingAccordions;
    });

    // Reset UI state if the deleted member was selected
    if (selectedMember?.memberId === memberId) {
      setSelectedMember(null);
    }

    // Reset dialogue state
    setMemberToDelete(null);

    // Event-driven progress update (member count affects family members completion)
    refreshProgress(); // Triggered by user action
  };

  // Handle updating form status
  const handleUpdateFormStatus = async (
    status: 'not_started' | 'in_progress' | 'completed',
    suppressToast = false
  ) => {
    if (!(household && censusYear)) {
      showError('householdInfoMissing');
      return;
    }

    try {
      setIsSubmitting(true);

      const response = await fetch('/api/census/form-status', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          householdId: household.id,
          censusYearId: censusYear.id,
          status,
          householdComment,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update form status');
      }

      const result = await response.json();

      // Update the form status
      setFormStatus(result.formStatus);

      // Only show toast if not suppressed
      if (!suppressToast) {
        showSuccess(
          status === 'completed'
            ? 'formCompletedSuccessfully'
            : 'formSavedSuccessfully'
        );
      }
    } catch (_error) {
      showError('failedToUpdateFormStatus');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render loading skeleton while checking registration status
  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex min-h-[calc(100vh-16rem)] items-center justify-center px-4 py-8 md:px-0">
          <div className="mx-auto w-full max-w-2xl">
            {/* Header skeleton */}
            <Skeleton className="mb-2 h-10 w-2/3" />
            <Skeleton className="mb-8 h-5 w-full max-w-md" />

            {/* Unique code box skeleton */}
            <Skeleton className="mb-8 h-10 w-full" />

            {/* Form section skeleton */}
            <Skeleton className="mb-2 h-6 w-1/3" />
            <Skeleton className="mb-6 h-4 w-1/2" />

            {/* Form fields skeleton */}
            <div className="space-y-6">
              <div className="grid gap-4 sm:grid-cols-2">
                <div className="sm:col-span-2">
                  <Skeleton className="h-16 w-full" />
                </div>
              </div>

              <div className="grid gap-4 sm:grid-cols-2">
                <Skeleton className="h-16 w-full" />
                <Skeleton className="h-16 w-full" />
              </div>

              <div className="grid gap-4 sm:grid-cols-2">
                <Skeleton className="h-16 w-full" />
                <Skeleton className="h-16 w-full" />
              </div>

              <Skeleton className="mt-4 h-10 w-full max-w-xs" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      {isRegistered ? (
        // Show census form if user has registered
        <div className="space-y-8 px-4 md:px-0">
          {/* Header Section */}
          <div className="flex items-center gap-4">
            <div className="rounded-full bg-primary/10 p-3">
              <FileText className="h-7 w-7 text-primary" />
            </div>
            <h1 className="font-bold text-4xl tracking-tight">
              {t('censusFormPageTitle')}
            </h1>
          </div>

          {/* Combined Information Section - Collapsible on Mobile */}
          <div className="rounded-xl border border-border/50 bg-card shadow-lg">
            {/* Mobile View - Collapsible */}
            <div className="md:hidden">
              <Accordion
                className="w-full"
                collapsible
                onValueChange={setInfoExpanded}
                type="single"
                value={infoExpanded}
              >
                <AccordionItem className="border-0" value="info-panel">
                  <AccordionTrigger className="group flex cursor-pointer touch-manipulation items-center justify-between rounded-t-xl rounded-b-none border-border/20 border-b px-5 py-4 transition-all duration-200 hover:bg-accent/20 hover:no-underline active:bg-accent/50">
                    <div className="flex items-center gap-2">
                      <svg
                        className="h-5 w-5 text-primary transition-colors group-hover:text-primary/80"
                        fill="none"
                        height="20"
                        stroke="currentColor"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        viewBox="0 0 24 24"
                        width="20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <rect height="9" rx="1" width="7" x="3" y="3" />
                        <rect height="5" rx="1" width="7" x="14" y="3" />
                        <rect height="9" rx="1" width="7" x="14" y="12" />
                        <rect height="5" rx="1" width="7" x="3" y="16" />
                      </svg>
                      <h2 className="font-semibold text-xl transition-colors group-hover:text-foreground/90">
                        {t('formAndHouseholdInfo')}
                      </h2>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-5 pt-4 pb-5">
                    <div className="flex flex-col gap-6">
                      {/* Form Information */}
                      <div className="flex-1">
                        <div className="mb-3 flex items-center gap-2">
                          <svg
                            className="h-4 w-4 text-primary"
                            fill="none"
                            height="16"
                            stroke="currentColor"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            viewBox="0 0 24 24"
                            width="16"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                            <polyline points="14 2 14 8 20 8" />
                          </svg>
                          <h3 className="font-semibold text-base">
                            {t('formInformation')}
                          </h3>
                        </div>

                        <p className="mb-3 text-muted-foreground text-xs">
                          {t('completeHouseholdCensus', {
                            year:
                              censusYear?.year?.toString() ||
                              new Date().getFullYear().toString(),
                          })}
                        </p>

                        <div className="space-y-3">
                          <div className="rounded-lg border border-muted/30 bg-muted/20 p-3">
                            <div className="flex flex-col justify-between gap-2 sm:flex-row sm:items-center">
                              <div>
                                <p className="font-medium text-muted-foreground text-sm">
                                  {t('uniqueCode')}
                                </p>
                                <p className="font-mono font-semibold text-base text-foreground">
                                  {code}
                                </p>
                              </div>
                              <StatusBadge
                                className="mt-1 transition-all duration-200 sm:mt-0"
                                interactive={true}
                                onClick={handleCopyCode}
                                title={
                                  isCopied
                                    ? tNotifications('uniqueCodeCopied')
                                    : t('clickToCopy')
                                }
                                variant="info"
                              >
                                {isCopied ? (
                                  <Check className="text-green-600" size={14} />
                                ) : (
                                  <Copy size={14} />
                                )}
                                {t('copyId')}
                              </StatusBadge>
                            </div>
                          </div>

                          <div className="rounded-lg border border-muted/30 bg-muted/20 p-3">
                            <div className="flex flex-col justify-between gap-2 sm:flex-row sm:items-center">
                              <div>
                                <p className="font-medium text-muted-foreground text-sm">
                                  {t('formStatus')}
                                </p>
                                <p className="font-semibold text-base text-foreground">
                                  {formStatus
                                    ? formStatus.status === 'not_started'
                                      ? t('notStarted')
                                      : formStatus.status === 'in_progress'
                                        ? t('inProgress')
                                        : formStatus.status === 'completed'
                                          ? t('completed')
                                          : (formStatus.status as string)
                                              .replace('_', ' ')
                                              .charAt(0)
                                              .toUpperCase() +
                                            (formStatus.status as string)
                                              .replace('_', ' ')
                                              .slice(1)
                                    : t('notStarted')}
                                </p>
                              </div>
                              {formStatus && (
                                <StatusBadge
                                  className="mt-1 sm:mt-0"
                                  variant={
                                    formStatus.status === 'completed'
                                      ? 'success'
                                      : formStatus.status === 'in_progress'
                                        ? 'warning'
                                        : 'default'
                                  }
                                >
                                  {formStatus.status === 'completed' ? (
                                    <CheckCircle />
                                  ) : (
                                    <Clock />
                                  )}
                                  {formStatus.status === 'not_started'
                                    ? t('notStarted')
                                    : formStatus.status === 'in_progress'
                                      ? t('inProgress')
                                      : formStatus.status === 'completed'
                                        ? t('completed')
                                        : (formStatus.status as string).replace(
                                            '_',
                                            ' '
                                          )}
                                </StatusBadge>
                              )}
                            </div>
                          </div>

                          {formStatus?.lastUpdated && (
                            <div className="rounded-lg border border-muted/30 bg-muted/20 p-3">
                              <p className="font-medium text-muted-foreground text-sm">
                                {t('lastUpdated')}
                              </p>
                              <p className="font-semibold text-base text-foreground">
                                {formatForDisplay(formStatus.lastUpdated, true)}
                              </p>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Household Information */}
                      {household && householdHead && (
                        <div className="flex-1 border-border/30 border-t pt-4">
                          <div className="mb-3 flex items-center gap-2">
                            <Home className="h-4 w-4 text-primary" />
                            <h3 className="font-semibold text-base">
                              {t('householdInformation')}
                            </h3>
                          </div>

                          <p className="mb-3 text-muted-foreground text-xs">
                            {t('householdDetailsDescription')}
                          </p>

                          <div className="space-y-3">
                            <div className="rounded-lg border border-muted/30 bg-muted/20 p-3">
                              <p className="font-medium text-muted-foreground text-sm">
                                {t('suburb')}
                              </p>
                              <p className="font-semibold text-base text-foreground">
                                {household.suburb}
                              </p>
                            </div>

                            <div className="rounded-lg border border-muted/30 bg-muted/20 p-3">
                              <p className="font-medium text-muted-foreground text-sm">
                                {t('householdHead')}
                              </p>
                              <p className="font-semibold text-base text-foreground">
                                {householdHead.firstName}{' '}
                                {householdHead.lastName}
                              </p>
                            </div>

                            <div className="rounded-lg border border-muted/30 bg-muted/20 p-3">
                              <p className="font-medium text-muted-foreground text-sm">
                                {t('contact')}
                              </p>
                              <p className="font-semibold text-base text-foreground">
                                {householdHead.mobilePhone}
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>

            {/* Desktop View - Always Expanded */}
            <div className="hidden p-5 sm:p-6 md:block">
              <div className="flex flex-col gap-6 md:flex-row md:gap-8">
                {/* Form Information */}
                <div className="flex-1">
                  <div className="mb-3 flex items-center gap-2">
                    <svg
                      className="h-5 w-5 text-primary"
                      fill="none"
                      height="20"
                      stroke="currentColor"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                      <polyline points="14 2 14 8 20 8" />
                    </svg>
                    <h2 className="font-semibold text-xl">
                      {t('formInformation')}
                    </h2>
                  </div>

                  <p className="mb-4 text-muted-foreground text-sm">
                    {t('completeHouseholdCensus', {
                      year:
                        censusYear?.year?.toString() ||
                        new Date().getFullYear().toString(),
                    })}
                  </p>

                  <div className="space-y-3">
                    <div className="rounded-lg border border-muted/30 bg-muted/20 p-3">
                      <div className="flex flex-col justify-between gap-2 sm:flex-row sm:items-center">
                        <div>
                          <p className="font-medium text-muted-foreground text-sm">
                            {t('uniqueCode')}
                          </p>
                          <p className="font-mono font-semibold text-base text-foreground">
                            {code}
                          </p>
                        </div>
                        <StatusBadge
                          className="mt-1 transition-all duration-200 sm:mt-0"
                          interactive={true}
                          onClick={handleCopyCode}
                          title={
                            isCopied
                              ? tNotifications('uniqueCodeCopied')
                              : t('clickToCopy')
                          }
                          variant="info"
                        >
                          {isCopied ? (
                            <Check className="text-green-600" size={14} />
                          ) : (
                            <Copy size={14} />
                          )}
                          {t('copyId')}
                        </StatusBadge>
                      </div>
                    </div>

                    <div className="rounded-lg border border-muted/30 bg-muted/20 p-3">
                      <div className="flex flex-col justify-between gap-2 sm:flex-row sm:items-center">
                        <div>
                          <p className="font-medium text-muted-foreground text-sm">
                            {t('formStatus')}
                          </p>
                          <p className="font-semibold text-base text-foreground">
                            {formStatus
                              ? formStatus.status === 'not_started'
                                ? t('notStarted')
                                : formStatus.status === 'in_progress'
                                  ? t('inProgress')
                                  : formStatus.status === 'completed'
                                    ? t('completed')
                                    : (formStatus.status as string)
                                        .replace('_', ' ')
                                        .charAt(0)
                                        .toUpperCase() +
                                      (formStatus.status as string)
                                        .replace('_', ' ')
                                        .slice(1)
                              : t('notStarted')}
                          </p>
                        </div>
                        {formStatus && (
                          <StatusBadge
                            className="mt-1 sm:mt-0"
                            variant={
                              formStatus.status === 'completed'
                                ? 'success'
                                : formStatus.status === 'in_progress'
                                  ? 'warning'
                                  : 'default'
                            }
                          >
                            {formStatus.status === 'completed' ? (
                              <CheckCircle />
                            ) : (
                              <Clock />
                            )}
                            {formStatus.status === 'not_started'
                              ? t('notStarted')
                              : formStatus.status === 'in_progress'
                                ? t('inProgress')
                                : formStatus.status === 'completed'
                                  ? t('completed')
                                  : (formStatus.status as string).replace(
                                      '_',
                                      ' '
                                    )}
                          </StatusBadge>
                        )}
                      </div>
                    </div>

                    {formStatus?.lastUpdated && (
                      <div className="rounded-lg border border-muted/30 bg-muted/20 p-3">
                        <p className="font-medium text-muted-foreground text-sm">
                          {t('lastUpdated')}
                        </p>
                        <p className="font-semibold text-base text-foreground">
                          {formatForDisplay(formStatus.lastUpdated, true)}
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Household Information */}
                {household && householdHead && (
                  <div className="flex-1 md:border-border/50 md:border-l md:pl-8">
                    <div className="mb-3 flex items-center gap-2">
                      <Home className="h-5 w-5 text-primary" />
                      <h2 className="font-semibold text-xl">
                        {t('householdInformation')}
                      </h2>
                    </div>

                    <p className="mb-4 text-muted-foreground text-sm">
                      {t('householdDetailsDescription')}
                    </p>

                    <div className="space-y-3">
                      <div className="rounded-lg border border-muted/30 bg-muted/20 p-3">
                        <p className="font-medium text-muted-foreground text-sm">
                          {t('suburb')}
                        </p>
                        <p className="font-semibold text-base text-foreground">
                          {household.suburb}
                        </p>
                      </div>

                      <div className="rounded-lg border border-muted/30 bg-muted/20 p-3">
                        <p className="font-medium text-muted-foreground text-sm">
                          {t('householdHead')}
                        </p>
                        <p className="font-semibold text-base text-foreground">
                          {householdHead.firstName} {householdHead.lastName}
                        </p>
                      </div>

                      <div className="rounded-lg border border-muted/30 bg-muted/20 p-3">
                        <p className="font-medium text-muted-foreground text-sm">
                          {t('contact')}
                        </p>
                        <p className="font-semibold text-base text-foreground">
                          {householdHead.mobilePhone}
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Household Members Section */}
          <div className="rounded-xl border border-border/50 bg-card p-5 shadow-lg sm:p-6">
            <div className="mb-3 flex items-center gap-2">
              <Users className="h-5 w-5 text-primary" />
              <h2 className="font-semibold text-xl">{t('householdMembers')}</h2>
            </div>

            <div className="mb-5">
              <p className="text-muted-foreground text-sm">
                {t('manageHouseholdMembersDescription')}
              </p>
            </div>

            {/* Add/Edit Member Form */}
            {isAddingMember && (
              <div className="mb-5 rounded-lg border p-4">
                <div className="mb-3 flex items-center gap-2">
                  <Plus className="h-4 w-4 text-primary" />
                  <h3 className="font-semibold text-lg">{t('addNewMember')}</h3>
                </div>
                <MemberFormWithSacraments
                  onCancel={() => setIsAddingMember(false)}
                  onSubmit={handleAddMember}
                  sacramentTypes={sacramentTypes}
                />
              </div>
            )}

            {isEditingMember && selectedMember && (
              <div
                className="mb-5 rounded-lg border p-4"
                data-editing-household-head={
                  selectedMember.relationship === 'head' ? 'true' : 'false'
                }
                data-member-editing={selectedMember.relationship}
              >
                <div className="mb-3 flex items-center gap-2">
                  <Edit className="h-4 w-4 text-primary" />
                  <h3 className="font-semibold text-lg">{t('editMember')}</h3>
                </div>
                <div className="mb-4 rounded-lg bg-blue-50 p-3 dark:bg-blue-950">
                  <p className="font-medium text-blue-700 text-sm dark:text-blue-300">
                    {t('youAreEditing', {
                      name: `${selectedMember.firstName} ${selectedMember.lastName}`,
                    })}
                  </p>
                </div>
                <MemberFormWithSacraments
                  initialData={{
                    id: selectedMember.memberId,
                    firstName: selectedMember.firstName,
                    lastName: selectedMember.lastName,
                    dateOfBirth: selectedMember.dateOfBirth
                      ? new Date(selectedMember.dateOfBirth)
                      : new Date(),
                    gender: selectedMember.gender,
                    mobilePhone: selectedMember.mobilePhone,
                    hobby: selectedMember.hobby || '',
                    occupation: selectedMember.occupation || '',
                    relationship: selectedMember.relationship,
                    sacraments:
                      sacraments[selectedMember.memberId]?.map((sacrament) => ({
                        sacramentTypeId: sacrament.sacramentTypeId,
                        date: sacrament.date ? new Date(sacrament.date) : null,
                        place: sacrament.place || '',
                      })) || [],
                  }}
                  isEdit={true}
                  onCancel={() => {
                    setIsEditingMember(false);
                    setSelectedMember(null);
                  }}
                  onSubmit={handleEditMember}
                  sacramentTypes={sacramentTypes}
                />
              </div>
            )}

            {/* Member list */}
            {members.length === 0 ? (
              <div className="rounded-lg border border-muted/30 bg-muted/10 py-8 text-center">
                <div className="flex flex-col items-center gap-3">
                  <div className="rounded-full bg-muted/30 p-3">
                    <Users className="h-6 w-6 text-muted-foreground" />
                  </div>
                  <div>
                    <p className="font-medium text-base text-foreground">
                      {t('noHouseholdMembersYet')}
                    </p>
                    <p className="mt-1 text-muted-foreground text-sm">
                      {t('clickAddMemberToStart')}
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-1">
                {members.map((member) => {
                  // Skip rendering this member if it's currently being edited
                  if (
                    isEditingMember &&
                    selectedMember &&
                    selectedMember.memberId === member.memberId
                  ) {
                    return null;
                  }

                  // Fetch sacraments if not already loaded
                  if (!sacraments[member.memberId]) {
                    fetchSacraments(member.memberId);
                  }

                  const memberSacraments = sacraments[member.memberId] || [];

                  return (
                    <Accordion
                      collapsible
                      key={member.memberId}
                      onValueChange={(newValue) => {
                        setMemberAccordionValues((prev) => ({
                          ...prev,
                          [member.memberId]: newValue,
                        }));
                      }}
                      type="single"
                      value={memberAccordionValues[member.memberId] || ''}
                    >
                      <AccordionItem
                        className="relative border-0"
                        data-member-relationship={member.relationship}
                        value={`member-${member.memberId}`}
                      >
                        <div className="relative">
                          <AccordionTrigger className="mb-3 w-full cursor-pointer justify-start rounded-lg border border-border px-4 py-3 hover:bg-accent/20 hover:no-underline data-[state=open]:bg-accent/10">
                            <div className="flex w-full items-center justify-between">
                              <div className="flex items-center">
                                <span className="font-semibold hover:underline">
                                  {member.firstName} {member.lastName}
                                </span>
                                <StatusBadge
                                  className="ml-2 text-xs"
                                  variant={
                                    member.relationship === 'head'
                                      ? 'info'
                                      : member.relationship === 'spouse'
                                        ? 'success'
                                        : member.relationship === 'child'
                                          ? 'default'
                                          : member.relationship === 'parent'
                                            ? 'warning'
                                            : 'default'
                                  }
                                >
                                  {tRelationships(member.relationship as any)}
                                </StatusBadge>
                              </div>
                            </div>
                          </AccordionTrigger>
                        </div>
                        <AccordionContent className="px-4 pt-3 pb-4">
                          {/* Member details */}
                          <div className="mb-4 grid grid-cols-1 gap-3 md:grid-cols-4">
                            <div className="rounded-lg border border-muted/20 bg-muted/10 p-2">
                              <p className="font-medium text-muted-foreground text-xs">
                                {t('dateOfBirth')}
                              </p>
                              <p className="font-medium text-sm">
                                {formatForDisplay(member.dateOfBirth)}
                              </p>
                            </div>
                            <div className="rounded-lg border border-muted/20 bg-muted/10 p-2">
                              <p className="font-medium text-muted-foreground text-xs">
                                {t('gender')}
                              </p>
                              <p className="font-medium text-sm">
                                {tGenders(member.gender as any)}
                              </p>
                            </div>
                            <div className="rounded-lg border border-muted/20 bg-muted/10 p-2">
                              <p className="font-medium text-muted-foreground text-xs">
                                {t('mobile')}
                              </p>
                              <p className="font-medium text-sm">
                                {member.mobilePhone}
                              </p>
                            </div>
                            <div className="rounded-lg border border-muted/20 bg-muted/10 p-2">
                              <p className="font-medium text-muted-foreground text-xs">
                                {t('relationship')}
                              </p>
                              <p className="font-medium text-sm">
                                {tRelationships(member.relationship as any)}
                              </p>
                            </div>
                            {(member.hobby || member.occupation) && (
                              <>
                                {member.hobby && (
                                  <div className="col-span-1 rounded-lg border border-muted/20 bg-muted/10 p-2 md:col-span-2">
                                    <p className="font-medium text-muted-foreground text-xs">
                                      {t('hobby')}
                                    </p>
                                    <p className="font-medium text-sm">
                                      {member.hobby}
                                    </p>
                                  </div>
                                )}
                                {member.occupation && (
                                  <div className="col-span-1 rounded-lg border border-muted/20 bg-muted/10 p-2 md:col-span-2">
                                    <p className="font-medium text-muted-foreground text-xs">
                                      {tForms('occupation')}
                                    </p>
                                    <p className="font-medium text-sm">
                                      {member.occupation}
                                    </p>
                                  </div>
                                )}
                              </>
                            )}
                          </div>

                          {/* Sacraments Section */}
                          <div className="mt-4 border-border/80 border-t pt-3">
                            <div className="mb-3 flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <svg
                                  className="text-primary"
                                  fill="none"
                                  height="16"
                                  stroke="currentColor"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                  viewBox="0 0 24 24"
                                  width="16"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                                  <polyline points="14 2 14 8 20 8" />
                                  <path d="M16 13H8" />
                                  <path d="M16 17H8" />
                                  <path d="M10 9H8" />
                                </svg>
                                <h3 className="font-semibold text-sm">
                                  {t('sacraments')}
                                </h3>
                              </div>
                              {memberSacraments.length > 0 && (
                                <StatusBadge className="text-xs" variant="info">
                                  {memberSacraments.length}/
                                  {sacramentTypes.length}
                                </StatusBadge>
                              )}
                            </div>

                            {memberSacraments.length === 0 ? (
                              <div className="rounded-lg border border-muted/20 bg-muted/10 py-4 text-center">
                                <div className="flex flex-col items-center gap-2">
                                  <div className="rounded-full bg-muted/20 p-1.5">
                                    <svg
                                      className="text-muted-foreground"
                                      fill="none"
                                      height="14"
                                      stroke="currentColor"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth="2"
                                      viewBox="0 0 24 24"
                                      width="14"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                                      <polyline points="14 2 14 8 20 8" />
                                      <path d="M16 13H8" />
                                      <path d="M16 17H8" />
                                      <path d="M10 9H8" />
                                    </svg>
                                  </div>
                                  <p className="text-muted-foreground text-sm">
                                    {t('noSacramentsRecordedYet')}
                                  </p>
                                </div>
                              </div>
                            ) : (
                              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                {memberSacraments.map((sacrament) => (
                                  <div
                                    className="flex items-center justify-between rounded-lg bg-muted/10 p-3 sm:p-4"
                                    key={sacrament.id}
                                  >
                                    <h4 className="font-semibold text-foreground text-md">
                                      {tSacraments(
                                        sacrament.sacrament_code as any
                                      )}
                                    </h4>

                                    <div className="ml-2 flex items-center gap-3 text-muted-foreground text-xs">
                                      {sacrament.date && (
                                        <span className="flex items-center gap-1">
                                          <CalendarDays className="h-3.5 w-3.5 flex-shrink-0 text-primary/70" />
                                          <span>
                                            {formatForDisplay(sacrament.date)}
                                          </span>
                                        </span>
                                      )}
                                      {sacrament.place && (
                                        <span className="flex items-center gap-1">
                                          <MapPin className="h-3.5 w-3.5 flex-shrink-0 text-primary/70" />
                                          <span>{sacrament.place}</span>
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>

                          <div className="mt-4 flex justify-end gap-2 border-muted/20 border-t pt-3">
                            <Button
                              className="flex h-8 items-center"
                              data-tour={
                                member.relationship === 'head'
                                  ? 'household-head-edit-button'
                                  : undefined
                              }
                              disabled={isAddingMember || isEditingMember}
                              onClick={async () => {
                                setSelectedMember(member);
                                setIsEditingMember(true);
                                setIsAddingMember(false);
                                // Close other accordions and open this one
                                setMemberAccordionValues({
                                  [member.memberId]: `member-${member.memberId}`,
                                });
                                // Ensure sacraments are loaded for this member before opening edit form
                                if (!sacraments[member.memberId]) {
                                  await fetchSacraments(member.memberId);
                                }
                              }}
                              size="sm"
                              variant="outline"
                            >
                              <Edit className="mr-1.5 h-3.5 w-3.5" />{' '}
                              {t('edit')}
                            </Button>
                            {member.relationship !== 'head' && (
                              <Button
                                className="flex h-8 items-center text-destructive transition-colors hover:bg-destructive/10 hover:text-destructive"
                                disabled={
                                  isAddingMember ||
                                  isEditingMember ||
                                  isSubmitting
                                }
                                onClick={() =>
                                  handleDeleteMember(member.memberId)
                                }
                                size="sm"
                                variant="outline"
                              >
                                <Trash2 className="mr-1.5 h-3.5 w-3.5" />{' '}
                                {t('delete')}
                              </Button>
                            )}
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>
                  );
                })}
              </div>
            )}

            {/* Add Member Button - Below Accordion */}
            <div className="mt-6 flex justify-center sm:justify-end">
              <Button
                className="w-full touch-manipulation border-0 bg-gradient-to-r from-[#FF6308] to-[#97A4FF] text-white shadow-lg transition-all duration-200 hover:from-[#FF6308]/90 hover:to-[#97A4FF]/90 hover:shadow-xl disabled:cursor-not-allowed disabled:opacity-50 disabled:shadow-md sm:w-auto"
                data-tour="add-member-button"
                disabled={isAddingMember || isEditingMember}
                onClick={async () => {
                  setIsAddingMember(true);
                  setIsEditingMember(false);
                  setSelectedMember(null);
                  setMemberAccordionValues({});

                  // Track that user has clicked Add Member button for progress calculation
                  try {
                    const response = await fetch(
                      '/api/census/add-member-attempted',
                      {
                        method: 'POST',
                        headers: {
                          'Content-Type': 'application/json',
                        },
                      }
                    );

                    if (response.ok) {
                      // Event-driven progress update (clicking Add Member affects family members completion)
                      await refreshProgress(); // Triggered by user action
                    }
                  } catch (_error) {
                    // Don't block the UI if tracking fails
                  }
                }}
                size="sm"
              >
                <Plus className="mr-1.5 h-3.5 w-3.5" /> {t('addMember')}
              </Button>
            </div>
          </div>

          {/* Community Feedback Section */}
          <div
            className="rounded-xl border border-border/50 bg-card p-5 shadow-lg sm:p-6"
            data-tour="community-feedback-card"
          >
            <div className="mb-3 flex items-center gap-2">
              <MessageSquare className="h-5 w-5 text-primary" />
              <h2 className="font-semibold text-xl">
                {t('communityFeedback')}
              </h2>
            </div>

            <div className="mb-5">
              <p className="text-muted-foreground text-sm">
                {t('communityFeedbackDescription')}
              </p>
            </div>

            <div className="space-y-4">
              <div className="relative overflow-hidden rounded-xl">
                <textarea
                  className="thin-scrollbar relative w-full resize-y overflow-y-auto border-none bg-transparent p-5 placeholder-gray-400 outline-none"
                  disabled={isSubmitting || isAddingMember || isEditingMember}
                  maxLength={1000}
                  onChange={(e) => setHouseholdComment(e.target.value)}
                  placeholder={tForms('enterCommunityFeedbackPlaceholder')}
                  style={{
                    lineHeight: '32px',
                    fontSize: '16px',
                    color: '#2d3748',
                    minHeight: '160px',
                    backgroundImage: `repeating-linear-gradient(
                      transparent 0px,
                      transparent 31px,
                      #e2e8f0 31px,
                      #e2e8f0 32px
                    )`,
                    backgroundPosition: '0 20px',
                    backgroundAttachment: 'local',
                  }}
                  value={householdComment}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="font-medium text-muted-foreground text-xs">
                  {householdComment.length}/1000
                </div>

                {/* Save button moved to right side below textarea */}
                {members.length > 0 && (
                  <Button
                    className="flex h-8 items-center"
                    disabled={isSubmitting || isAddingMember || isEditingMember}
                    onClick={() => handleUpdateFormStatus('in_progress')}
                    size="sm"
                    variant="outline"
                  >
                    <Save className="mr-1.5 h-3.5 w-3.5" /> {t('save')}
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* Complete Action - Below Household Comments */}
          {members.length > 0 && (
            <div className="mt-6 flex justify-end">
              <Button
                className="touch-manipulation border-0 bg-gradient-to-r from-[#FF6308] to-[#97A4FF] text-white shadow-lg transition-all duration-200 hover:from-[#FF6308]/90 hover:to-[#97A4FF]/90 hover:shadow-xl disabled:cursor-not-allowed disabled:opacity-50 disabled:shadow-md"
                disabled={isSubmitting || isAddingMember || isEditingMember}
                onClick={() => handleUpdateFormStatus('completed')}
                size="default"
              >
                <CheckCircle className="mr-2 h-4 w-4" /> {t('complete')}
              </Button>
            </div>
          )}
        </div>
      ) : (
        // Show registration form if user hasn't registered yet
        <div className="flex min-h-[calc(100vh-16rem)] items-center justify-center px-4 py-8 md:px-0">
          <HouseholdRegistrationForm
            code={code}
            onRegistrationComplete={handleRegistrationComplete}
          />
        </div>
      )}

      {/* Welcome Modal - Shows after successful household registration */}
      <Suspense fallback={null}>
        <CensusWelcomeModal
          isOpen={isWelcomeOpen}
          onClose={() => setWelcomeOpen(false)}
          onDismiss={dismissModal}
        />
      </Suspense>

      {/* Delete Member Dialog */}
      <CensusDeleteMemberDialog
        member={memberToDelete}
        onMemberDeleted={handleMemberDeleted}
        onOpenChange={setDeleteDialogOpen}
        open={deleteDialogOpen}
      />
    </div>
  );
}
