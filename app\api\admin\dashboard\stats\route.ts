import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { authOptions } from "@/lib/auth/auth-options";
import { isCensusOpen } from "@/lib/census/census-availability";
import { prisma } from "@/lib/db/prisma";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";

export async function GET(_request: Request) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require admin authentication
    if (!session || session.user.role !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get current and previous census years (5-year intervals)
    const censusYears = await prisma.censusYear.findMany({
      select: { year: true, id: true },
      orderBy: { year: "desc" },
      take: 2,
    });

    const currentCensusYear = censusYears[0];
    const previousCensusYear = censusYears[1];

    const currentYear = currentCensusYear?.year || new Date().getFullYear();
    const currentYearId = currentCensusYear?.id;

    // Get member statistics for current and previous census periods using Prisma
    const totalMembers = await prisma.member.count();

    const currentCensusMembers = currentCensusYear?.id
      ? await prisma.member.count({
          where: {
            householdMembers: {
              some: {
                censusYearId: currentCensusYear.id,
                isCurrent: true,
              },
            },
          },
        })
      : 0;

    const previousCensusMembers = previousCensusYear?.id
      ? await prisma.member.count({
          where: {
            householdMembers: {
              some: {
                censusYearId: previousCensusYear.id,
                isCurrent: true,
              },
            },
          },
        })
      : 0;

    const memberStats = [
      {
        total_members: totalMembers,
        current_census_members: currentCensusMembers,
        previous_census_members: previousCensusMembers,
      },
    ];

    // Get household statistics for current and previous census periods using Prisma
    const totalHouseholds = await prisma.household.count();

    const currentCensusHouseholds = currentCensusYear?.id
      ? await prisma.household.count({
          where: { lastCensusYearId: currentCensusYear.id },
        })
      : 0;

    const previousCensusHouseholds = previousCensusYear?.id
      ? await prisma.household.count({
          where: { lastCensusYearId: previousCensusYear.id },
        })
      : 0;

    const householdStats = [
      {
        total_households: totalHouseholds,
        current_census_households: currentCensusHouseholds,
        previous_census_households: previousCensusHouseholds,
      },
    ];

    // Get unique code statistics using Prisma
    const totalCodes = currentYearId
      ? await prisma.uniqueCode.count({
          where: { censusYearId: currentYearId },
        })
      : 0;

    const assignedCodes = currentYearId
      ? await prisma.uniqueCode.count({
          where: {
            censusYearId: currentYearId,
            isAssigned: true,
          },
        })
      : 0;

    const availableCodes = currentYearId
      ? await prisma.uniqueCode.count({
          where: {
            censusYearId: currentYearId,
            isAssigned: false,
          },
        })
      : 0;

    const uniqueCodeStats = [
      {
        total_codes: totalCodes,
        assigned_codes: assignedCodes,
        available_codes: availableCodes,
      },
    ];

    // Calculate census progress (percentage of households that have completed census) using Prisma
    let progress = 0;
    if (currentYearId) {
      const totalCurrentHouseholds = await prisma.household.count({
        where: { lastCensusYearId: currentYearId },
      });

      const completedForms = await prisma.censusForm.count({
        where: {
          censusYearId: currentYearId,
          status: "completed",
        },
      });

      progress =
        totalCurrentHouseholds > 0
          ? Math.round(((completedForms * 100.0) / totalCurrentHouseholds) * 10) / 10
          : 0;
    }

    const censusProgress = [{ progress }];

    // Get census status
    const censusStatus = await isCensusOpen();

    // Calculate growth percentages between census periods (e.g., 2018 to 2025)
    const memberGrowth = memberStats[0]?.previous_census_members
      ? Math.round(
          ((memberStats[0].current_census_members - memberStats[0].previous_census_members) /
            memberStats[0].previous_census_members) *
            100,
        )
      : 0;

    const householdGrowth = householdStats[0]?.previous_census_households
      ? Math.round(
          ((householdStats[0].current_census_households -
            householdStats[0].previous_census_households) /
            householdStats[0].previous_census_households) *
            100,
        )
      : 0;

    const dashboardStats = {
      members: {
        total: memberStats[0]?.total_members || 0,
        thisYear: memberStats[0]?.current_census_members || 0,
        growth: memberGrowth,
      },
      households: {
        total: householdStats[0]?.total_households || 0,
        thisYear: householdStats[0]?.current_census_households || 0,
        growth: householdGrowth,
      },
      uniqueCodes: {
        total: uniqueCodeStats[0]?.total_codes || 0,
        assigned: uniqueCodeStats[0]?.assigned_codes || 0,
        available: uniqueCodeStats[0]?.available_codes || 0,
      },
      census: {
        currentYear,
        isOpen: censusStatus.isOpen,
        progress: censusProgress[0]?.progress || 0,
      },
    };

    return NextResponse.json(dashboardStats);
  } catch (_error) {
    // Get locale for error translations
    const locale = await getLocaleFromCookies();
    const t = await getTranslations({ locale, namespace: "admin" });

    return NextResponse.json({ error: t("statsUpdateFailed") }, { status: 500 });
  }
}
