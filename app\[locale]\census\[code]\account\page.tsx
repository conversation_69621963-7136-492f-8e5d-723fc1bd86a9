import type { Metadata } from "next";
import { getTranslations } from "next-intl/server";
import { requireCensusAuth } from "@/lib/census-auth/census-auth-utils";
import { AccountClient } from "./account-client";

// Force dynamic rendering to prevent DYNAMIC_SERVER_USAGE errors
export const dynamic = "force-dynamic";

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("metadata");

  return {
    title: t("accountManagementTitle"),
    description: t("accountManagementDescription"),
  };
}

// This is needed for TypeScript in Next.js 15.3.1 with dynamic routes
export async function generateStaticParams() {
  return [{ code: "example" }];
}

/**
 * Account page for census participants
 *
 * This page allows census participants to manage their account settings
 * and perform account-related actions like deletion.
 *
 * It requires census authentication and uses the same security model
 * as the main census form page.
 */
export default async function AccountPage({ params }: { params: Promise<{ code: string }> }) {
  // Server-side authentication check
  const session = await requireCensusAuth();

  // Get the code from the URL params - await params in Next.js 15
  const resolvedParams = await params;
  const code = resolvedParams.code;

  // Pass the initial session data to the client component
  // This ensures we have data to display even before the client-side session is loaded
  const initialSessionData = {
    id: session.user.id,
    name: session.user.name,
    code: session.user.code,
    censusYearId: session.user.censusYearId,
    householdId: session.user.householdId || undefined,
  };

  // Use the client component to render the account page
  return <AccountClient code={code} initialSessionData={initialSessionData} />;
}
