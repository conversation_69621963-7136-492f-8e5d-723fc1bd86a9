/**
 * Client-side toast translation hook
 * Enhances existing toast handlers with translation support while maintaining
 * complete compatibility with the existing AlertContext system
 */

import { usePathname } from "next/navigation";
import { useLocale, useTranslations } from "next-intl";
import { useCallback } from "react";
import {
	createSecureErrorMessage,
	getAuthContextFromPath,
	getErrorKeyMapping,
} from "@/lib/utils/toast-translation-helpers";

/**
 * Hook for translating toast messages on the client side
 * This hook enhances the existing error handling without breaking changes
 */
export function useToastTranslation() {
	const pathname = usePathname();
	const currentLocale = useLocale(); // ✅ Use next-intl's built-in hook
	const authContext = getAuthContextFromPath(pathname);

	// Get translations for different namespaces
	const tAuth = useTranslations("auth");
	const tErrors = useTranslations("errors");

	/**
	 * Translates an auth error message
	 * Memoized to prevent infinite re-renders
	 *
	 * @param errorCode - The error code to translate
	 * @param fallbackMessage - Optional fallback message
	 * @returns string - The translated message
	 */
	const translateAuthError = useCallback(
		(errorCode: string, fallbackMessage?: string): string => {
			try {
				const errorKeys = getErrorKeyMapping("auth");
				const translationKey = errorKeys[errorCode] || errorKeys.default;

				// Get the translated message
				const translatedMessage = tAuth(translationKey as any);

				// Validate and return secure message
				return createSecureErrorMessage(
					translatedMessage,
					fallbackMessage || "Authentication error occurred",
				);
			} catch (error) {
				// Log error in development only
				if (process.env.NODE_ENV === "development") {
					console.warn("[ToastTranslation] Auth translation failed:", error);
				}

				return createSecureErrorMessage(
					fallbackMessage || "Authentication error occurred",
				);
			}
		},
		[tAuth],
	);

	/**
	 * Translates a census error message
	 * Memoized to prevent infinite re-renders
	 *
	 * @param errorCode - The error code to translate
	 * @param fallbackMessage - Optional fallback message
	 * @returns string - The translated message
	 */
	const translateCensusError = useCallback(
		(errorCode: string, fallbackMessage?: string): string => {
			try {
				const errorKeys = getErrorKeyMapping("census");
				const translationKey = errorKeys[errorCode] || errorKeys.default;

				// Census errors use auth namespace
				const translatedMessage = tAuth(translationKey as any);

				return createSecureErrorMessage(
					translatedMessage,
					fallbackMessage || "Census error occurred",
				);
			} catch (error) {
				if (process.env.NODE_ENV === "development") {
					console.warn("[ToastTranslation] Census translation failed:", error);
				}

				return createSecureErrorMessage(
					fallbackMessage || "Census error occurred",
				);
			}
		},
		[tAuth],
	);

	/**
	 * Translates a settings error message
	 * Memoized to prevent infinite re-renders
	 *
	 * @param errorCode - The error code to translate
	 * @param fallbackMessage - Optional fallback message
	 * @returns string - The translated message
	 */
	const translateSettingsError = useCallback(
		(errorCode: string, fallbackMessage?: string): string => {
			try {
				const errorKeys = getErrorKeyMapping("settings");
				const translationKey = errorKeys[errorCode] || errorKeys.default;

				// Settings errors use errors namespace
				const translatedMessage = tErrors(translationKey as any);

				return createSecureErrorMessage(
					translatedMessage,
					fallbackMessage || "Settings error occurred",
				);
			} catch (error) {
				if (process.env.NODE_ENV === "development") {
					console.warn(
						"[ToastTranslation] Settings translation failed:",
						error,
					);
				}

				return createSecureErrorMessage(
					fallbackMessage || "Settings error occurred",
				);
			}
		},
		[tErrors],
	);

	/**
	 * Translates any error message based on context
	 * This is a convenience function that determines the appropriate translation method
	 * Memoized to prevent infinite re-renders in components that depend on it
	 *
	 * @param errorCode - The error code to translate
	 * @param errorType - Optional error type override
	 * @param fallbackMessage - Optional fallback message
	 * @returns string - The translated message
	 */
	const translateError = useCallback(
		(
			errorCode: string,
			errorType?: "auth" | "census" | "settings",
			fallbackMessage?: string,
		): string => {
			// Determine error type from context if not provided
			const type = errorType || (authContext === "admin" ? "auth" : "census");

			switch (type) {
				case "auth":
					return translateAuthError(errorCode, fallbackMessage);
				case "census":
					return translateCensusError(errorCode, fallbackMessage);
				case "settings":
					return translateSettingsError(errorCode, fallbackMessage);
				default:
					return translateAuthError(errorCode, fallbackMessage);
			}
		},
		[
			authContext,
			translateAuthError,
			translateCensusError,
			translateSettingsError,
		],
	);

	/**
	 * Enhances a toast message with translation if it appears to be an error code
	 * This function can be used to enhance existing toast messages
	 * Memoized to prevent infinite re-renders
	 *
	 * @param message - The original message
	 * @param errorType - Optional error type
	 * @returns string - The enhanced/translated message
	 */
	const enhanceToastMessage = useCallback(
		(message: string, errorType?: "auth" | "census" | "settings"): string => {
			// If the message looks like an error code, try to translate it
			if (message && typeof message === "string") {
				// Check if it's a known error code
				const type = errorType || (authContext === "admin" ? "auth" : "census");
				const errorKeys = getErrorKeyMapping(type);

				if (errorKeys[message]) {
					return translateError(message, type, message);
				}
			}

			// Return the original message if it's not an error code
			return createSecureErrorMessage(message);
		},
		[authContext, translateError],
	);

	return {
		// Translation functions
		translateAuthError,
		translateCensusError,
		translateSettingsError,
		translateError,
		enhanceToastMessage,

		// Context information
		currentLocale,
		authContext,

		// Utility functions
		isTranslationEnabled: currentLocale !== "en",
	};
}
