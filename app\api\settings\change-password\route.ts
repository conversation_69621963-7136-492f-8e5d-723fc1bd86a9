import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { verifyPassword } from "@/lib/auth";
import { authOptions } from "@/lib/auth/auth-options";
import { prisma } from "@/lib/db/prisma";
import { getAdminById, updateAdminPassword } from "@/lib/db/users";
import { getZodErrorDetails, isZodError } from "@/lib/utils/error-handling";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";
import { createPasswordChangeSchema } from "@/lib/validation/auth";

export async function PUT(request: Request) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const t = await getTranslations({ locale, namespace: "errors" });
  const tNotifications = await getTranslations({
    locale,
    namespace: "notifications",
  });
  const tAdmin = await getTranslations({ locale, namespace: "admin" });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require authentication for updating password
    if (!session || session.user.role !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse and validate request body with translations
    const data = await request.json();
    const passwordChangeSchema = await createPasswordChangeSchema(locale);
    const validatedData = passwordChangeSchema.parse(data);

    // Get the admin from the database
    const adminId = Number.parseInt(session.user.id, 10);
    const admin = await getAdminById(adminId);

    if (!admin) {
      return NextResponse.json({ error: "Admin not found" }, { status: 404 });
    }

    // Verify current password using normal verification process
    const isCurrentPasswordValid = await verifyPassword(
      validatedData.currentPassword,
      admin.password,
    );

    if (!isCurrentPasswordValid) {
      return NextResponse.json({ error: "Current password is incorrect" }, { status: 400 });
    }

    try {
      // Update the password
      await updateAdminPassword(adminId, validatedData.newPassword);

      // Try to log the action (without sensitive data)
      try {
        await prisma.auditLog.create({
          data: {
            userType: "admin",
            userId: adminId,
            action: "change-password",
            entityType: "admins",
            entityId: adminId,
            newValues: JSON.stringify({ passwordChanged: true }),
            ipAddress: request.headers.get("x-forwarded-for") || "unknown",
          },
        });
      } catch (_logError) {
        // If logging fails, just log to console but don't fail the request
        if (process.env.NODE_ENV === "development") {
        }
      }

      return NextResponse.json({
        message: tNotifications("passwordChangedSuccessfully"),
      });
    } catch (_dbError) {
      return NextResponse.json({ error: t("passwordUpdateFailed") }, { status: 500 });
    }
  } catch (error) {
    // Handle validation errors
    if (isZodError(error)) {
      return NextResponse.json(
        {
          error: tAdmin("validationError"),
          details: getZodErrorDetails(error),
        },
        { status: 400 },
      );
    }

    return NextResponse.json({ error: t("passwordChangeFailed") }, { status: 500 });
  }
}
