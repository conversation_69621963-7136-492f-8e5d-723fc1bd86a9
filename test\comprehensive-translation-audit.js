#!/usr/bin/env node

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, "..");

console.log("🔍 WSCCC Census System - Comprehensive Translation Audit");
console.log("=====================================================\n");

// Configuration
const EXCLUDED_PATHS = [
  "node_modules",
  ".next",
  ".git",
  "dist",
  "build",
  "coverage",
  "test",
  "lang/en.d.json.ts", // Auto-generated file
];

const EXCLUDED_FILES = [
  "privacy-policy", // Explicitly excluded per user request
  "terms", // Explicitly excluded per user request
  "bible-quotes", // Explicitly excluded per user request
];

const INCLUDED_EXTENSIONS = [".tsx", ".ts", ".jsx", ".js"];

// Patterns to detect untranslated text
const HARDCODED_TEXT_PATTERNS = [
  // Direct string literals in JSX
  />\s*[A-Z][a-zA-Z\s]{3,}\s*</g,
  // String literals in attributes
  /(?:placeholder|title|alt|aria-label)=["'][A-Z][a-zA-Z\s]{3,}["']/g,
  // Button text and labels
  /(?:children|label):\s*["'][A-Z][a-zA-Z\s]{3,}["']/g,
  // Common hardcoded patterns
  /["'][A-Z][a-zA-Z\s]{5,}["']/g,
  // Short UI labels (like "Rows:", "Page:", etc.)
  /["'][A-Z][a-zA-Z]{2,}:["']/g,
  // Label components with short text
  /<Label[^>]*>([A-Z][a-zA-Z]{2,}:?)<\/Label>/g,
];

// Patterns that should be ignored (already translated or acceptable)
const IGNORE_PATTERNS = [
  /t\(['"`][^'"`]+['"`]\)/, // Translation function calls
  /useTranslations/, // Translation hooks
  /getTranslations/, // Server translation calls
  /className=/, // CSS classes
  /import\s+/, // Import statements
  /export\s+/, // Export statements
  /console\./, // Console statements
  /\/\*/, // Comments
  /\/\//, // Single line comments
  /\$\{/, // Template literals
  /process\.env/, // Environment variables
  /\.map\(/, // Array methods
  /\.filter\(/, // Array methods
  /\.find\(/, // Array methods
  /href=/, // Links (often URLs)
  /src=/, // Image sources
  /id=/, // HTML IDs
  /data-/, // Data attributes
  /aria-/, // ARIA attributes (when not user-facing)
  /role=/, // ARIA roles
  /type=/, // Input types
  /method=/, // HTTP methods
  /name=/, // Form field names
  /value=/, // Form values (often dynamic)
];

// Technical terms and math expressions that should NOT be translated
const TECHNICAL_TERMS = [
  /^[A-Z_]+$/, // Constants (e.g., "API_KEY", "MAX_SIZE")
  /^\d+(\.\d+)?[a-zA-Z]*$/, // Numbers with units (e.g., "100px", "2.5rem")
  /^[a-z]+\([^)]*\)$/, // Function calls (e.g., "parseInt()", "Math.max()")
  /^[a-z]+\.[a-z]+/, // Object properties (e.g., "window.location")
  /^\/[^/]+\/[gimuy]*$/, // Regex patterns
  /^\{[^}]*\}$/, // Object literals
  /^\[[^\]]*\]$/, // Array literals
  /^[a-zA-Z]+:\/\//, // URLs
  /^#[0-9a-fA-F]{3,8}$/, // Hex colours
  /^rgb\(/, // RGB colours
  /^rgba\(/, // RGBA colours
  /^hsl\(/, // HSL colours
  /^calc\(/, // CSS calc functions
  /^var\(/, // CSS variables
  /^\d+(\.\d+)?(px|em|rem|vh|vw|%|pt|pc|in|cm|mm|ex|ch|vmin|vmax)$/, // CSS units
  /^[+-]?\d+(\.\d+)?([eE][+-]?\d+)?$/, // Scientific notation
  /^0x[0-9a-fA-F]+$/, // Hexadecimal numbers
  /^0b[01]+$/, // Binary numbers
  /^0o[0-7]+$/, // Octal numbers
];

// Punctuation and symbols that should NOT be translated
const PUNCTUATION_SYMBOLS = [
  ":",
  ";",
  ",",
  ".",
  "!",
  "?",
  "(",
  ")",
  "[",
  "]",
  "{",
  "}",
  "<",
  ">",
  "/",
  "\\",
  "|",
  "-",
  "_",
  "+",
  "=",
  "*",
  "&",
  "%",
  "$",
  "#",
  "@",
  "^",
  "~",
  "`",
  '"',
  "'",
  "°",
  "±",
];

// Language names that should NOT be translated (always show in native language)
const LANGUAGE_NAMES = [
  "English",
  "Chinese",
  "中文",
  "Español",
  "Français",
  "Deutsch",
  "Italiano",
  "Português",
  "Русский",
  "日本語",
  "한국어",
  "العربية",
  "हिन्दी",
  "Türkçe",
  "Polski",
  "Nederlands",
  "Svenska",
  "Norsk",
];

/**
 * Check if a file should be excluded
 */
function shouldExcludeFile(filePath) {
  const relativePath = path.relative(projectRoot, filePath);

  // Check excluded paths
  if (EXCLUDED_PATHS.some((excludedPath) => relativePath.includes(excludedPath))) {
    return true;
  }

  // Check excluded files
  if (EXCLUDED_FILES.some((excludedFile) => relativePath.includes(excludedFile))) {
    return true;
  }

  // Check file extension
  const ext = path.extname(filePath);
  if (!INCLUDED_EXTENSIONS.includes(ext)) {
    return true;
  }

  return false;
}

/**
 * Check if a line should be ignored
 */
function shouldIgnoreLine(line) {
  return IGNORE_PATTERNS.some((pattern) => pattern.test(line));
}

/**
 * Check if text is a technical term that should not be translated
 */
function isTechnicalTerm(text) {
  const cleanText = text.replace(/['"<>]/g, "").trim();

  // Check against technical term patterns
  return TECHNICAL_TERMS.some((pattern) => pattern.test(cleanText));
}

/**
 * Check if text is only punctuation/symbols
 */
function isPunctuationOnly(text) {
  const cleanText = text.replace(/['"<>\s]/g, "").trim();
  return (
    cleanText.length > 0 && cleanText.split("").every((char) => PUNCTUATION_SYMBOLS.includes(char))
  );
}

/**
 * Check if text is a language name that should stay in native language
 */
function isLanguageName(text) {
  const cleanText = text.replace(/['"<>]/g, "").trim();
  return LANGUAGE_NAMES.includes(cleanText);
}

/**
 * Extract potential untranslated text from a line
 */
function extractUntranslatedText(line, lineNumber) {
  if (shouldIgnoreLine(line)) {
    return [];
  }

  const findings = [];

  HARDCODED_TEXT_PATTERNS.forEach((pattern, patternIndex) => {
    let match;
    while ((match = pattern.exec(line)) !== null) {
      const text = match[0];
      const cleanText = text.replace(/['"<>]/g, "").trim();

      // Additional filtering
      if (cleanText.length < 2) continue; // Too short
      if (/^\d+$/.test(cleanText)) continue; // Just numbers
      if (/^[A-Z_]+$/.test(cleanText)) continue; // Constants
      if (text.includes("className")) continue; // CSS classes
      if (text.includes("import")) continue; // Import statements
      if (isTechnicalTerm(cleanText)) continue; // Technical terms
      if (isPunctuationOnly(cleanText)) continue; // Only punctuation
      if (isLanguageName(cleanText)) continue; // Language names (should stay native)

      // Skip common single words that are often technical
      const singleWordTechnical = [
        "API",
        "URL",
        "HTTP",
        "JSON",
        "XML",
        "CSS",
        "HTML",
        "SQL",
        "UUID",
        "JWT",
        "QR",
      ];
      if (singleWordTechnical.includes(cleanText.toUpperCase())) continue;

      findings.push({
        line: lineNumber,
        text: cleanText,
        pattern: patternIndex,
        context: line.trim(),
      });
    }
  });

  return findings;
}

/**
 * Scan a single file for untranslated text
 */
function scanFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, "utf8");
    const lines = content.split("\n");
    const findings = [];

    lines.forEach((line, index) => {
      const lineFindings = extractUntranslatedText(line, index + 1);
      findings.push(...lineFindings);
    });

    return findings;
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error.message);
    return [];
  }
}

/**
 * Recursively scan directory
 */
function scanDirectory(dirPath) {
  const results = [];

  try {
    const items = fs.readdirSync(dirPath);

    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);

      if (stat.isDirectory()) {
        if (!shouldExcludeFile(itemPath)) {
          results.push(...scanDirectory(itemPath));
        }
      } else if (stat.isFile() && !shouldExcludeFile(itemPath)) {
        const findings = scanFile(itemPath);
        if (findings.length > 0) {
          results.push({
            file: path.relative(projectRoot, itemPath),
            findings,
          });
        }
      }
    }
  } catch (error) {
    console.error(`Error scanning directory ${dirPath}:`, error.message);
  }

  return results;
}

/**
 * Generate markdown report
 */
function generateMarkdownReport(results) {
  const totalFiles = results.length;
  const totalFindings = results.reduce((sum, result) => sum + result.findings.length, 0);

  let report = "# Translation Audit Report\n\n";
  report += `**Generated**: ${new Date().toISOString()}\n`;
  report += `**Files with issues**: ${totalFiles}\n`;
  report += `**Total findings**: ${totalFindings}\n\n`;

  if (totalFindings === 0) {
    report += "✅ No obvious untranslated text found!\n\n";
    return report;
  }

  report += "## Issues Found\n\n";

  results.forEach((result) => {
    report += `### ${result.file}\n\n`;
    result.findings.forEach((finding) => {
      report += `**Line ${finding.line}**: \`${finding.text}\`\n`;
      report += `Context: \`${finding.context}\`\n\n`;
    });
  });

  return report;
}

/**
 * Generate report
 */
function generateReport(results) {
  const totalFiles = results.length;
  const totalFindings = results.reduce((sum, result) => sum + result.findings.length, 0);

  console.log("📊 **AUDIT SUMMARY**");
  console.log(`Files scanned: ${totalFiles} files with potential issues`);
  console.log(`Total findings: ${totalFindings} potential untranslated strings`);
  console.log("");

  if (totalFindings === 0) {
    console.log("✅ No obvious untranslated text found! 🎉");
    console.log("");
    console.log("📝 **NOTE**: This audit checks for common patterns of hardcoded text.");
    console.log("   Manual review is still recommended for complete verification.");
    return;
  }

  console.log("⚠️  **POTENTIAL ISSUES FOUND**");
  console.log("");

  results.forEach((result, index) => {
    console.log(`📁 **${result.file}** (${result.findings.length} findings)`);

    result.findings.forEach((finding, findingIndex) => {
      console.log(`   Line ${finding.line}: ${finding.text}`);
      console.log(`   Context: ${finding.context}`);
      if (findingIndex < result.findings.length - 1) console.log("");
    });

    if (index < results.length - 1) {
      console.log("");
      console.log("---");
      console.log("");
    }
  });
}

// Main execution
const startTime = Date.now();
console.log("🚀 Starting comprehensive translation audit...\n");

const results = scanDirectory(projectRoot);
const endTime = Date.now();

console.log(`⏱️  Scan completed in ${endTime - startTime}ms\n`);

generateReport(results);

console.log("\n📋 **EXCLUSIONS APPLIED**");
console.log("- Privacy policy pages (as requested)");
console.log("- Terms of service pages (as requested)");
console.log("- Bible quotes (as requested)");
console.log("- Auto-generated files");
console.log("- Build and dependency directories");
console.log("- Translation function calls (already translated)");
console.log("- Technical terms (constants, functions, URLs, etc.)");
console.log("- Math expressions and scientific notation");
console.log("- Punctuation and symbols (:, ;, -, +, etc.)");
console.log("- CSS values and units (px, rem, colors, etc.)");
console.log("- Language names (English, 中文, etc. - stay in native language)");

console.log("\n💡 **RECOMMENDATIONS**");
console.log("1. Review each finding manually");
console.log("2. Add translation keys for user-facing text");
console.log("3. Use t() function for all user-visible strings");
console.log("4. Test in both English and Chinese");
console.log("5. Run build verification: npm run build");

console.log("\n🔍 **MANUAL REVIEW REQUIRED**");
console.log("The following files need manual inspection:");
console.log("- src/components/home/<USER>");
console.log("- middleware.ts (API error messages)");
console.log("- app/[locale]/admin/login/login-client.tsx (WSCCC Census text)");
console.log("- Language selector hardcoded labels");

console.log("\n📝 **SAVE REPORT**");
const reportPath = "translation-audit-report.md";
try {
  const reportContent = generateMarkdownReport(results);
  require("fs").writeFileSync(reportPath, reportContent, "utf8");
  console.log(`📄 Detailed report saved: ${reportPath}`);
} catch (error) {
  console.error("❌ Failed to save report:", error.message);
}
