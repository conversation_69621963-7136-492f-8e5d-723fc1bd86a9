"use client";

import { <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Respons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "recharts";
import { type BaseChartProps, registerChart } from "./chart-registry";
import { CHART_COLORS, CHART_DEFAULTS, MOBILE_CHART_CONFIGS } from "./constants";

interface PieDataPoint {
  name: string;
  value: number;
  [key: string]: unknown;
}

function PieChartComponent({ data, isAnimationActive = true, className }: BaseChartProps) {
  const config = data.config || {};
  const { showLegend = true } = config;

  // Check for responsive configuration
  const responsiveConfig = (data as any)._responsive;
  const isMobile = responsiveConfig?.isMobile;
  const chartHeight = responsiveConfig?.height || CHART_DEFAULTS.HEIGHT;
  const fontSize = responsiveConfig?.fontSize || MOBILE_CHART_CONFIGS.FONT_SIZES.DESKTOP;

  // Use data directly after type assertion
  const processedData = data.data as PieDataPoint[];

  // Calculate total for percentages
  const total = processedData.reduce((sum, item) => sum + item.value, 0);

  // Custom tooltip
  const CustomTooltip = ({
    active,
    payload,
  }: {
    active?: boolean;
    payload?: Array<{ payload: PieDataPoint & { percent: number } }>;
  }) => {
    if (active && payload && payload.length) {
      const data = payload[0]?.payload;
      if (!data || data.value === undefined || data.value === null) {
        return null;
      }

      const percentage = total > 0 ? ((data.value / total) * 100).toFixed(1) : "0";

      return (
        <div className="rounded-lg border border-slate-200 bg-white p-3 shadow-lg dark:border-slate-600 dark:bg-slate-800">
          <p className="mb-2 font-medium text-slate-900 dark:text-slate-100">{data.name}</p>
          <div className="space-y-1 text-sm">
            <div className="flex items-center gap-2">
              <span className="text-slate-600 dark:text-slate-400">Value:</span>
              <span className="font-medium text-slate-900 dark:text-slate-100">
                {data.value.toLocaleString()}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-slate-600 dark:text-slate-400">Percentage:</span>
              <span className="font-medium text-slate-900 dark:text-slate-100">{percentage}%</span>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  // Custom label function
  const renderLabel = ({ name, percent }: { name: string; percent: number }) => {
    return `${name}: ${(percent * 100).toFixed(1)}%`;
  };

  // Custom legend
  const CustomLegend = ({ payload }: { payload?: Array<{ color: string; value: string }> }) => {
    if (!(showLegend && payload)) {
      return null;
    }

    return (
      <div className="mt-4 flex flex-wrap justify-center gap-4 text-sm">
        {payload.map((entry, index) => (
          <div className="flex items-center gap-2" key={index}>
            <div className="h-3 w-3 rounded-sm" style={{ backgroundColor: entry.color }} />
            <span className="text-slate-700 dark:text-slate-300">{entry.value}</span>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className={`w-full ${className || ""}`}>
      <ResponsiveContainer height={chartHeight} width="100%">
        <PieChart>
          <Pie
            animationBegin={0}
            animationDuration={CHART_DEFAULTS.ANIMATION_DURATION}
            cx="50%"
            cy="50%"
            data={processedData}
            dataKey={data.valueKey || "value"}
            isAnimationActive={isAnimationActive}
            label={renderLabel}
            labelLine={false}
            nameKey={data.nameKey || "name"}
            outerRadius={isMobile ? "70%" : "80%"}
          >
            {processedData.map((_, index) => (
              <Cell
                fill={CHART_COLORS.PALETTE[index % CHART_COLORS.PALETTE.length]}
                key={`cell-${index}`}
              />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
          {showLegend && (
            <Legend
              content={<CustomLegend />}
              height={isMobile ? 60 : 80}
              verticalAlign="bottom"
              wrapperStyle={{
                paddingTop: isMobile ? "10px" : "20px",
                width: "100%",
                textAlign: "center",
                fontSize: `${fontSize.legend}px`,
              }}
            />
          )}
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
}

// Register the component
registerChart("pie", PieChartComponent);

export default PieChartComponent;
