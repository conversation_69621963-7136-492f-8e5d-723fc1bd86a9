import { useCallback, useEffect, useState } from "react";
import { useDebounce } from "./useDebounce";

interface SearchOptions {
  initialSearchTerm?: string;
  debounceDelay?: number;
  onSearch?: (searchTerm: string) => void;
}

interface SearchState {
  searchTerm: string;
  debouncedSearchTerm: string;
  isSearching: boolean;
  setSearchTerm: (term: string) => void;
  clearSearch: () => void;
}

/**
 * A custom hook for handling search functionality with debounce.
 *
 * @param options Search options
 * @returns Search state and handlers
 */
export function useSearch({
  initialSearchTerm = "",
  debounceDelay = 300,
  onSearch,
}: SearchOptions = {}): SearchState {
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);
  const [isSearching, setIsSearching] = useState(false);

  // Debounce the search term to avoid excessive API calls
  const debouncedSearchTerm = useDebounce(searchTerm, debounceDelay);

  // Clear the search term
  const clearSearch = useCallback(() => {
    setSearchTerm("");
  }, []);

  // Call the onSearch callback when the debounced search term changes
  useEffect(() => {
    if (onSearch) {
      setIsSearching(true);
      onSearch(debouncedSearchTerm);
      setIsSearching(false);
    }
  }, [debouncedSearchTerm, onSearch]);

  return {
    searchTerm,
    debouncedSearchTerm,
    isSearching,
    setSearchTerm,
    clearSearch,
  };
}
