// This file is auto-generated by next-intl, do not edit directly.
// See: https://next-intl.dev/docs/workflows/typescript#messages-arguments

declare const messages: {
  accessibility: {
    searchHouseholdsDescription: 'Search for households by suburb, head name, contact number, or unique code';
  };
  admin: {
    accessError: 'Access Error';
    accountDeleted: 'Account deleted successfully';
    actionCompletedSuccessfully: '{action} completed successfully';
    active: 'Active';
    activeStatus: '(Active)';
    addExtraLayerSecurity: 'Add an extra layer of security to your account';
    additionalMinutesForSubsequent: 'Additional minutes added for each subsequent census lockout';
    adminAuthProtectedBy2FA: 'Admin authentication is protected by 2FA and does not use rate limiting.';
    administrator: 'Administrator';
    adminLogin: 'Admin Login';
    ageDob: 'Age / DOB';
    ageGroupBreakdownByGender: 'Age group breakdown by gender';
    aiProcessing: 'AI Processing';
    alertType: 'Alert Type';
    allTables: 'All Tables';
    allYears: 'All Years';
    alternatives: 'Alternatives';
    analyticsAssistantForCensusData: 'Analytics Assistant for census data queries and insights';
    anErrorOccurredWhileGenerating: 'An error occurred while generating unique codes';
    announcementText: 'Announcement Text';
    announcementTextTooLong: 'Announcement text cannot exceed 1000 characters';
    archived: 'Archived';
    archivedStatus: '(Archived)';
    askAboutMembersHouseholdsOrUniqueCodes: 'Ask about members, households, or unique codes...';
    askAugust: 'Ask August';
    askMeAnythingAboutYourCensusD: 'Ask me anything about your census data...';
    auditLogsNotImplemented: 'Audit logs feature not implemented';
    augustIsThinking: 'August is thinking...';
    automaticallyOpenCloseBasedOnSchedule: 'Automatically open and close the census based on the schedule';
    automaticOpenClose: 'Automatic Open/Close';
    automaticSchedulingDisabled: 'Automatic scheduling is disabled';
    automaticSchedulingEnabled: 'Automatic scheduling is enabled';
    available: 'available';
    availablePlaceholders: 'Available placeholders';
    backToHome: 'Back to Home';
    backToLogin: 'Back to login';
    backupCode: 'Backup Code';
    backupExport: 'Backup & Export';
    cannotDeleteAssignedCodes: 'Cannot delete assigned codes';
    census: 'Census';
    censusAttemptsLockout: 'Census: {attempts} attempts → {minutes}min lockout';
    censusAuthenticationRateLimiting: 'Census Authentication Rate Limiting';
    censusAvailability: 'Census Availability';
    censusClosed: 'Closed';
    censusControlsUpdateFailed: 'Failed to update census controls';
    censusFormat: 'Census {year}';
    censusIsClosed: 'Census is closed';
    censusIsOpen: 'Census is open';
    censusLockoutDurations: 'Census lockout durations';
    censusOpen: 'Open';
    censusProgress: 'Census Progress';
    censusSessionCheckFailed: 'Failed to check census session';
    censusStatus: 'Census Status';
    censusStatusCheckFailed: 'Failed to check census status';
    censusYear: 'Census Year';
    censusYearFetchFailed: 'Failed to fetch census year settings';
    censusYearHelperText: 'This will be displayed to members when they fill out the census';
    censusYearUpdatedSuccessfully: 'Census year updated successfully';
    censusYearUpdatedSuccessfullyBoth: 'Census year updated successfully in both settings and census years table';
    changePassword: 'Change Password';
    changesApplyToNewLockoutsOnly: 'Changes will apply to new lockouts only. Existing locked accounts will maintain their current lockout duration.';
    chartElementNotAvailableForImageExport: 'Chart element not available for image export';
    chartElementRequired: 'Chart element required';
    chartExportedAs: 'Chart exported as {format}';
    chartFailedToRender: 'Chart is taking too long to render. Please try again or check if the chart is properly loaded.';
    chatbotAvailableData: 'Available Data';
    chatbotCodeStatus: 'Code Status';
    chatbotHouseholdsCategory: 'Households';
    chatbotHouseholdStats: 'Household Stats';
    chatbotLocationDesc: 'Location, census years, family relationships';
    chatbotLoginCodesDesc: 'Login codes, assignment status, household links';
    chatbotMemberCount: 'Member Count';
    chatbotMembersCategory: 'Members';
    chatbotPersonalInfoDesc: 'Personal info, demographics, contact details, hobbies';
    chatbotTips: 'Tips';
    chatbotUniqueCodesCategory: 'Unique Codes';
    chatError: 'Chat Error';
    chatErrorDescription: 'Something went wrong with the chat interface. This is usually a temporary issue.';
    chatErrorDetails: 'Error Details';
    chooseBackupFileToImport: 'Choose a database backup file to import (SQL, CSV, or JSON)';
    chooseFormatForExport: 'Choose the format for your database export';
    churchInformation: 'Church Information';
    churchInfoUpdatedSuccessfully: 'Church information updated successfully';
    churchName: 'Church Name';
    cleanExpiredSessions: 'Clean Expired Sessions';
    cleanExpiredSessionsWarning: 'This will remove all expired sessions from the database. This action cannot be undone.';
    cleanOldAuditLogs: 'Clean Old Audit Logs';
    cleanOldAuditLogsWarning: 'This will remove all audit logs older than 7 days from the database. This action cannot be undone.';
    clearConversation: 'Clear conversation';
    clickToCopy: 'Click to copy';
    code: 'Code';
    codes: 'codes';
    commaSeparatedValuesForSpreads: 'Comma-separated values for spreadsheets';
    configureBaseUrl: 'Configure the base URL for census participants';
    configureCensusYearDescription: 'Configure the current census year (last year is automatically set)';
    configureHomepageAnnouncementMessage: 'Configure announcement message displayed on the homepage with smart placeholders';
    configureInitialLockoutDuration: 'Configure initial census lockout duration';
    configureMaximumFailedAttempts: 'Configure maximum failed census attempts before lockout';
    configureSecurityThresholds: 'Configure security thresholds for failed census login attempts. Note: Admin login is protected by 2FA and does not use rate limiting.';
    confirmNewPassword: 'Confirm New Password';
    connectionError: 'Connection Error';
    connectionErrorDescription: 'Please check your connection and try again. If the problem persists, contact support.';
    contactNumber: 'Contact Number';
    controlWhenCensusAvailable: 'Control when the census is available to members';
    copied: 'Copied!';
    copyCode: 'Copy code';
    copyMessage: 'Copy message';
    createBackupForSafekeeping: 'Create a backup of your database for safekeeping';
    createdDate: 'Created Date';
    createNewUniqueCodesDescription: 'Create new unique codes for the {year} census year (Active).';
    createNewUniqueCodesDescriptionFallback: 'Create new unique codes for the current active census year.';
    createNewUniqueCodesNote: 'Note: Unique codes can only be generated for the active census year.';
    currentCensus: 'current census';
    currentCensusYear: 'Current Census Year';
    currentCensusYearMustBeGreater: 'Current census year must be greater than the last census year ({lastYear})';
    currentCensusYearRequired: 'Current census year is required';
    currentPassword: 'Current Password';
    currentPasswordIncorrect: 'Current password is incorrect';
    customExport: 'Custom Export';
    dashboardOverview: 'Your census management dashboard overview';
    databaseCleanupCompleted: 'Database cleanup completed successfully';
    databaseCleanupFailed: 'Database cleanup failed';
    databaseExportedSuccessfully: 'Database exported successfully';
    databaseImportedSuccessfully: 'Database imported successfully';
    databaseMaintenance: 'Database Maintenance';
    databaseStatusCheckFailed: 'Database status check failed';
    dataExport: 'Data Export';
    dataOnly: 'Data Only';
    dataTable: 'Data Table';
    deleteCodesConfirmation: 'This action will permanently delete {count} unique {codeText}. Only unassigned codes can be deleted.';
    deleteCount: 'Delete ({count})';
    deleteMessage: 'Delete message';
    deleteMessagesTitle: 'Delete Messages';
    deleteMessageTitle: 'Delete Message';
    deleteMultipleMessagesConfirmation: 'Are you sure you want to permanently delete {count} selected messages? This action cannot be undone and will remove the messages from the conversation history.';
    deleteSelected: 'Delete Selected';
    deleteSelectedMessages: 'Delete {count} selected message{plural}';
    deleteSingleMessageConfirmation: 'Are you sure you want to permanently delete this message? This action cannot be undone and will remove the message from the conversation history.';
    demographicsFetchFailed: 'Failed to fetch demographics data';
    deselectAll: 'Deselect all';
    deselectAllMessages: 'Deselect all messages';
    directAccessNotSupported: "This page should only be accessed from the 'Print Cards' button in the Unique Code Management page. Direct access is not supported for security reasons.";
    durationOfFirstLockout: 'Duration of the first census IP lockout period';
    emptyAnnouncementPreview: 'Announcement appears empty after processing placeholders';
    enable2FA: 'Enable 2FA';
    enableDisableCensusAccess: 'Enable or disable census access for all members';
    endDateTime: 'End Date & Time';
    endDateTimeTooltip: 'The date and time when the census will automatically close';
    enterAnnouncementText: 'Enter your announcement message...';
    enterBackupCode: 'Enter one of your backup codes';
    enterChurchAddress: 'Enter church address';
    enterChurchName: 'Enter church name';
    enterCurrentCensusYear: 'Enter current census year';
    enterCurrentPassword: 'Enter your current password';
    enterCurrentPasswordToVerify: 'Enter your current password to verify your identity';
    enterEmailAddress: 'Enter email address';
    enterFilename: 'Enter filename';
    enterFullUrl: 'Enter the full URL including http:// or https://';
    enterNewPassword: 'Enter new password';
    enterNumberOfCodes: 'Enter number of codes to generate';
    enterOneTimeCode: 'Enter the one-time code from your authenticator app';
    enterSixCharacterBackupCode: 'Enter one of your 6-character backup codes';
    enterSixDigitCode: 'Enter the 6-digit code from your authenticator app';
    enterYourPassword: 'Enter your password';
    enterYourUsername: 'Enter your username';
    errorDuringLogin: 'An error occurred during login';
    errorDuringVerification: 'An error occurred during verification';
    errorOccurredUpdatingSiteUrl: 'An error occurred while updating site URL';
    escalationDisabledAllLockouts: 'Escalation disabled - all census lockouts will be {minutes} minutes';
    escalationIncrementMinutes: 'Escalation Increment (minutes)';
    expiredSessions: 'Expired Sessions';
    exportChart: 'Export Chart';
    exportCsv: 'Export CSV';
    exportDatabase: 'Export Database';
    exported: 'Exported!';
    exportFormat: 'Export Format';
    exportFormatDescription: 'SQL format provides a complete backup that can be restored. CSV and JSON formats are useful for data analysis or migration.';
    exportInformation: 'Export Information';
    exporting: 'Exporting...';
    exportingRecords: 'Exporting {count} records...';
    exportOptions: 'Export Options';
    failed: 'Failed';
    failedToChangePassword: 'Failed to change password';
    failedToCopyMessage: 'Failed to copy message';
    failedToCreateData: 'Failed to create data';
    failedToCreatePrintSession: 'Failed to create print session';
    failedToDeleteData: 'Failed to delete data';
    failedToDeleteMessage: 'Failed to delete message';
    failedToDeleteUniqueCodes: 'Failed to delete unique codes';
    failedToExportData: 'Failed to export data';
    failedToExportDatabase: 'Failed to export database';
    failedToFetchChurchInformation: 'Failed to fetch church information';
    failedToFetchData: 'Failed to fetch data';
    failedToFetchHouseholdInformation: 'Failed to fetch household information';
    failedToFetchSiteUrl: 'Failed to fetch site URL';
    failedToFetchUniqueCodes: 'Failed to fetch unique codes';
    failedToGeneratePreview: 'Failed to generate announcement preview';
    failedToGenerateUniqueCodes: 'Failed to generate unique codes';
    failedToGetResponseFromAiAssist: 'Failed to get response from AI assistant';
    failedToImportDatabase: 'Failed to import database';
    failedToLoad2FASettings: 'Failed to load 2FA settings';
    failedToLoadCensusControlsSettings: 'Failed to load census controls settings';
    failedToLoadCensusYearSettings: 'Failed to load census year settings';
    failedToLoadChurchInfo: 'Failed to load church information';
    failedToLoadDeletionInfo: 'Failed to load deletion information';
    failedToLoadHomepageAnnouncementSettings: 'Failed to load homepage announcement settings';
    failedToLoadRateLimitSettings: 'Failed to load rate limit settings';
    failedToLoadSuburbs: 'Failed to load suburbs';
    failedToPerformAction: 'Failed to perform {action}';
    failedToRegenerateResponse: 'Failed to regenerate response';
    failedToUpdateCensusControls: 'Failed to update census controls';
    failedToUpdateCensusYear: 'Failed to update census year';
    failedToUpdateChurchInfo: 'Failed to update church information';
    failedToUpdateChurchInformation: 'Failed to update church information';
    failedToUpdateData: 'Failed to update data';
    failedToUpdateSiteUrl: 'Failed to update site URL';
    findSpecificCodes: 'Find specific codes or filter by multiple criteria';
    findSpecificHouseholds: 'Find specific households by suburb, head name, contact, or unique code. Press / to focus search.';
    findSpecificMembers: 'Find specific members by name, mobile, suburb, or other criteria. Press / to focus search.';
    firstPage: 'First Page';
    fullExport: 'Full Export';
    genderChart: 'Gender Chart';
    genderRepresentationByPercentage: 'Gender representation by percentage';
    generate: 'Generate';
    generateAndManageUniqueCodes: 'Generate and manage unique access codes for census participants';
    generateBetween1And1000: 'Generate between 1 and 1000 unique codes for the active census year only.';
    generateCodes: 'Generate Codes';
    generateUniqueCodes: 'Generate Unique Codes';
    goToUniqueCodeManagement: 'Go to Unique Code Management';
    help: 'Help';
    highQualityChartImage: 'High-quality chart image';
    homepage: 'Homepage';
    homepageAnnouncement: 'Homepage Announcement';
    homepageAnnouncementDisabled: 'Homepage Announcement Disabled';
    homepageAnnouncementEnabled: 'Homepage Announcement Enabled';
    homepageAnnouncementSettingsSaved: 'Homepage announcement settings saved successfully';
    homepageAnnouncementStatus: 'Homepage Announcement Status';
    household: 'household';
    householdCode: 'Household Code:';
    householdDeleteFailed: 'Failed to delete household';
    householdFetchFailed: 'Failed to fetch household data';
    householdHead: 'Household Head';
    householdManagement: 'Household Management';
    householdManagementDescription: 'View and edit existing households created through the census registration process';
    householdMembers: 'Household Members';
    households: 'Households';
    householdSearchFailed: 'Failed to search households';
    householdsOnly: 'Households Only';
    householdUpdatedSuccessfully: 'Household updated successfully';
    importData: 'Import Data';
    importDatabase: 'Import Database';
    importDataFromBackup: 'Import data from a backup file';
    importFailed: 'Import failed';
    importing: 'Importing...';
    importWillOverwriteWarning: 'Importing data will overwrite existing records. Make sure to backup your current database first.';
    info: 'Info';
    initialLockoutMinutes: 'Initial Lockout (minutes)';
    invalidConfirmationPhrase: 'Invalid confirmation phrase';
    invalidMemberId: 'Invalid member ID';
    invalidParameters: 'Invalid parameters';
    invalidRequestData: 'Invalid request data';
    invalidVerificationCode: 'Invalid verification code. Please try again.';
    lastCensusYear: 'Last Census Year';
    lastCensusYearHelperText: 'Last census year is read-only and automatically set based on the current year';
    lastPage: 'Last Page';
    lastUpdated: 'Last updated';
    loadingChart: 'Loading chart...';
    loadingDeletionInfo: 'Loading deletion information...';
    loadingMemberInfo: 'Loading member information...';
    loginFailed: 'Login failed';
    loginSuccessful: 'Login successful, redirecting...';
    logsOlderThan7Days: 'Logs Older Than 7 Days';
    mainNavigation: 'Main navigation';
    maintainYourDatabase: 'Maintain your database by cleaning up old sessions and audit logs';
    manualOverride: 'Manual Override';
    manualOverrideActive: 'Manual override active';
    manualOverrideWillReset: 'Manual override will reset on {time}';
    maxCodesPerPrintSession: 'Maximum of 100 codes allowed per print session';
    maximumAttempts: 'Maximum Attempts';
    member: 'member';
    memberDetails: 'Member Details';
    memberFetchFailed: 'Failed to fetch member';
    memberNotFound: 'Member not found';
    members: 'Members';
    membersManagement: 'Members Management';
    membersManagementSubtitle: 'Manage individual member records across all households in the WSCCC Census System.';
    membersOnly: 'Members Only';
    message: 'Message';
    messageCopied: 'Message copied!';
    messageDeleted: 'Message deleted successfully';
    messageDeletedSuccessfully: 'Message deleted successfully';
    messages: 'Messages';
    messagesDeleted: '{count} messages deleted successfully';
    messagesDeletedSuccessfully: '{count} messages deleted successfully';
    mobileChartHelp: 'Tap chart elements for details • Pinch to zoom';
    mobileNavigation: 'Mobile navigation';
    moreOptionsAndFormats: 'More options and formats';
    mustBeValidYear: 'Must be a valid year';
    needAdminLoginToChangePassword: 'You need to be logged in as an admin to change your password';
    needAdminLoginToSaveSettings: 'You need to be logged in as an admin to save settings';
    needAdminLoginToView2FASettings: 'You need to be logged in as an admin to view 2FA settings';
    needAdminLoginToViewSettings: 'You need to be logged in as an admin to view settings';
    needToBeLoggedInAsAdmin: 'You need to be logged in as an admin to view settings';
    networkErrorCheckConnection: 'Network error. Please check your connection and try again.';
    newBackupCodesGenerated: 'New backup codes generated';
    newPassword: 'New Password';
    nextPage: 'Next Page';
    nextScheduledChange: 'Next scheduled change: {time}';
    noCodeIdsProvided: 'No code IDs provided';
    noCodesFoundToPrint: 'No codes found to print. This page must be accessed through the proper workflow.';
    noFileSelectedForImport: 'No file selected for import';
    notSpecified: 'Not specified';
    number: 'No.';
    numberOfCodes: 'Number of Codes';
    numberOfFailedCensusAttempts: 'Number of failed census unique code attempts before IP lockout';
    openActionsMenu: 'Open actions menu';
    password: 'Password';
    passwordMustBeAtLeast8Characters: 'Password must be at least 8 characters long';
    percentOfMembers: '% of members';
    placeholderCensusDates: 'Shows census date range';
    placeholderCensusStatus: 'Shows current census status';
    placeholderChurchName: 'Shows church name';
    placeholderEndDate: 'Shows census end date';
    placeholderStartDate: 'Shows census start date';
    pleaseSelectFileToImport: 'Please select a file to import';
    pngImage: 'PNG Image';
    pressFocusSearch: 'Press / to focus';
    preview: 'Preview';
    previousPage: 'Previous Page';
    printConfirmDescription: 'You are about to print {count} selected codes. This will generate approximately {pages} page(s).';
    printingTips: 'Printing Tips';
    printingTipsDescription: 'For best results, set your printer to A4 paper size, landscape orientation, and disable any scaling options (print at 100% scale). Make sure to select "Print background colors and images" in your browser\'s print dialog.';
    printInstructions: 'Print Instructions';
    printInstructionsDescription: 'This page displays {count} unassigned unique codes ({pages} pages with 9 cards per page). Each card includes a QR code that, when scanned, will direct users to the census homepage with the code pre-filled.';
    printInstructionsText: 'Click the Print button above to print these cards. The layout is optimized for A4 paper. Cut along the dotted lines to separate the cards.';
    printSelectedCodes: 'Print Selected Codes';
    printSessionExpired: 'Your print session has expired or is invalid. Please select codes again from the Unique Code Management page.';
    printUniqueCodeCards: 'Print Unique Code Cards';
    qrCodeError: 'QR Code Error';
    queryExamples: 'Query Examples';
    quickExport: 'Quick Export';
    rawFormat: 'Raw Format';
    reenterNewPasswordToConfirm: 'Re-enter your new password to confirm';
    regenerateResponse: 'Regenerate response';
    regenerating: 'Regenerating...';
    regeneratingResponse: 'Regenerating response';
    regularMaintenanceCanImprove: 'Regular maintenance of your database can improve performance. Sessions are automatically expired after 8 hours of inactivity. Audit logs older than 7 days can be safely removed to free up space.';
    removeSacramentStatusFilter: 'Remove sacrament status filter';
    removeSearchFilter: 'Remove search filter';
    removeStatusFilter: 'Remove status filter';
    removeYearFilter: 'Remove year filter';
    requestTimedOut: 'Request timed out. Please try again.';
    reviewMembersCannotDelete: 'Please review the members that cannot be deleted and try again';
    sacramentParticipationOverview: 'Sacrament participation overview';
    sacraments: 'Sacraments';
    sacramentsDistribution: 'Sacraments Distribution';
    sacramentTypesFetchFailed: 'Failed to fetch sacrament types';
    scanThisQrCodeToAccessCensus: 'Scan this QR code to access the census';
    scheduleAvailability: 'Schedule Availability';
    scheduled: 'Scheduled';
    scheduledState: 'Scheduled state';
    scrollToBottom: 'Scroll to bottom';
    searchAndFilter: 'Search and Filter';
    searchCodes: 'Search codes...';
    securityNotice: 'Security Notice';
    selectAll: 'Select all';
    selectAllCodes: 'Select all codes';
    selectAllHouseholds: 'Select all households';
    selectAllMembers: 'Select all members';
    selectAllMessages: 'Select all messages';
    selectBackupFile: 'Select Backup File';
    selectCodesToPrint: 'Select codes to print';
    selectEndDateTime: 'Select end date and time';
    selectFormat: 'Select format';
    selectStartDateTime: 'Select start date and time';
    selectTables: 'Select tables';
    selectTablesToInclude: 'Select which tables you want to include in the export';
    selectYear: 'Select year';
    sendingMessage: 'Sending message...';
    sendMessage: 'Send message';
    settingsCensusControls: 'Census Controls';
    settingsDatabase: 'Database';
    settingsGeneral: 'General';
    settingsOnly: 'Settings Only';
    settingsSecurity: 'Security';
    settingsWouldBeSavedInProduction: 'Settings would be saved in production';
    settingsWouldBeSavedInProductionCensusYear: 'Settings would be saved in both system_settings and census_years tables in production. Database connection may not be available in development.';
    setToZeroToDisableEscalation: 'Set to 0 to disable escalation';
    showHelpAndQueryExamples: 'Show help and query examples';
    signIn: 'Sign In';
    signInToAccessDashboard: 'Sign in to access the admin dashboard';
    siteUrl: 'Site URL';
    siteUrlPlaceholder: 'https://your-domain.com';
    startDateTime: 'Start Date & Time';
    startDateTimeTooltip: 'The date and time when the census will automatically open';
    statsUpdateFailed: 'Failed to update statistics';
    statusAssigned: 'Status: Assigned';
    statusUnassigned: 'Status: Unassigned';
    success: 'Success';
    successfullyExported: 'Successfully exported {count} records';
    systemSettings: 'System Settings';
    systemSettingsDescription: 'Configure system settings, user accounts, and preferences.';
    table: 'Table';
    tablesToExport: 'Tables to Export';
    theseCodesWillBeUsed: 'These codes will be used by households to access the census form.';
    theseSettingsControlCensusResponse: 'These settings control how the census system responds to failed unique code attempts.';
    totalAuditLogs: 'Total Audit Logs';
    totalMembers: 'Total Members';
    totalSessions: 'Total Sessions';
    twoFactorAddsExtraLayer: 'Two-factor authentication adds an extra layer of security to your account';
    twoFactorAuthentication: 'Two-Factor Authentication';
    twoFactorAuthRequired: 'Two-factor authentication required';
    twoFactorDisabled: 'Two-factor authentication is disabled';
    twoFactorEnabled: 'Two-factor authentication is enabled';
    unauthorized: 'Unauthorized';
    uniqueCode: 'Unique Code';
    uniqueCodeCard: 'Unique Code Card';
    uniqueCodeManagement: 'Unique Code Management';
    uniqueCodes: 'Unique Codes';
    uniqueCodesGeneratedSuccessfully: 'Unique codes generated successfully';
    unknownError: 'Unknown error';
    unknownLoginError: 'Unknown login error';
    unknownVerificationError: 'Unknown verification error';
    updateAccountPassword: 'Update your account password';
    updateChurchContactInfo: "Update your church's contact information and address";
    updateMember: 'Update Member';
    updateMemberInformationFor: 'Update member information for {memberName}';
    updatePassword: 'Update Password';
    updating: 'Updating...';
    urgent: 'Urgent';
    urlForQrCodes: 'This URL will be used for QR codes and links';
    useAuthenticatorApp: 'Use authenticator app';
    useBackupCode: 'Use a backup code';
    username: 'Username';
    usingDefaultUrlDatabaseUnavailable: 'Using default URL - database connection may not be available';
    usingDefaultValuesDatabaseUnavailable: 'Using default values - database connection may not be available';
    validationError: 'Validation error';
    validationFailed: 'Validation failed';
    verificationCode: 'Verification Code';
    verificationFailed: 'Verification failed';
    verificationSuccessful: 'Verification successful, redirecting...';
    verify: 'Verify';
    verifying: 'Verifying...';
    viewDetailsForThisUniqueCode: 'View details for this unique code';
    warning: 'Warning';
    welcomeBack: 'Welcome back';
    whenEnabledAutomaticOpenClose: 'When enabled, the census will automatically open and close at the specified times';
    whenEnabledMembersCanAccess: 'When enabled, members can access and submit census data';
    whenEnabledNeedCodeFromApp: "When enabled, you'll need to enter a code from your authenticator app when signing in";
    year: 'Year';
    yearMustBeBetween2000And2100: 'Year must be between 2000 and 2100';
    years: 'years';
    yesImportDatabase: 'Yes, Import Database';
    yourUniqueCode: 'Your Unique Code:';
  };
  api: {
    aiServiceUnavailable: 'AI service temporarily unavailable. Please try again.';
    authenticationFailed: 'Authentication failed. Please try logging in again.';
    databaseUnavailable: 'Database temporarily unavailable. Please try again in a moment.';
    generalError: 'An error occurred while processing your request. Please try again.';
    invalidRequestFormat: 'Invalid request format. Please check your input and try again.';
    requestTimedOut: 'Request timed out. Please try again with a simpler query.';
    tooManyRequests: 'Too many requests. Please wait before trying again.';
  };
  auth: {
    accessDenied: 'Access denied';
    accountDeleted: 'Your household has been removed by an administrator. Please contact the church office if you believe this is an error';
    adminLogin: 'Admin Login';
    authenticationError: 'An error occurred during authentication';
    backToLogin: 'Back to Login';
    callbackError: 'Error during authentication callback';
    censusClosed: 'The census is currently closed. Please check back during the census period';
    censusCode: 'Census Code';
    censusError: 'An error occurred with the census';
    emailCreateAccountError: 'Error creating account with email';
    emailSigninError: 'Error sending login email';
    enterSixDigitCode: 'Enter your 6-digit code';
    errorDuringLogin: 'An error occurred during login';
    expiredCode: 'This census code has expired';
    invalidCode: 'The census code you entered is invalid';
    invalidCredentials: 'Invalid username or password';
    invalidTotpToken: 'Invalid TOTP token';
    login: 'Login';
    loginFailed: 'Login failed: {error}';
    loginSuccessful: 'Login successful, redirecting...';
    oauthAccountNotLinked: 'This email is already associated with another account';
    oauthCallbackError: 'Error during external provider callback';
    oauthCreateAccountError: 'Error creating account with external provider';
    oauthSigninError: 'Error signing in with external provider';
    password: 'Password';
    sessionExpired: 'Your session has expired';
    sessionRequired: 'You must be logged in to access this content';
    signIn: 'Sign In';
    signInToAccessDashboard: 'Sign in to access the admin dashboard';
    tooManyAttempts: 'Too many attempts. Please wait a few minutes before trying again';
    totpInvalid: 'Invalid two-factor authentication code';
    totpRequired: 'Two-factor authentication code required';
    twoFactorAuthCode: 'Two-Factor Authentication Code';
    twoFactorRequired: 'Two-factor authentication required';
    unauthenticated: 'Please log in to access the admin portal';
    unauthorized: 'Unauthorized';
    unknownLoginError: 'Unknown login error';
    usedCode: 'This census code has already been used';
    username: 'Username';
    verificationError: 'An error occurred during verification';
    verificationFailed: 'Verification failed';
  };
  brand: {
    name: 'WSCCC Census';
  };
  census: {
    adding: 'Adding...';
    addMember: 'Add Member';
    addNewMember: 'Add New Member';
    addNewSacrament: 'Add New Sacrament';
    addSacrament: 'Add Sacrament';
    allSacramentTypesAdded: 'All available sacrament types have already been added.';
    allSacramentTypesInUse: 'All available sacrament types are in use by other entries.';
    allSacramentTypesRecorded: 'All available sacrament types have already been recorded.';
    cannotAddMoreSacraments: 'You cannot add more than {count} sacraments (forms).';
    censusCurrentlyClosed: 'Census is Currently Closed';
    censusFormPageTitle: 'Census Form';
    checkBackNextPeriod: 'Please check back during the next census period.';
    clickAddMemberToStart: 'Click "Add Member" to add your first household member.';
    clickToCopy: 'Click to copy';
    communityFeedback: 'Community Feedback';
    communityFeedbackDescription: 'Help us improve our church community by sharing your thoughts, suggestions, and feedback.';
    complete: 'Complete';
    completed: 'Completed';
    completeHouseholdCensus: 'Complete your household census information for {year}.';
    confirmDeleteMember: 'Are you sure you want to delete this household member? This action cannot be undone.';
    contact: 'Contact';
    continue: 'Continue';
    continueToCensusForm: 'Continue to Census Form';
    continueWhereYouLeftOff: 'Continue where you left off';
    copyId: 'Copy ID';
    date: 'Date';
    dateOfBirth: 'Date of Birth';
    delete: 'Delete';
    duplicateSacramentTypes: 'Duplicate sacrament types selected. Each sacrament must be of a unique type.';
    edit: 'Edit';
    editMember: 'Edit Member';
    enterDetailsBelow: 'Scan your QR code to get started';
    enterDetailsToRegister: 'Enter the details below to register your household.';
    enterFirstName: 'Enter first name';
    enterHobby: 'Enter hobby';
    enterLastName: 'Enter last name';
    enterMemberDetailsAndSacraments: 'Enter member details and sacraments';
    enterOccupation: 'Enter occupation';
    enterPlaceOfSacrament: 'Enter place of sacrament';
    failedToAddHouseholdMember: 'Failed to add household member';
    failedToDeleteHouseholdMember: 'Failed to delete household member';
    failedToFetchHouseholdData: 'Failed to fetch household data';
    failedToFetchHouseholdMembers: 'Failed to fetch household members';
    failedToFetchSacraments: 'Failed to fetch sacraments';
    failedToLoadHouseholdData: 'Failed to load household data';
    failedToLoadSacraments: 'Failed to load sacraments';
    failedToUpdateFormStatus: 'Failed to update form status';
    failedToUpdateHouseholdMember: 'Failed to update household member';
    firstName: 'First Name';
    formAndHouseholdInfo: 'Form & Household Info';
    formInformation: 'Form Information';
    formStatus: 'Form Status';
    gender: 'Gender';
    helpAndFaq: 'Help & FAQ';
    hobbies: 'Hobbies';
    hobby: 'Hobby';
    householdComments: 'Household Comments';
    householdCommentsDescription: 'Add any additional comments about your household (optional).';
    householdDetailsDescription: 'Your household details and primary contact information.';
    householdHead: 'Household Head';
    householdInfoMissing: 'Household or census year information is missing';
    householdInformation: 'Household Information';
    householdMemberAddedSuccessfully: 'Household member added successfully';
    householdMemberDeletedSuccessfully: 'Household member deleted successfully';
    householdMembers: 'Household Members';
    householdMemberUpdatedSuccessfully: 'Household member updated successfully';
    householdRegisteredSuccessfully: 'Household registered successfully';
    householdRegistration: 'Household Registration';
    inProgress: 'In progress';
    keepCodeSafe: 'Keep this code safe for future logins.';
    lastName: 'Last Name';
    lastUpdated: 'Last Updated';
    manageHouseholdMembers: 'Manage your household members and their sacrament information';
    manageHouseholdMembersDescription: 'Manage your household members and their sacrament information.';
    memberInformation: 'Member Information';
    mobile: 'Mobile';
    noHouseholdMembersYet: 'No household members added yet';
    noSacramentsAddedYet: 'No sacraments added yet';
    noSacramentsRecordedYet: 'No sacraments recorded yet';
    notStarted: 'Not started';
    occupation: 'Occupation';
    personalInformation: 'Personal Information';
    place: 'Place';
    pleaseCheckBack: 'Please check back during the next census period';
    registerHousehold: 'Register Household';
    relationship: 'Relationship';
    removeMember: 'Remove Member';
    removeSacrament: 'Remove Sacrament';
    sacramentDate: 'Sacrament date cannot be in the future';
    sacramentDetails: 'Sacrament Details';
    sacraments: 'Sacraments';
    save: 'Save';
    saveAndContinue: 'Save and Continue';
    selectDate: 'Select date';
    selectType: 'Select Type';
    setUpYourHousehold: 'Set Up Your Household';
    submitCensus: 'Submit Census';
    suburb: 'Suburb';
    title: 'Census Form';
    uniqueCode: 'Unique Code';
    updateMember: 'Update';
    useAddSacramentButton: 'Use the "Add Sacrament" button at the bottom to record a sacrament';
    welcome: 'Welcome!';
    welcomeBack: 'Hi, {name}!';
    welcomeSetupMessage: 'Welcome! Please set up your household to begin the census process.';
    youAreEditing: 'You are editing: {name}';
    yourAccount: 'Your Account';
    yourCensusAccountDetails: 'Your census account details and identification information.';
    yourUniqueCodeIs: 'Your unique code is:';
  };
  common: {
    '2faQrCode': '2FA QR Code';
    accountLocked: 'Account locked. Time remaining: {time}';
    accountSettings: 'Danger Zone';
    acmeInc: 'Acme Inc';
    actions: 'Actions';
    add: 'Add';
    addNewHousehold: 'Add New Household';
    addNewSacrament: 'Add New Sacrament';
    adminPortal: 'Admin Portal';
    age: 'Age';
    ageDistribution: 'Age Distribution';
    ageStatistics: 'Age statistics';
    all: 'All';
    allGenders: 'All Genders';
    allRelationships: 'All Relationships';
    allSacraments: 'All Sacraments';
    allSacramentsRecorded: 'All sacrament types have been recorded';
    allSacramentTypesAdded: 'All sacrament types have been added';
    allStatuses: 'All Statuses';
    allYears: 'All Years';
    analyseMemberDemographics: 'Analyse member demographics';
    and: 'and';
    anErrorOccurredWhileSavingTheH: 'An error occurred while saving the household member';
    apply: 'Apply';
    areYouSure: 'Are you sure?';
    askQuestionsInNaturalLanguageA: 'Ask questions in natural language about members, households, or unique codes';
    assignmentPatterns: 'Assignment patterns';
    associatedUniqueCodesWillBeMar: 'Associated unique codes will be marked as unassigned';
    associatedUniqueCodeWillBeMark: 'Associated unique code will be marked as unassigned';
    attemptsLeft: 'attempts left';
    authenticationError: 'Authentication error';
    back: 'Back';
    backupCodes: 'Backup Codes';
    bulkDeleteMembers: 'Bulk Delete Members';
    byClickingContinue: 'By clicking continue, you agree to our';
    cancel: 'Cancel';
    canDelete: 'Can Delete';
    cannotAddMoreSacraments: 'Cannot add more sacraments';
    cannotDelete: 'Cannot Delete';
    censuscallbackurl: 'census-callback-url';
    censuscsrftoken: 'census-csrf-token';
    censusLockoutPatternPreview: 'Census Lockout Pattern Preview';
    censussessiontoken: 'census-session-token';
    censusYear: 'Census Year';
    censusYearBreakdown: 'Census year breakdown';
    changeLanguage: 'Change language';
    checkCodeAvailability: 'Check code availability';
    clear: 'Clear';
    clickQuotaddSacramentquotToRec: 'Click "Add Sacrament" to record a sacrament.';
    close: 'Close';
    closed: 'Closed';
    codeAssignmentStatus: 'Code assignment status';
    codeMustBe6CharactersLettersOr: 'Code must be 6 characters (letters or numbers)';
    codeMustBeAtLeast6Characters: 'Code must be at least 6 characters';
    codeMustBeAtMost6Characters: 'Code must be at most 6 characters';
    codesFoundSummary: '{count} {codeText} found{yearText}';
    codeUsageAnalysis: 'Code usage analysis';
    collapseSidebar: 'Collapse sidebar';
    communityGathering: 'Community gathering';
    completed: 'Completed';
    confirm: 'Confirm';
    confirmation: 'Confirmation';
    confirmDatabaseImport: 'Confirm Database Import';
    confirmPasswordMustBeAtLeast8C: 'Confirm password must be at least 8 characters';
    connectionEstablished: 'Connection established successfully';
    contact: 'Contact';
    continue: 'Continue';
    copyId: 'Copy ID';
    copyright: '© {year} WSCCC Census System. All rights reserved.';
    createdAt: 'Created At';
    credentials: 'Credentials';
    csvImportFailedErrorInstanceof: 'CSV import failed: ${error instanceof Error ? error.message : ';
    currentPasswordIsRequired: 'Current password is required';
    dataLoadedSuccessfully: 'Data loaded successfully';
    dataLoading: 'Loading data...';
    dateIsRequired: 'Date is required';
    dateOfBirth: 'Date of Birth';
    dateOptional: 'Date (Optional)';
    deletable: 'Deletable';
    delete: 'Delete';
    deleteAccount: 'Delete Account';
    deleteHousehold: 'Delete Household';
    deleteProject: 'Delete Project';
    deleteSacrament: 'Delete Sacrament';
    deleteSacramentConfirmation: 'Are you sure you want to delete {sacramentName} for {memberName}? This action cannot be undone.';
    deleteSelectedHouseholds: 'Delete Selected Households';
    deleting: 'Deleting...';
    designEngineering: 'Design Engineering';
    displaysTheMobileSidebar: 'Displays the mobile sidebar.';
    documentation: 'Documentation';
    done: 'Done';
    edit: 'Edit';
    editHousehold: 'Edit Household';
    editMember: 'Edit Member';
    editSacrament: 'Edit {sacramentName}';
    empty: 'Empty';
    enterDeleteMode: 'Enter delete mode';
    enterUniqueCode: 'Enter your unique code';
    error: 'Error';
    errorImportingCsvErrorInstance: 'Error importing CSV: ${error instanceof Error ? error.message : ';
    errorImportingJsonErrorInstanc: 'Error importing JSON: ${error instanceof Error ? error.message : ';
    errorImportingSqlErrorInstance: 'Error importing SQL: ${error instanceof Error ? error.message : ';
    exitDeleteMode: 'Exit delete mode';
    expandSidebar: 'Expand sidebar';
    export: 'Export';
    failedToCreateHousehold: 'Failed to create household';
    failedToDeleteHouseholds: 'Failed to delete households';
    failedToDeleteMembers: 'Failed to delete members';
    failedToFetchHouseholdDetails: 'Failed to fetch household details';
    failedToFetchMemberDetails: 'Failed to fetch member details';
    failedToFetchSacraments: 'Failed to fetch sacraments';
    failedToLoadDeletionInformatio: 'Failed to load deletion information';
    failedToLoadHouseholdDetails: 'Failed to load household details';
    failedToLoadMemberDetails: 'Failed to load member details';
    failedToLoadStatus: 'Failed to load census status';
    failedToUpdateHousehold: 'Failed to update household';
    failedToUpdateMember: 'Failed to update member';
    failedToUpdateRateLimitSetting: 'Failed to update rate limit settings';
    failedToValidateBulkDeleteOper: 'Failed to validate bulk delete operation';
    failedToValidateHouseholds: 'Failed to validate households';
    failedToVerify2fa: 'Failed to verify 2FA';
    familyRelationshipAnalysis: 'Family relationship analysis';
    familySizeAnalysis: 'Family size analysis';
    featureAvailableInfo: 'New feature available. Click to learn more.';
    fieldInFirstrecordAsRecord: '!(field in (firstRecord as Record';
    filter: 'Filter';
    filterByAgeCriteria: 'Filter by age criteria';
    filterByCensusYear: 'Filter by census year';
    filterByGender: 'Filter by gender';
    filterByRelationship: 'Filter by relationship';
    filterBySacramentStatus: 'Filter by sacrament status';
    filters: 'Filters';
    firstPage: 'First Page';
    firstTimeSetupInfo: "Welcome! Let's get you set up.";
    found: 'found';
    gender: 'Gender';
    genderDistribution: 'Gender Distribution';
    genderFilter: 'Gender';
    geographicDistribution: 'Geographic distribution';
    getStarted: 'Get Started';
    getTotalMemberCount: 'Get total member count';
    googleAuthenticator: 'Google Authenticator';
    goToNextPage: 'Go to next page';
    goToPreviousPage: 'Go to previous page';
    householdDetails: 'Household Details';
    householdHeadDetailsWillBeRemo: 'Household head details will be removed';
    householdHeadInformation: 'Household Head Information';
    householdHeads: 'Household heads';
    householdInformation: 'Household Information';
    householdInformationWillBeDele: 'Household information will be deleted';
    householdMembers: 'Household Members';
    householdsToDelete: 'Households to delete';
    id: 'ID';
    import: 'Import';
    important: 'Important';
    importantWarnings: 'Important Warnings';
    importSummary: 'Import Summary';
    importWarnings: 'Import Warnings';
    includesCloudBackup: '- Includes cloud backup';
    info: 'Information';
    infoMessage: 'Information: Please note this update.';
    inProgress: 'In Progress';
    introduction: 'Introduction';
    jsonImportFailedErrorInstanceo: 'JSON import failed: ${error instanceof Error ? error.message : ';
    largeDatasetDetected: 'Large Dataset Detected';
    largeFamilyAnalysis: 'Large family analysis';
    largeTableDetected: 'Large Table Detected';
    lastPage: 'Last Page';
    lastUpdated: 'Last Updated';
    loading: 'Loading...';
    loadingCensusStatus: 'Loading census status...';
    loadingDeletionInformation: 'Loading deletion information...';
    loadingHouseholdInformation: 'Loading household information...';
    loadingQrCode: 'Loading QR code...';
    loadingSuburbs: 'Loading suburbs...';
    lockedRemaining: 'Locked: {time} remaining';
    mainNavigation: 'Main navigation';
    manageYourCensusAccountSettings: 'Manage your census account settings and data.';
    membersLowercase: 'members';
    messageCannotBeEmpty: 'Message cannot be empty';
    microsoftAuthenticator: 'Microsoft Authenticator';
    mobile: 'Mobile';
    mobileNavigation: 'Mobile navigation';
    mobilePhone: 'Mobile Phone';
    na: 'N/A';
    name: 'Name';
    needAdminLoginToViewSettings: 'You need to be logged in as an admin to view settings';
    needToBeLoggedInAsAdmin: 'You need to be logged in as an admin';
    newPasswordMustBeAtLeast8Chara: 'New password must be at least 8 characters';
    next: 'Next';
    nextPage: 'Next Page';
    no: 'No';
    noMembersFound: 'No members found';
    noMembersInThisHousehold: 'No members in this household';
    noResultsFound: 'No results found';
    noSacraments: 'No Sacraments';
    noSacramentsAddedYet: 'No sacraments added yet';
    noSacramentsRecorded: 'No sacraments recorded';
    noSuburbsFound: 'No suburbs found.';
    notAssigned: 'Not assigned';
    notAvailable: 'N/A';
    notProvided: 'Not provided';
    notSpecified: 'Not specified';
    notStarted: 'Not Started';
    nowAlerttimestamp: 'now - alert.timestamp';
    offersMultideviceSync: '- Offers multi-device sync';
    ok: 'OK';
    open: 'Open';
    openFilterOptions: 'Open filter options';
    operationInProgress: 'Operation in progress, please wait...';
    optional: 'Optional';
    passwordIsRequired: 'Password is required';
    passwordsDoNotMatch: 'Passwords do not match';
    permanentlyDeleteYourAccountAnd: 'Permanently delete your account and all associated data. This action cannot be undone.';
    personalInformation: 'Personal Information';
    pickADate: 'Pick a date';
    pickDate: 'Pick date';
    placeOptional: 'Place (Optional)';
    platform: 'Platform';
    pleaseCheckTheFormForErrors: 'Please check the form for errors';
    pleaseEnterTheVerificationCode: 'Please enter the verification code from your authenticator app';
    previous: 'Previous';
    previousPage: 'Previous Page';
    print: 'Print';
    printCards: 'Print Cards';
    privacyPolicy: 'Privacy Policy';
    processing: 'Processing...';
    profileInformation: 'Profile Information';
    qrCodeError: 'QR Code Error';
    qrCodeFor: 'QR Code for {code}';
    queryCodesAssignmentStatus: 'Show me assigned vs unassigned codes';
    queryCodesAssignmentTrends: 'Show me code assignment trends';
    queryCodesByCensusYear: 'List codes by census year';
    queryCodesLinkedToHouseholds: 'How many codes are linked to households?';
    queryHouseholdsByCensusYear: 'List households by census year';
    queryHouseholdsBySuburb: 'Show me household distribution by suburb';
    queryHouseholdsLargeFamilies: 'Show me households with more than 4 members';
    queryHouseholdsSizeDistribution: 'Show me household size distribution';
    queryHouseholdsTopSuburbs: 'Which suburbs have the most households?';
    queryMembersAverageAge: 'What is the average age of members?';
    queryMembersBornAfter2000: 'List members born after 2000';
    queryMembersByRelationship: 'Show me members by relationship type';
    queryMembersGenderDistribution: 'Show me the gender distribution of members';
    quickActionCodeStatus: 'How many unique codes are available?';
    quickActionGenderChart: 'Show me member count by gender with a chart';
    quickActionMemberCount: 'How many members do we have in total?';
    rateLimitSettingsUpdatedSucces: 'Rate limit settings updated successfully';
    recommendedAuthenticatorApps: 'Recommended Authenticator Apps';
    record: '| Record';
    recordAsRecord: '(record as Record';
    refresh: 'Refresh';
    regularMembers: 'Regular members';
    relationship: 'Relationship';
    relationshipFilter: 'Relationship';
    remove: 'Remove';
    requestComparisonsTrendsAndSta: 'Request comparisons, trends, and statistical analysis';
    required: 'Required';
    reset: 'Reset';
    resetFilters: 'Reset Filters';
    retry: 'Retry';
    rows: 'Rows';
    sacramentLimitReached: 'Cannot add more sacraments - limit reached';
    sacraments: 'Sacraments';
    sacramentStatus: 'Sacrament Status';
    sacramentType: 'Sacrament Type';
    salesMarketing: 'Sales & Marketing';
    save: 'Save';
    saveTheseBackupCodesInASecureL: 'Save these backup codes in a secure location. Each code can only be used once.';
    saving: 'Saving...';
    scanQrCode: 'Scan QR Code';
    scanTheQrCodeWithYourAuthentic: 'Scan the QR code with your authenticator app';
    scanThisQrCodeToAccessTheCensus: 'Scan this QR code to access the census';
    scanThisQrCodeWithYourAuthenti: 'Scan this QR code with your authenticator app';
    search: 'Search';
    searchHouseholds: 'Search households';
    searchingSuburbs: 'Searching suburbs...';
    searchMembers: 'Search members';
    select: 'Select';
    selectAll: 'Select All';
    selected: 'selected';
    shareProject: 'Share Project';
    sidebar: 'Sidebar';
    simpleAndWidelyUsed: '- Simple and widely used';
    someSacraments: 'Some Sacraments';
    sort: 'Sort';
    sqlImportFailedErrorInstanceof: 'SQL import failed: ${error instanceof Error ? error.message : ';
    sqlImportNotYetImplementedWith: 'SQL import not yet implemented with Prisma. Please use JSON or CSV format.';
    startCensus: 'Start Census';
    statusAssigned: 'Assigned';
    statusUnassigned: 'Unassigned';
    submit: 'Submit';
    suburb: 'Suburb';
    success: 'Success';
    systemReady: 'System is ready';
    termsAndConditions: 'Terms and Conditions';
    termsOfService: 'Terms of Service';
    theAiWillOnlyAnswerQuestionsAb: 'The AI will only answer questions about census database tables';
    today: 'Today';
    toggle: 'Toggle';
    toggleMenu: 'Toggle menu';
    toggleMobileMenu: 'Toggle mobile menu';
    toggleSidebar: 'Toggle Sidebar';
    toggleTheme: 'Toggle theme';
    topSuburbsByHouseholdCount: 'Top suburbs by household count';
    total: 'Total';
    totalSelected: 'Total Selected';
    ttlNumberPromise: ', ttl?: number): Promise';
    twofactorAuthenticationDisable: 'Two-factor authentication disabled successfully';
    twofactorAuthenticationSetting: 'Two-factor authentication settings saved';
    twoFactorRequired: 'Two-factor authentication required';
    unableToLoadSacramentInformati: 'Unable to load sacrament information';
    uniqueCode: 'Unique Code';
    unknown: 'Unknown';
    unknownError: 'Unknown error';
    unknownErrorOccurred: 'Unknown error occurred';
    updateHouseholdInformationForId: 'Update household information for ID: {id}';
    usernameIsRequired: 'Username is required';
    useSpecificCriteriaLikeAgeRang: 'Use specific criteria like age ranges, suburbs, or time periods';
    usingDefaultValuesDatabaseUnavailable: 'Using default values - database unavailable';
    validating: 'Validating...';
    validatingBulkDeleteOperation: 'Validating bulk delete operation...';
    validatingHouseholdsForDeletio: 'Validating households for deletion...';
    validationError: 'Validation error';
    verificationCodeMustBe6Digits: 'Verification code must be 6 digits';
    view: 'View';
    viewProject: 'View Project';
    warning: 'Warning';
    welcome: 'Welcome';
    wscccCensus: 'WSCCC Census';
    yes: 'Yes';
    youCanUseAnyOfTheseAuthenticat: 'You can use any of these authenticator apps:';
    youNeedToBeLoggedInAsAnAdminTo: 'You need to be logged in as an admin to save settings';
  };
  dialogs: {
    allMemberDetails: 'All member details';
    anySacramentRecords: 'Any sacrament records';
    areYouSure: 'Are you sure?';
    associatedUniqueCodeWillBeUnassigned: 'Associated unique code will be marked as unassigned';
    cannotDeleteCount: 'Cannot Delete ({count})';
    cannotDeleteHousehold: 'Cannot Delete Household';
    cannotDeleteHouseholdHead: 'Cannot Delete Household Head';
    cannotDeleteMember: 'Cannot Delete Member';
    cannotProceedBulkDeleteResolveIssues: 'Cannot proceed with bulk delete. Please resolve the issues above or remove the problematic members from your selection.';
    confirmDelete: 'Confirm Delete';
    deleteAccount: 'Delete Account';
    deleteCountHouseholds: 'Delete {count} Households';
    deleteEntireHousehold: 'Delete Entire Household';
    deleteHousehold: 'Delete Household';
    deleteMember: 'Delete Member';
    deleteNowPlaceholder: 'DELETE NOW';
    failedToDeleteHousehold: 'Failed to delete household';
    failedToDeleteMember: 'Failed to delete member';
    householdsNotFoundWarning: 'These households could not be found or validated.';
    householdsUnavailable: '{count} households unavailable';
    householdsWillBeCompletelyDeleted: '{count} {households} will be completely deleted';
    householdsWillBeRemovedAndCodesUnassigned: '{count} {households} will be completely removed and their unique codes will be marked as unassigned.';
    householdsWithMembers: '{count} households with members';
    householdsWithMembersWarning: 'These households have members other than the household head and cannot be deleted.';
    isHouseholdHeadWithOtherMembers: '{memberName} is the household head with other members.';
    isOnlyMemberInHousehold: '{memberName} is the only member in this household.';
    memberAndHouseholdDeletedSuccessfully: 'Member and household deleted successfully';
    memberDeletedSuccessfully: 'Member deleted successfully';
    noHouseholdsCanBeDeleted: 'No households can be deleted. Please resolve the issues above before proceeding.';
    pleaseDeleteNonHeadMembers: 'Please delete the non-head members first';
    pleaseDeleteOtherMembersFirst: 'Please delete other household members first, or transfer headship to another member before deleting.';
    pleaseReviewInformationBelow: 'Please review the information below before proceeding.';
    pleaseReviewInformationBulkDelete: 'Please review the information below before proceeding with the bulk delete.';
    reviewHouseholdsForDeletion: 'Review the households that will be deleted.';
    someHouseholdsCannotBeDeleted: 'Some households cannot be deleted at this time.';
    someMembersCannotBeDeletedReview: 'Some members cannot be deleted. Please review the details below.';
    thisActionCannotBeUndone: 'This action cannot be undone';
    thisActionCannotBeUndoneHouseholdRemoved: 'This action cannot be undone. The household will be removed from the system.';
    thisActionCannotBeUndoneMemberRemoved: 'This action cannot be undone. The member will be removed from their household.';
    thisMemberCannotBeDeleted: 'This member cannot be deleted at this time.';
    thisWillDeleteEntireHousehold: 'This will delete the entire household';
    thisWillPermanentlyDelete: 'This will permanently delete {memberName} from the system.';
    thisWillPermanentlyDeleteMembers: 'This will permanently delete {count} {members} from the system.';
    typeDeleteNow: 'Type DELETE NOW to confirm';
    whatWillBeDeleted: 'What will be deleted:';
    willDeleteCount: 'Will Delete ({count})';
    yourHouseholdInformation: 'Your household information';
    yourUniqueCode: 'Your unique code';
  };
  emptyStates: {
    clickAddMember: 'Click "Add Member" to add your first household member';
    commandPalette: 'Command Palette';
    noDataAvailable: 'No data available';
    noHouseholdMembersAdded: 'No household members added yet';
    noHouseholdsFound: 'No households found';
    noMembersFound: 'No members found';
    noSacramentsRecorded: 'No sacraments recorded';
    noUniqueCodesFound: 'No unique codes found';
    retrying: 'Retrying...';
    retryLoad: 'Retry Load';
    status: 'Status';
    tryAdjustingSearch: 'Try adjusting your search or filters';
    unableToLoadSacramentInfo: 'Unable to load sacrament information';
  };
  errors: {
    accountDeleted: 'Account deleted';
    AccountDeleteFailed: 'Failed to delete account';
    adminNotFound: 'Admin not found';
    AiResponseFailed: 'Failed to get response from AI assistant';
    aiServiceUnavailable: 'AI service temporarily unavailable. Please try again.';
    anErrorOccurredWhileDeletingUniqueCodes: 'An error occurred while deleting unique codes';
    AuthenticationError: 'An error occurred during authentication';
    authenticationFailed: 'Authentication failed. Please try logging in again.';
    backToHome: 'Back to Home';
    BackupCodeCopyFailed: 'Failed to copy backup codes';
    BackupCodeFetchFailed: 'Failed to fetch backup codes';
    BackupCodeGenerationFailed: 'Failed to generate new backup codes';
    BulkDeleteFailed: 'Failed to delete members';
    cannotDeleteAssignedCodes: 'Cannot delete assigned codes';
    cannotDeleteAssignedCodesWithList: 'Cannot delete assigned codes: {codes}';
    cannotDeleteHouseholdHead: 'Cannot delete the household head';
    cannotDeleteHouseholdHeadWhenO: 'Cannot delete household head when other members exist. Please delete other members first or transfer headship.';
    cannotDeleteHouseholdWithMembers: 'Cannot delete household with members other than the household head';
    censusToast: 'census_toast';
    censusYearFetchFailed: 'Failed to fetch census year settings';
    censusYearIdRequired: 'Census year ID is required';
    censusYearUpdatedSuccessfullyInBothTables: 'Census year updated successfully in both settings and census years table';
    censusYearUpdateFailed: 'Failed to update census year settings';
    ChartElementNotAvailable: 'Chart element not available for image export';
    ChartExportFailed: 'Failed to export chart';
    churchInfoFetchFailed: 'Failed to fetch church information';
    churchInformationUpdatedSuccessfully: 'Church information updated successfully';
    churchInfoUpdateFailed: 'Failed to update church information';
    criticalError: 'Critical Error';
    criticalErrorDescription: "We're sorry, but there was a critical error in the application.";
    databaseConnectionError: 'Database connection error';
    databaseExportedSuccessfullyAs: 'Database exported successfully as {format}';
    databaseExportFailed: 'Failed to export database';
    DatabaseImportFailed: 'Failed to import database';
    databaseServiceActionFailed: 'Failed to perform database service action';
    databaseStatusGetFailed: 'Failed to get database status';
    databaseTemporarilyUnavailable: 'Database temporarily unavailable';
    DataLoadFailed: 'Failed to load data';
    deleteEntireHouseholdAndUnassignCode: 'This will delete the entire household and mark the associated unique code as unassigned.';
    DuplicateSacramentTypes: 'Duplicate sacrament types not allowed';
    duplicateUniqueCodeGeneratedPl: 'Duplicate unique code generated. Please try again.';
    emergencyResetConnectionFailed: 'Failed to reset connection pool';
    errorDescription: "We're sorry, something went wrong on our end. Please try again or contact support if the problem persists.";
    errorDetails: 'Error Details';
    errorDetailsDevOnly: 'Error details (development only)';
    errorOccurredDuringRegistration: 'An error occurred during registration';
    errorOccurredUpdatingSiteUrl: 'An error occurred while updating site URL';
    errorProcessingRequest: 'An error occurred while processing your request';
    ExportValidationFailed: 'Export validation failed';
    failedToCopyCode: 'Failed to copy code';
    failedToCreateBackupBeforeImpo: 'Failed to create backup before import';
    failedToCreateHousehold: 'Failed to create household';
    failedToCreateHouseholdMember: 'Failed to create household member';
    failedToCreateHouseholdMemberRelationship: 'Failed to create household member relationship';
    failedToCreatePrintSession: 'Failed to create print session';
    failedToCreateSacrament: 'Failed to create sacrament';
    failedToDeleteAnyMembers: 'Failed to delete any members';
    failedToDeleteHouseholdMember: 'Failed to delete household member';
    failedToDeleteMember: 'Failed to delete member';
    failedToDeleteUniqueCodes: 'Failed to delete unique codes';
    failedToDisable2fa: 'Failed to disable 2FA';
    failedToDisableTwoFactor: 'Failed to disable 2FA';
    failedToFetchActiveCensusYear: 'Failed to fetch active census year';
    failedToFetchBackupCodes: 'Failed to fetch backup codes';
    failedToFetchCensusRateLimitSt: 'Failed to fetch census rate limit status';
    failedToFetchCensusStatusRespo: 'Failed to fetch census status (HTTP {status})';
    failedToFetchCensusYears: 'Failed to fetch census years';
    failedToFetchChurchInformation: 'Failed to fetch church information';
    failedToFetchData: 'Failed to fetch data';
    failedToFetchDatabaseInfo: 'Failed to fetch database service information';
    failedToFetchDeletionInfo: 'Failed to fetch deletion info';
    failedToFetchHouseholdMembers: 'Failed to fetch household members';
    failedToFetchRateLimitSettings: 'Failed to fetch rate limit settings';
    failedToFetchSacraments: 'Failed to fetch sacraments';
    failedToFetchSacramentTypes: 'Failed to fetch sacrament types';
    failedToFetchSiteUrl: 'Failed to fetch site URL';
    failedToFetchTwoFactorStatus: 'Failed to fetch 2FA status';
    failedToFetchUniqueCodes: 'Failed to fetch unique codes';
    failedToGenerateNewBackupCodes: 'Failed to generate new backup codes';
    failedToGenerateQrCode: 'Failed to generate QR code';
    failedToGenerateSecureUniqueCo: 'Failed to generate secure unique code';
    failedToGenerateUniqueCodes: 'Failed to generate unique codes';
    failedToGetHouseholdDeletionInfo: 'Failed to get household deletion info';
    failedToLoadCensusControlsSettings: 'Failed to load census controls settings';
    failedToLoadCensusYearSettings: 'Failed to load census year settings';
    failedToLoadChurchInfo: 'Failed to load church information';
    failedToLoadDashboardData: 'Failed to load dashboard data';
    failedToLoadData: 'Failed to load data';
    failedToLoadRateLimitSettings: 'Failed to load rate limit settings';
    failedToPrepareCodeCard: 'Failed to prepare code card. Please try again.';
    failedToPrepareCodesForPrinting: 'Failed to prepare codes for printing: {error}';
    failedToProcessBulkDelete: 'Failed to process bulk delete';
    failedToRegisterHousehold: 'Failed to register household';
    failedToRetrievePrintSession: 'Failed to retrieve print session';
    failedToSetupTwoFactor: 'Failed to set up 2FA';
    failedToStart2faSetup: 'Failed to start 2FA setup';
    failedToUpdateCensusYear: 'Failed to update census year';
    failedToUpdateHouseholdMember: 'Failed to update household member';
    failedToUpdateMember: 'Failed to update member';
    failedToUpdateRateLimitSettings: 'Failed to update rate limit settings';
    failedToUpdateUniqueCode: 'Failed to update unique code';
    failedToValidateBulkDelete: 'Failed to validate bulk delete';
    failedToVerifyTwoFactor: 'Failed to verify 2FA';
    forbidden: 'Forbidden';
    hide: 'Hide';
    hideDetails: 'Hide Details';
    householdAlreadyDeleted: 'Household {householdId} was already deleted';
    householdCreateFailed: 'Failed to create household';
    householdMemberCreatedSuccessfully: 'Household member created successfully';
    householdMemberDeletedSuccessfully: 'Household member deleted successfully';
    householdMemberUpdatedSuccessfully: 'Household member updated successfully';
    householdNotFound: 'Household not found';
    householdNotRegistered: 'Household not registered';
    householdRemovedByAdmin: 'Your household has been removed by an administrator';
    householdSearchFailed: 'Failed to search households';
    householdUpdateFailed: 'Failed to update household';
    incompleteData: 'Please complete all required fields';
    invalidAddress: 'Please enter a valid address';
    invalidChurchName: 'Church name cannot contain digits';
    invalidConfirmationPhrase: 'Invalid confirmation phrase';
    invalidContactNumber: 'Contact number must be exactly 10 digits and contain only numbers';
    invalidEmail: 'Please enter a valid email address';
    invalidEscalationminutesMustBe: 'Invalid escalationMinutes: must be integer between 0 and 120';
    invalidHouseholdId: 'Invalid household ID';
    invalidJsonInRequestBody: 'Invalid JSON in request body';
    invalidJsonRequest: 'Invalid JSON in request body';
    invalidJsonResponseFromServer: 'Invalid JSON response from server';
    invalidLockoutminutesMustBeInt: 'Invalid lockoutMinutes: must be integer between 1 and 1440';
    invalidMaxattemptsMustBeIntege: 'Invalid maxAttempts: must be integer between 1 and 20';
    invalidMemberId: 'Invalid member ID';
    invalidParameters: 'Invalid parameters';
    invalidRequestData: 'Invalid request data';
    invalidRequestFormat: 'Invalid request format';
    invalidResponseFromServer: 'Invalid response from server';
    invalidSearchQuery: 'Invalid search query';
    invalidServerResponse: 'Invalid response from server. Please try again.';
    InvalidServerResponse: 'Invalid response from server';
    invalidSessionTokenMustBeANone: 'Invalid session token: must be a non-empty string';
    invalidSessionTokenTooLong: 'Invalid session token: too long';
    invalidUniqueCode: 'Invalid unique code';
    memberAlreadyDeletedDuringTransaction: 'Member {memberId} was already deleted during transaction';
    memberAlreadyDeletedOrNotExists: "Member {memberId} already deleted or doesn't exist";
    memberBulkDeleteFailed: 'Failed to process bulk delete';
    memberCannotHaveMoreThanMaxSacraments: 'Member cannot have more than {max} sacraments';
    memberDeletedByAnotherProcess: 'Member {memberId} was deleted by another process during transaction';
    memberDeleteFailed: 'Failed to delete member';
    memberDeletionInfoFailed: 'Failed to get member deletion info';
    memberFetchFailed: 'Failed to fetch member';
    memberIdAndSacramentTypeRequired: 'Member ID and sacrament type are required';
    memberIdRequired: 'Member ID is required';
    memberNotFound: 'Member not found';
    memberNotFoundInThisHousehold: 'Member not found in this household';
    memberSacramentsFetchFailed: 'Failed to fetch member sacraments';
    memberSearchFailed: 'Failed to search members';
    memberUpdateFailed: 'Failed to update member';
    MessageCopyFailed: 'Failed to copy message';
    MessageDeleteFailed: 'Failed to delete message';
    networkError: 'Network error occurred. Please try again';
    networkErrorPleaseCheckYourCon: 'Network error. Please check your connection and try again.';
    noActiveCensusYearFound: 'No active census year found';
    noBackupCodesFound: 'No backup codes found';
    noCodesFound: 'No codes found';
    noCodesFoundDetails: 'No codes were found in your print session. Please select codes from the Unique Code Management page.';
    noCodesFoundPrintError: 'No codes found. Please select codes from the Unique Code Management page.';
    noCodesSelectedForPrinting: 'No codes selected for printing';
    NoFileSelected: 'No file selected for import';
    noHouseholdIdReturned: 'Server did not return a household ID';
    noPrintSessionFound: 'No print session found';
    noSessionTokenFound: 'No session token found';
    pageNotFound: 'Page not found';
    pageNotFoundDescription: "The page you're looking for doesn't exist or has been moved.";
    pageNotFoundTitle: 'Page Not Found';
    passwordChangeFailed: 'Failed to change password';
    passwordUpdateFailed: 'Failed to update password';
    pleaseCheckYourInput: 'Please check your input';
    pleaseCompleteHouseholdRegistrationFirst: 'Please complete household registration first';
    pleaseSelectAtLeastOneCodeToPrint: 'Please select at least one code to print';
    pleaseTryAgainLater: 'Please try again later';
    printCardsLoadError: 'An error occurred while loading the print cards. Please try again.';
    printSessionExpired: 'Print session expired';
    printSessionExpiredDetails: 'Your print session has expired. Please select codes again from the Unique Code Management page.';
    printSessionRetrievalError: 'An error occurred while retrieving your print session. Please try again.';
    rateLimitExceeded: 'Too many requests. Please wait before trying again.';
    rateLimitFetchFailed: 'Failed to fetch rate limit settings';
    rateLimitSettingsWouldBeSavedInProduction: 'Rate limit settings would be saved in production environment';
    rateLimitUpdateFailed: 'Failed to update rate limit settings';
    registrationError: 'An error occurred during registration';
    requestTimedOut: 'Request timed out';
    requestTimedOutPleaseTryAgain: 'Request timed out. Please try again.';
    requestTimeout: 'Request timed out';
    ResponseRegenerationFailed: 'Failed to regenerate response';
    sacramentAlreadyExists: 'A sacrament of this type already exists for this member in this census year';
    sacramentCreatedSuccessfully: 'Sacrament created successfully';
    sacramentDateCannotBeInFuture: 'Sacrament date cannot be in the future';
    sacramentDeletedSuccessfully: 'Sacrament deleted successfully';
    sacramentDeleteFailed: 'Failed to delete sacrament';
    sacramentIdRequired: 'Sacrament ID is required';
    sacramentNotFound: 'Sacrament not found';
    sacramentNotFoundOrDoesNotBelong: 'Sacrament not found or does not belong to this household';
    sacramentTypeAlreadyRecorded: 'This sacrament type has already been recorded for this member';
    sacramentUpdatedSuccessfully: 'Sacrament updated successfully';
    sacramentUpdateFailed: 'Failed to update sacrament';
    searchFailed: 'Search failed';
    selectCodesToPrint: 'Please select codes to print from the Unique Code Management page';
    serverError: 'Server Error';
    serverRespondedWithStatusRespo: 'Server responded with status {status}';
    sessionUpdatedSuccessfully: 'Session updated successfully';
    sessionUpdateError: 'An error occurred during session update';
    sessionUpdateFailed: 'Session update failed';
    sessionUpdateFailedButRegistrationSuccessful: 'Session update failed, but registration was successful';
    settingsError: 'An error occurred while updating settings';
    settingsFetchFailed: 'Failed to fetch settings';
    settingsWouldBeSavedInProduction: 'Settings would be saved in production. Database connection may not be available in development.';
    show: 'Show';
    showDetails: 'Show Details';
    siteUrlFetchFailed: 'Failed to fetch site URL';
    siteUrlUpdateFailed: 'Failed to update site URL';
    someMembersCannotBeDeleted: 'Some members cannot be deleted';
    somethingWentWrong: 'Something went wrong';
    startDateMustBeBeforeEndDate: 'Start date must be before end date';
    submissionFailed: 'Failed to submit data';
    tooManyRequests: 'Too many requests. Please wait before trying again';
    tryAgain: 'Try Again';
    TwoFactorDisableFailed: 'Failed to disable 2FA';
    twoFactorNotEnabled: 'Two-factor authentication not enabled';
    twoFactorNotSetup: 'Two-factor authentication not set up';
    TwoFactorSetupFailed: 'Failed to start 2FA setup';
    TwoFactorVerificationFailed: 'Failed to verify 2FA';
    unableToLoadWelcomeModal: 'Unable to load the welcome modal. Please try refreshing the page.';
    unableToRenderComponent: 'Unable to render this component. Please try refreshing the page';
    unauthorized: 'Unauthorized';
    unexpectedError: 'An unexpected error occurred';
    UnexpectedError: 'An unexpected error occurred';
    unexpectedErrorComponent: 'An unexpected error occurred while rendering this component.';
    uniqueCodeAlreadyAssociated: 'This unique code is already associated with a household';
    uniqueCodeDeleteFailed: 'Cannot delete assigned codes';
    uniqueCodeIdRequired: 'Unique code ID is required';
    unknownError: 'Unknown error';
    unsupportedFileType: 'Unsupported file type';
    updateFailed: 'Failed to update settings';
    ValidationFailed: 'Validation failed';
    validationFailed: 'Validation failed';
    VerificationCodeRequired: 'Please enter the verification code from your authenticator app';
  };
  forms: {
    address: 'Address';
    child: 'Child';
    chooseFile: 'Choose file...';
    dateOfBirth: 'Date of Birth';
    dateOptional: 'Date (Optional)';
    email: 'Email';
    enter10DigitMobile: 'Enter 10-digit mobile number';
    enter10digitMobileNumber: 'Enter 10-digit mobile number';
    enterCommunityFeedbackPlaceholder: '• How can we better serve our community?\n• What programs would benefit families?\n• How can we strengthen our fellowship?';
    enterFirstName: 'Enter first name';
    enterFirstNamePlaceholder: 'Enter first name';
    enterHobby: 'Enter hobby';
    enterHobbyOrInterests: 'Enter hobby or interests';
    enterHobbyPlaceholder: 'Enter hobby';
    enterHouseholdCommentsPlaceholder: 'Add any additional comments about your household...';
    enterLastName: 'Enter last name';
    enterLastNamePlaceholder: 'Enter last name';
    enterMobileNumber: 'Enter mobile number';
    enterMobileNumberPlaceholder: 'Enter 10-digit mobile number';
    enterOccupationPlaceholder: 'Enter occupation';
    enterPassword: 'Enter your password';
    enterPlace: 'Enter place';
    enterPlaceEgChurchName: 'Enter place (e.g., church name)';
    enterPlaceOfSacramentPlaceholder: 'Enter place of sacrament';
    enterSuburb: 'Enter suburb';
    enterUsername: 'Enter your username';
    enterValue: 'Enter value...';
    female: 'Female';
    firstName: 'First Name';
    gender: 'Gender';
    head: 'Head';
    hobby: 'Hobby';
    hobbyOptional: 'Hobby (Optional)';
    householdHeadDateOfBirth: 'DOB';
    householdHeadFirstName: 'Household Head First Name';
    householdHeadLastName: 'Household Head Last Name';
    lastName: 'Last Name';
    male: 'Male';
    mobilePhone: 'Mobile Phone';
    noFileChosen: 'No file chosen';
    occupation: 'Occupation';
    other: 'Other';
    parent: 'Parent';
    phone: 'Phone';
    placeOptional: 'Place (Optional)';
    postcode: 'Postcode';
    pressToFocus: 'Press / to focus';
    relationship: 'Relationship';
    relationshipToHousehold: 'Relationship to Household';
    relative: 'Relative';
    sacramentType: 'Sacrament Type';
    searchForSuburb: 'Search for your suburb...';
    searchForYourSuburb: 'Search for your suburb...';
    searchSuburbPlaceholder: 'Search for your suburb...';
    selectDateOfBirthPlaceholder: 'Select date of birth';
    selectDatePlaceholder: 'Select date';
    selectGender: 'Select gender';
    selectGenderPlaceholder: 'Select gender';
    selectOption: 'Select an option...';
    selectRelationship: 'Select relationship';
    selectRelationshipPlaceholder: 'Select relationship';
    selectSacramentType: 'Select sacrament type';
    selectStatus: 'Select status';
    selectType: 'Select Type';
    selectYear: 'Select year';
    spouse: 'Spouse';
    state: 'State';
    suburb: 'Suburb';
    typeAtLeast2CharactersToSearch: 'Type at least 2 characters to search...';
    typeAtLeast2CharactersToSearchSuburbs: 'Type at least 2 characters to search suburbs';
    typeAtLeastNCharacters: 'Type at least {count} characters to search suburbs';
    typeHere: 'Type here...';
  };
  genders: {
    female: 'Female';
    male: 'Male';
    other: 'Other';
  };
  help: {
    accessControls: 'Access Controls';
    accessControlsDescription: 'Only authorised church administrators with proper credentials can access the census data. Access is restricted based on role and responsibility, and all access to the system is logged and monitored. We regularly review our security practises to ensure your information remains protected.';
    address: 'Address';
    censusOverview: 'Census Overview';
    censusOverviewDescription: 'The church census is an important process that helps us maintain accurate records of our congregation. It allows us to better understand the needs of our community and plan for future ministry activities.';
    censusProcess: 'Census Process';
    churchName: 'Church Name';
    contactInformation: 'Contact Information';
    dataProtection: 'Data Protection';
    dataProtectionDescription: 'We take the security of your personal information seriously. All data submitted through the census system is encrypted and stored securely in our database. We implement industry-standard security measures to protect against unauthorised access, alteration, disclosure, or destruction of your personal information.';
    email: 'Email';
    fallbackAddress: '123 Main Street, Sydney NSW 2000';
    fallbackChurchName: 'WSCCC Community';
    fallbackEmail: '<EMAIL>';
    fallbackPhone: '0412345678';
    faq: 'FAQ';
    faqGeneralCensusPeriodAnswer: 'The census period is determined by church administrators. When the census is open, you will be able to submit your information through the homepage. If the census is closed, a message will be displayed indicating when it will be available again.';
    faqGeneralCensusPeriodQuestion: 'When is the census period?';
    faqGeneralLostCodeAnswer: 'Please contact your parish administrator to get a new unique code. You can use the contact information provided on this page to reach out for assistance.';
    faqGeneralLostCodeQuestion: 'What if I lost my unique code?';
    faqGeneralSubmitCensusAnswer: 'You can submit your census information by using the unique code provided to you and filling out the form on the homepage. Follow the instructions and complete all required fields to submit your information.';
    faqGeneralSubmitCensusQuestion: 'How do I submit my census information?';
    faqPrivacyDataAccessAnswer: 'Only authorised church administrators with proper credentials can access your information. Access is restricted and monitored to ensure data privacy and security.';
    faqPrivacyDataAccessQuestion: 'Who can access my information?';
    faqPrivacyDataProtectionAnswer: 'All data is encrypted and stored securely. Only authorised administrators have access to the information. We follow strict data protection protocols to ensure your information remains confidential.';
    faqPrivacyDataProtectionQuestion: 'How is my data protected?';
    faqPrivacyDataUsageAnswer: 'Your information will only be used for church administrative purposes, to help with ministry planning, and to better serve the congregation. We will never sell or share your personal information with third parties.';
    faqPrivacyDataUsageQuestion: 'How will my information be used?';
    faqProcessCensusPurposeAnswer: 'The church census helps us maintain accurate records of our congregation, understand the needs of our community, and plan for future ministry activities. Your participation helps us serve you better.';
    faqProcessCensusPurposeQuestion: 'What is the purpose of the census?';
    faqProcessRequiredInfoAnswer: 'You will need to provide basic personal information such as your name, contact details, family information, and your involvement in church activities. All required fields will be clearly marked in the census form.';
    faqProcessRequiredInfoQuestion: 'What information do I need to provide?';
    faqProcessTimeToCompleteAnswer: 'The census typically takes about 10-15 minutes to complete. You can save your progress and return later if needed.';
    faqProcessTimeToCompleteQuestion: 'How long does it take to complete the census?';
    findAnswersToCommonQuestions: 'Find answers to the most common questions about the census system';
    frequentlyAskedQuestions: 'Frequently Asked Questions';
    generalQuestions: 'General Questions';
    gettingStarted: 'Getting Started';
    helpAndFaq: 'Help & FAQ';
    informationUsage: 'Information Usage';
    informationUsageDescription: 'The information collected through the census is used solely for church administrative purposes. This includes maintaining accurate congregation records, planning ministry activities, and better serving the needs of our community. Your information will never be sold or shared with third parties.';
    learnHowToUseCensusSystem: 'Learn how to use the census system effectively';
    needHelpContact: 'Need help? Contact us using the information below.';
    phone: 'Phone';
    privacyAndData: 'Privacy & Data';
    privacySecurity: 'Privacy & Security';
    privacySecurityDescription: 'Learn how we protect your information';
    privacySecurityTitle: 'Privacy & Security';
    step1: 'Receive your unique code from your church administrator';
    step2: 'Visit the census system homepage during the census period';
    step3: 'Enter your unique code in the provided field';
    step4: 'Fill out all required information in the census form';
    step5: 'Review your information for accuracy';
    step6: 'Submit your completed census form';
    stepByStepGuide: 'Step-by-Step Guide';
    stepByStepGuideDescription: 'Follow these simple steps to complete your census form:';
    tip1: 'Have all your family information ready before starting';
    tip2: 'Complete the form in one sitting if possible';
    tip3: 'Ensure all required fields are filled out';
    tip4: 'Contact support if you encounter any issues';
    tipsForCompletion: 'Tips for Completion';
  };
  info: {
    allSacramentsRecorded: 'All sacrament types have been recorded';
    sacramentLimitReached: 'Cannot add more sacraments - limit reached';
  };
  legal: {
    and: 'and';
    byClickingContinue: 'By clicking continue, you agree to our';
    privacyPolicy: 'Privacy Policy';
    termsAndConditions: 'Terms and Conditions';
    termsOfService: 'Terms of Service';
  };
  metadata: {
    accountManagementDescription: 'Manage your census account and data';
    accountManagementTitle: 'Account Management | WSCCC Census System';
    adminDashboardDescription: 'Admin dashboard for the WSCCC Census System';
    adminDashboardTitle: 'Admin Dashboard | WSCCC Census System';
    adminLoginDescription: 'Login to the WSCCC Census System admin portal';
    adminLoginTitle: 'Admin Login | WSCCC Census System';
    adminPortalDescription: 'Admin portal for the WSCCC Census System';
    adminPortalTitle: 'Admin Portal | WSCCC Census System';
    analyticsDescription: 'AI-powered analytics assistant for census data queries and insights';
    analyticsTitle: 'AI Analytics Assistant | WSCCC Census System';
    censusFormDescription: 'Complete your census form for WSCCC';
    censusFormTitle: 'Census Form | WSCCC Census System';
    description: 'Catholic Census System for WSCCC - Comprehensive household and member management';
    helpDescription: 'Help and frequently asked questions for the WSCCC Census System';
    helpTitle: 'Help & FAQ | WSCCC Census System';
    householdManagementDescription: 'Manage household records in the WSCCC Census System';
    householdManagementTitle: 'Household Management | WSCCC Census System';
    membersManagementDescription: 'Manage member records in the WSCCC Census System';
    membersManagementTitle: 'Members Management | WSCCC Census System';
    systemSettingsDescription: 'Configure settings for the WSCCC Census System';
    systemSettingsTitle: 'System Settings | WSCCC Census System';
    title: 'WSCCC Census System';
    uniqueCodeManagementDescription: 'Manage unique access codes in the WSCCC Census System';
    uniqueCodeManagementTitle: 'Unique Code Management | WSCCC Census System';
  };
  navigation: {
    account: 'Account';
    adminLogin: 'Admin Login';
    adminPortal: 'Admin Portal';
    analytics: 'Analytics';
    census: 'Census';
    chinese: '中文';
    code: 'Code';
    darkMode: 'Dark mode';
    dashboard: 'Dashboard';
    english: 'English';
    faq: 'FAQ';
    help: 'Help';
    home: 'Home';
    household: 'household';
    lightMode: 'Light mode';
    loadingHouseholdData: 'Loading household data...';
    logout: 'Logout';
    members: 'Members';
    navigation: 'Navigation';
    platform: 'Platform';
    settings: 'Settings';
    signingOut: 'Signing Out...';
    signOut: 'Sign Out';
    toggleMenu: 'Toggle menu';
    toggleMobileMenu: 'Toggle mobile menu';
    uniqueCode: 'Unique Code';
  };
  notifications: {
    accountDeleted: 'Account deleted successfully';
    backupCodesCopied: 'Backup codes copied to clipboard';
    backupCodesGenerated: 'New backup codes generated successfully';
    backupCreated: 'Database backup created successfully';
    backupFailed: 'Failed to create database backup';
    bulkDeleteMembersPartialSuccess: 'Successfully deleted {deletedCount} members. {skippedCount} members could not be deleted.';
    bulkDeleteMembersSuccess: 'Successfully deleted {count} members';
    bulkDeletePartialSuccess: 'Successfully deleted {deletedCount} households. {skippedCount} households could not be deleted.';
    bulkDeleteSuccess: 'Successfully deleted {count} households';
    censusCodeValidated: 'Census code validated successfully';
    censusControlsUpdatedSuccessfully: 'Census controls updated successfully';
    censusYearUpdatedSuccessfully: 'Census year updated successfully';
    chartExported: 'Chart exported successfully';
    chartExportedSuccessfully: 'Chart exported successfully';
    churchInformationUpdatedSuccessfully: 'Church information updated successfully';
    connectionReset: 'Database connection has been successfully reset';
    connectionTestFailed: 'Connection test failed';
    databaseImported: 'Database imported successfully';
    dataExportedSuccessfully: 'Data exported successfully';
    duplicateAlert: 'Duplicate alert detected';
    errorOccurredDuringRegistration: 'An error occurred during registration';
    failedToRegisterHousehold: 'Failed to register household';
    formCompletedSuccessfully: 'Form completed successfully';
    formSavedSuccessfully: 'Form saved successfully';
    householdDeleted: 'Household deleted successfully';
    householdDeletedSuccessfully: 'Household deleted successfully';
    householdRegisteredSuccessfully: 'Household registered successfully';
    householdUpdatedSuccessfully: 'Household updated successfully';
    loginSuccessful: 'Login successful';
    memberAddedSuccessfully: 'Member added successfully';
    memberDeleted: 'Member deleted successfully';
    memberDeletedSuccessfully: 'Member deleted successfully';
    memberUpdated: 'Member updated successfully';
    memberUpdatedSuccessfully: 'Member updated successfully';
    messageDeleted: 'Message deleted successfully';
    messagesDeleted: 'Messages deleted successfully';
    operationCompleted: 'Operation completed successfully';
    operationFailed: 'Operation failed';
    passwordChangedSuccessfully: 'Password changed successfully';
    preparedCodesForPrinting: 'Prepared {count} codes for printing';
    qrCodeGenerated: 'Scan the QR code with your authenticator app';
    rateLimitSettingsUpdatedSuccessfully: 'Rate limit settings updated successfully';
    registrationComplete: 'Registration complete';
    sacramentAddedSuccessfully: 'Sacrament added successfully';
    sacramentDeletedSuccessfully: 'Sacrament deleted successfully';
    sacramentUpdatedSuccessfully: 'Sacrament updated successfully';
    sessionUpdateFailed: 'Session update failed, but registration was successful';
    settingsUpdatedSuccessfully: 'Settings updated successfully';
    siteUrlUpdatedSuccessfully: 'Site URL updated successfully';
    successfullyDeletedUniqueCodes: 'Successfully deleted {count} unique codes';
    successfullyGeneratedUniqueCodes: 'Successfully generated {count} unique codes';
    twoFactorAuthenticationDisabled: 'Two-factor authentication disabled successfully';
    twoFactorDisabled: 'Two-factor authentication disabled successfully';
    twoFactorEnabled: 'Two-factor authentication enabled successfully';
    twoFactorSettingsUpdated: 'Two-factor authentication settings updated successfully';
    uniqueCodeCopied: 'Unique code copied to clipboard';
  };
  onboarding: {
    almostDone: 'Almost Done!';
    communityFeedbackDescription: 'Share your thoughts and suggestions to help improve our community.';
    communityFeedbackTitle: 'Community Feedback';
    complete: 'Complete! 🎉';
    continueProgress: 'Continue Progress';
    getStarted: 'Get Started';
    hobbyFieldsDescription: 'Add hobbies for the household head - these fields are completely optional.';
    hobbyFieldsTitle: 'Household Head Hobbies';
    householdMembersDescription: 'Include all members of your household in the census.';
    householdMembersTitle: 'Add Family Members';
    householdRegistrationDescription: 'Basic household information has been completed.';
    householdRegistrationTitle: 'Household Registration';
    nextStepsTitle: "What's Next?";
    occupationDescription: 'Add occupation information for the household head.';
    occupationTitle: 'Household Head Occupation';
    progressBadgeTooltip: 'Census completion: {progress}%';
    sacramentsDescription: 'Record important sacramental milestones for the household head.';
    sacramentsTitle: 'Household Head Sacraments';
    skipForNow: 'Skip for Now';
    welcomeDescription: 'Click on any incomplete step below to get started';
  };
  pagination: {
    firstPage: 'First page';
    goToPage: 'Go to page';
    items: 'items';
    itemsPerPage: 'Items per page';
    lastPage: 'Last page';
    morePages: 'More pages';
    next: 'Next';
    nextPage: 'Next page';
    of: 'of';
    page: 'Page';
    pages: 'pages';
    pagination: 'pagination';
    previous: 'Previous';
    previousPage: 'Previous page';
    results: 'results';
    showing: 'Showing';
    showingItemsInVirtualizedView: 'Showing {count} items in virtualized view for optimal performance';
    showingRowsInVirtualizedView: 'Showing {count} rows in virtualized view for optimal performance';
    to: 'to';
  };
  qrScanner: {
    browserWillAskPermission: 'Your browser will ask for camera permission';
    cameraConstraintError: 'Camera configuration not supported. Please try again with different settings.';
    cameraInUse: 'Camera is currently in use by another application. Please close other apps using the camera and try again.';
    cameraNotSupported: 'Camera access is not supported on this browser. Please use a modern browser with camera support.';
    cameraPermissionDenied: 'Camera permission denied. Please enable camera access in your browser settings and try again.';
    clickAllowToEnableScanning: "Click 'Allow' to enable QR code scanning";
    clickLockIconInAddressBar: "Click the lock icon in your browser's address bar";
    httpsRequired: 'QR scanner requires HTTPS for security. Please access this site via https:// or use manual entry.';
    noCameraFound: 'No camera found. Please ensure your device has a camera and try again.';
    permissionHelp: 'To enable camera access';
    permissionSteps: 'How it works';
    pleaseAllowCameraAccess: 'Please allow camera access to scan QR codes';
    pointCameraAtQrCode: 'Point your camera at a QR code to automatically fill in your unique code';
    qrCodeScanned: 'QR Code Scanned Successfully!';
    refreshPageAndTryAgain: 'Refresh the page and try again';
    requestingCameraAccess: 'Requesting Camera Access';
    scannerError: 'Scanner Error';
    scanQrCode: 'Scan QR Code';
    selectAllowForCamera: "Select 'Allow' for camera permissions";
    tryAgain: 'Try Again';
  };
  relationships: {
    child: 'Child';
    grandchild: 'Grandchild';
    grandparent: 'Grandparent';
    head: 'Head';
    other: 'Other';
    parent: 'Parent';
    relative: 'Relative';
    sibling: 'Sibling';
    spouse: 'Spouse';
  };
  sacraments: {
    anointingOfSick: 'Anointing of the Sick';
    baptism: 'Baptism';
    communion: 'Communion';
    confirmation: 'Confirmation';
    holyOrders: 'Holy Orders';
    marriage: 'Marriage';
    matrimony: 'Matrimony';
  };
  status: {
    approved: 'Approved';
    censusClosed: 'Census is Closed';
    censusOpen: 'Census is Open';
    complete: 'Complete';
    incomplete: 'Incomplete';
    pending: 'Pending';
    rejected: 'Rejected';
  };
  tables: {
    age: 'Age';
    assignedDate: 'Assigned Date';
    censusYear: 'Census Year';
    createdDate: 'Created Date';
    householdId: 'Household ID';
    id: 'ID';
    isAssigned: 'Is Assigned';
    memberCount: 'Member Count';
    memberId: 'Member ID';
    name: 'Name';
    noResults: 'No results found';
    openActionsMenu: 'Open actions menu';
    sacramentCount: 'Sacrament Count';
    selectAllHouseholds: 'Select all households';
    selectAllMembers: 'Select all members';
    status: 'Status';
    updatedDate: 'Updated Date';
  };
  tour: {
    addMemberDescription: 'Click here to add family members to your household and complete your census registration.';
    addSacramentButtonDescription: 'Click this button to add a new sacrament record for the household head.';
    communityFeedbackInFormDescription: 'Use this area to share your thoughts, suggestions, and feedback about our church community.';
    editHouseholdHeadDescription: 'Click here to edit household head information including hobbies and sacrament records.';
    editHouseholdHeadForHobbyDescription: 'Click here to edit household head information and add hobby details.';
    editHouseholdHeadForOccupationDescription: 'Click here to edit household head information and add occupation details.';
    editHouseholdHeadForSacramentsDescription: 'Click here to edit household head information and manage sacrament records.';
    hobbyFieldInFormDescription: "This is the hobby field where you can enter the household head's interests and activities.";
    occupationFieldInFormDescription: "This is the occupation field where you can enter the household head's job or profession.";
    progressTooltip: 'Census completion: {progress}%';
    sacramentsInFormDescription: 'This section allows you to add and manage sacrament records for the household head.';
  };
  validation: {
    addressIsRequired: 'Address is required';
    addressRequired: 'Address is required';
    announcementTextMaxLength: 'Announcement text cannot exceed 1000 characters';
    atLeast1CodeMustBeGenerated: 'At least 1 code must be generated';
    atLeastOneCodeIdMustBeProvided: 'At least one code ID must be provided';
    atLeastOneHouseholdIdRequired: 'At least one household ID is required';
    atLeastOneMemberIdRequired: 'At least one member ID is required';
    censusYearDescriptionMaxLength: 'Census year description cannot exceed 255 characters';
    censusYearDescriptionRequired: 'Census year description is required';
    censusYearIdInteger: 'Census year ID must be an integer';
    censusYearIdMustBeAnInteger: 'Census year ID must be an integer';
    censusYearIdMustBePositive: 'Census year ID must be positive';
    censusYearIdPositive: 'Census year ID must be positive';
    censusYearIdRequired: 'Census year ID is required';
    censusYearIdValidNumber: 'Must be a valid number';
    censusYearInteger: 'Census year must be an integer';
    censusYearMaximum: 'Census year cannot exceed 2100';
    censusYearMinimum: 'Census year must be at least 2000';
    checkFormErrors: 'Please check the form for errors';
    churchNameIsRequired: 'Church name is required';
    churchNameRequired: 'Church name is required';
    codeCountInteger: 'Number of codes must be an integer';
    codeCountMaximum: 'Maximum 1000 codes can be generated at once';
    codeCountMinimum: 'At least 1 code must be generated';
    codeCountRange: 'Number of codes must be between 1 and 1000';
    codeCountValidNumber: 'Must be a valid number';
    codeInvalidFormat: 'Code can only contain letters, numbers, and hyphens';
    codeIsRequired: 'Code is required';
    codeIsTooLong: 'Code is too long';
    codeMaxLength: 'Code must be at most 6 characters';
    codeMinLength: 'Code must be at least 6 characters';
    codeRequired: 'Code is required';
    codeSelectionRequired: 'At least one code must be selected';
    codeTooLong: 'Code is too long';
    confirmationPhraseRequired: 'Please enter the confirmation phrase';
    confirmPasswordMinLength: 'Confirm password must be at least 8 characters';
    contactNumberDigits: 'Contact number can only contain digits';
    contactNumberIsRequired: 'Contact number is required';
    contactNumberLength: 'Contact number must be exactly 10 digits';
    contactNumberMustBeExactly10Di: 'Contact number must be exactly 10 digits';
    contactNumberRequired: 'Contact number is required';
    currentCensusYearMustBeGreater: 'Current census year must be greater than the last census year ({lastYear})';
    currentCensusYearRequired: 'Current census year is required';
    currentPasswordRequired: 'Current password is required';
    dateOfBirthCannotBeInTheFuture: 'Date of birth cannot be in the future';
    dateOfBirthRequired: 'Date of birth is required';
    escalationIncrementCannotBeNeg: 'Escalation increment cannot be negative';
    escalationIncrementCannotExcee: 'Escalation increment cannot exceed 2 hours (120 minutes)';
    escalationIncrementInteger: 'Escalation increment must be an integer';
    escalationIncrementMaximum: 'Escalation increment cannot exceed 2 hours (120 minutes)';
    escalationIncrementMinimum: 'Escalation increment cannot be negative';
    escalationIncrementMustBeAnInt: 'Escalation increment must be an integer';
    escalationSettingsExceed24Hours: 'Escalation settings would exceed 24 hours maximum. Reduce escalation increment or initial lockout.';
    exportFormatRequired: 'Please select an export format';
    firstCensusYearMustBeNumber: 'First census year must be a number';
    firstCensusYearRequired: 'First census year is required';
    firstNameIsRequired: 'First name is required';
    firstNameRequired: 'First name is required';
    firstNameTooLong: 'First name too long';
    futureDateNotAllowed: 'Date cannot be in the future';
    genderRequired: 'Gender is required';
    hobbyDescriptionTooLong: 'Hobby description too long';
    hobbyTooLong: 'Hobby description too long';
    householdCommentTooLong: 'Household comment is too long (maximum 1000 characters)';
    householdIdRequired: 'Household ID is required';
    invalidAddress: 'Please enter a valid address';
    invalidAnnouncementType: 'Invalid announcement type';
    invalidCensusCode: 'Please enter a valid census code';
    invalidCharactersDetected: 'Invalid characters detected';
    invalidChurchName: 'Church name cannot contain digits';
    invalidConfirmationPhrase: 'Please type DELETE NOW to confirm';
    invalidContactNumber: 'Contact number must be exactly 10 digits and contain only numbers';
    invalidDateFormat: 'Invalid date format. Please use DD/MM/YYYY or select from calendar';
    invalidEmail: 'Please enter a valid email address';
    invalidEmailFormat: 'Invalid email format';
    invalidPhone: 'Please enter a valid phone number';
    invalidSearchCharacters: 'Please use only letters, numbers, spaces, and common punctuation';
    invalidUrlFormat: 'Please enter a valid URL';
    lastCensusYearMustBeNumber: 'Last census year must be a number';
    lastCensusYearRequired: 'Last census year is required';
    lastNameIsRequired: 'Last name is required';
    lastNameRequired: 'Last name is required';
    lastNameTooLong: 'Last name too long';
    lockoutDurationCannotExceed24H: 'Lockout duration cannot exceed 24 hours (1440 minutes)';
    lockoutDurationInteger: 'Lockout duration must be an integer';
    lockoutDurationMaximum: 'Lockout duration cannot exceed 24 hours (1440 minutes)';
    lockoutDurationMinimum: 'Lockout duration must be at least 1 minute';
    lockoutDurationMustBeAnInteger: 'Lockout duration must be an integer';
    lockoutDurationMustBeAtLeast1M: 'Lockout duration must be at least 1 minute';
    maxAttemptsInteger: 'Maximum attempts must be an integer';
    maxAttemptsMaximum: 'Maximum attempts cannot exceed 20';
    maxAttemptsMinimum: 'Maximum attempts must be at least 1';
    maximum1000CodesCanBeGenerated: 'Maximum 1000 codes can be generated at once';
    maximumAttemptsCannotExceed20: 'Maximum attempts cannot exceed 20';
    maximumAttemptsMustBeAnInteger: 'Maximum attempts must be an integer';
    maximumAttemptsMustBeAtLeast1: 'Maximum attempts must be at least 1';
    memberSelectionRequired: 'At least one member must be selected';
    mobilePhoneCannotExceed10Digit: 'Mobile phone cannot exceed 10 digits';
    mobilePhoneCanOnlyContainNumbe: 'Mobile phone can only contain numbers';
    mobilePhoneMustBeAtLeast10Digi: 'Mobile phone must be at least 10 digits';
    mobileRequired: 'Mobile phone is required';
    mustBeAValidNumber: 'Must be a valid number';
    newPasswordMinLength: 'New password must be at least 8 characters';
    numberOfCodesMustBeAnInteger: 'Number of codes must be an integer';
    numberOfCodesMustBeBetween1And: 'Number of codes must be between 1 and 1000';
    occupationTooLong: 'Occupation description too long';
    pageMustBeAtLeast1: 'Page must be at least 1';
    pageSizeCannotExceed100: 'Page size cannot exceed 100';
    pageSizeMustBeAtLeast1: 'Page size must be at least 1';
    passwordRequired: 'Password is required';
    passwordsDoNotMatch: 'Passwords do not match';
    phoneLength: 'Mobile phone must be 10 digits';
    phoneMaxLength: 'Mobile phone cannot exceed 10 digits';
    phoneMinLength: 'Mobile phone must be at least 10 digits';
    phoneNumbers: 'Mobile phone can only contain numbers';
    pleaseEnterAValidUrl: 'Please enter a valid URL';
    pleaseEnterTheConfirmationPhra: 'Please enter the confirmation phrase';
    pleaseTypeDeleteNowToConfirm: 'Please type DELETE NOW to confirm';
    relationshipRequired: 'Relationship is required';
    required: 'This field is required';
    sacramentDateCannotBeInTheFutu: 'Sacrament date cannot be in the future';
    sacramentDateFuture: 'Sacrament date cannot be in the future';
    sacramentTypeRequired: 'Sacrament type is required for sacrament #{number}';
    searchTermMaxLength: 'Search term cannot exceed 50 characters';
    searchTermMinLength: 'Search term must be at least 2 characters';
    selectGender: 'Please select a gender';
    selectRelationship: 'Please select a relationship';
    settingsUpdateFailed: 'Failed to update settings';
    siteUrlIsRequired: 'Site URL is required';
    siteUrlRequired: 'Site URL is required';
    suburbIsRequired: 'Suburb is required';
    suburbRequired: 'Suburb is required';
    tablesSelectionRequired: 'Please select tables to export';
    urlMustStartWithHttpOrHttps: 'URL must start with http:// or https://';
    urlMustStartWithProtocol: 'URL must start with http:// or https://';
    urlNoTrailingSlash: 'URL should not end with a trailing slash';
    urlShouldNotEndWithATrailingSl: 'URL should not end with a trailing slash';
    usernameRequired: 'Username is required';
    validationError: 'Validation error';
    verificationCodeMustBe6Digits: 'Verification code must be 6 digits';
  };
  warnings: {
    cannotDeleteHouseholdWithMembers: 'Cannot delete household with members other than the household head. Please delete other members first.';
    dataLossWarning: 'This action may result in data loss. Please confirm.';
    deleteConfirmationWarning: 'This action cannot be undone. Are you sure?';
    deleteHouseholdWarning: 'This will delete the household and mark the associated unique code as unassigned.';
    duplicateDataDetected: 'Duplicate data detected. Please review before proceeding.';
    largeDatasetDetected: 'Large dataset detected. This operation may take longer than usual.';
    limitReachedWarning: 'You have reached the limit for this operation.';
    maintenanceModeWarning: 'System is in maintenance mode. Some features may be unavailable.';
    needAdminLoginToViewSettings: 'You need to be logged in as an admin to view settings';
    needToBeLoggedInAsAdmin: 'You need to be logged in as an admin';
    permanentActionWarning: 'This is a permanent action that cannot be reversed.';
    sessionExpiredWarning: 'Your session has expired. Please log in again.';
    sessionExpiringWarning: 'Your session will expire soon. Please save your work.';
    unsavedChangesWarning: 'You have unsaved changes. Are you sure you want to leave?';
    usingDefaultValuesDatabaseUnavailable: 'Using default values - database unavailable';
    warningMessage: 'Warning: Please review this action carefully.';
  };
};
export default messages;
