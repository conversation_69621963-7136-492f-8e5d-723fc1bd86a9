/**
 * Census Years Database Operations with Prisma
 * Replaces MySQL-based implementation with Prisma ORM
 */

import type { CensusYear } from "@prisma/client";
import { prisma } from "./prisma";

export async function getCensusYears(): Promise<CensusYear[]> {
	return await prisma.censusYear.findMany({
		orderBy: { year: "desc" },
	});
}

export async function getCensusYearById(
	id: number,
): Promise<CensusYear | null> {
	return await prisma.censusYear.findUnique({
		where: { id },
	});
}

export async function getCensusYearByYear(
	year: number,
): Promise<CensusYear | null> {
	return await prisma.censusYear.findFirst({
		where: { year },
	});
}

export async function getActiveCensusYear(): Promise<CensusYear | null> {
	return await prisma.censusYear.findFirst({
		where: { isActive: true },
	});
}

export async function createCensusYear(data: {
	year: number;
	isActive: boolean;
	startDate?: Date | null;
	endDate?: Date | null;
}): Promise<CensusYear> {
	// If this is set as active, deactivate all other years first
	if (data.isActive) {
		await prisma.censusYear.updateMany({
			data: { isActive: false },
		});
	}

	return await prisma.censusYear.create({
		data: {
			year: data.year,
			isActive: data.isActive,
			startDate: data.startDate || new Date(),
			endDate: data.endDate || new Date(),
		},
	});
}

// Alternative signature for compatibility with census-years-prisma.ts
export async function createCensusYearAlt(
	censusYear: Omit<CensusYear, "id" | "createdAt" | "updatedAt">,
): Promise<CensusYear> {
	return createCensusYear({
		year: censusYear.year,
		isActive: censusYear.isActive,
		startDate: censusYear.startDate,
		endDate: censusYear.endDate,
	});
}

export async function updateCensusYear(
	id: number,
	data: Partial<{
		year: number;
		isActive: boolean;
		startDate: Date | null;
		endDate: Date | null;
	}>,
): Promise<CensusYear> {
	// If this is set as active, deactivate all other years first
	if (data.isActive) {
		await prisma.censusYear.updateMany({
			where: { id: { not: id } },
			data: { isActive: false },
		});
	}

	// Filter out null values for Prisma
	const updateData: Partial<{
		year: number;
		isActive: boolean;
		startDate: Date;
		endDate: Date;
	}> = {};
	if (data.year !== undefined) updateData.year = data.year;
	if (data.isActive !== undefined) updateData.isActive = data.isActive;
	if (data.startDate !== undefined && data.startDate !== null)
		updateData.startDate = data.startDate;
	if (data.endDate !== undefined && data.endDate !== null)
		updateData.endDate = data.endDate;

	return await prisma.censusYear.update({
		where: { id },
		data: updateData,
	});
}

export async function deleteCensusYear(id: number): Promise<void> {
	await prisma.censusYear.delete({
		where: { id },
	});
}

export async function setActiveCensusYear(id: number): Promise<void> {
	// Use transaction to ensure atomicity
	await prisma.$transaction([
		prisma.censusYear.updateMany({
			data: { isActive: false },
		}),
		prisma.censusYear.update({
			where: { id },
			data: { isActive: true },
		}),
	]);
}

export async function getCurrentCensusYear(): Promise<CensusYear | null> {
	return await prisma.censusYear.findFirst({
		where: { isActive: true },
	});
}

export async function getCensusYearStatistics(censusYearId: number): Promise<{
	totalHouseholds: number;
	totalMembers: number;
	averageHouseholdSize: number;
}> {
	// Get household count
	const totalHouseholds = await prisma.householdMember.findMany({
		where: { censusYearId },
		select: { householdId: true },
		distinct: ["householdId"],
	});

	// Get member count
	const totalMembers = await prisma.householdMember.count({
		where: { censusYearId },
	});

	const householdCount = totalHouseholds.length;
	const averageHouseholdSize =
		householdCount > 0
			? Math.round((totalMembers / householdCount) * 100) / 100
			: 0;

	return {
		totalHouseholds: householdCount,
		totalMembers,
		averageHouseholdSize,
	};
}
