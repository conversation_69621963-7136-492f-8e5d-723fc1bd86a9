/**
 * Smart Placeholder Engine for Homepage Announcements
 * Processes placeholders in announcement text with dynamic census data
 */

import { formatForDisplay, parseDate as parseUtilDate } from "@/lib/utils/date-time";

export interface CensusData {
  isOpen: boolean;
  startDate?: Date;
  endDate?: Date;
  churchName?: string;
}

export interface PlaceholderContext {
  censusData: CensusData;
  locale?: string;
}

/**
 * Available placeholders for homepage announcements
 */
export const AVAILABLE_PLACEHOLDERS = [
  "[CENSUS_DATES]",
  "[CENSUS_STATUS]",
  "[CHURCH_NAME]",
  "[START_DATE]",
  "[END_DATE]",
] as const;

/**
 * Sanitize text to prevent XSS - removes ALL HTML and dangerous content
 */
function sanitizeText(text: string): string {
  if (!text || typeof text !== "string") return "";

  // Remove ALL HTML tags and dangerous protocols
  return text
    .replace(/<[^>]*>/g, "") // Remove all HTML tags
    .replace(/javascript:/gi, "") // Remove javascript: protocol
    .replace(/data:/gi, "") // Remove data: protocol
    .replace(/vbscript:/gi, "") // Remove vbscript: protocol
    .replace(/on\w+\s*=/gi, "") // Remove event handlers
    .trim();
}

/**
 * Process all placeholders in announcement text
 */
export function processPlaceholders(text: string, context: PlaceholderContext): string {
  if (!text) return "";

  // Sanitize input text first
  let processedText = sanitizeText(text);
  const { censusData } = context;

  // Process each placeholder
  processedText = processedText.replace(/\[CHURCH_NAME\]/g, () => censusData.churchName || "");

  processedText = processedText.replace(/\[START_DATE\]/g, () =>
    censusData.startDate ? formatForDisplay(censusData.startDate) : "",
  );

  processedText = processedText.replace(/\[END_DATE\]/g, () =>
    censusData.endDate ? formatForDisplay(censusData.endDate) : "",
  );

  processedText = processedText.replace(/\[CENSUS_DATES\]/g, () =>
    generateCensusDatesText(censusData),
  );

  processedText = processedText.replace(/\[CENSUS_STATUS\]/g, () =>
    generateCensusStatusText(censusData),
  );

  return processedText.trim();
}

/**
 * Generate census dates text (e.g., "Census is open from 25 Apr 2025 to 31 Dec 2025")
 */
function generateCensusDatesText(censusData: CensusData): string {
  const { isOpen, startDate, endDate } = censusData;

  if (!(startDate && endDate)) {
    return isOpen ? "Census is currently open" : "Census is currently closed";
  }

  const start = formatForDisplay(startDate);
  const end = formatForDisplay(endDate);

  if (isOpen) {
    return `Census is open from ${start} to ${end}`;
  }
  const now = new Date();
  if (now < startDate) {
    return `Census opens ${start} and closes ${end}`;
  }
  return `Census was open from ${start} to ${end}`;
}

/**
 * Generate smart census status text
 */
function generateCensusStatusText(censusData: CensusData): string {
  const { isOpen, startDate, endDate } = censusData;
  const now = new Date();

  if (isOpen) {
    if (endDate && now < endDate) {
      return `Census is currently open until ${formatForDisplay(endDate)}`;
    }
    return "Census is currently open";
  }
  if (startDate && now < startDate) {
    return `Census opens on ${formatForDisplay(startDate)}`;
  }
  return "Census is currently closed";
}

/**
 * Safely parse date string with validation
 */
// Use centralized date utilities
export const parseDate = parseUtilDate;

/**
 * Preview placeholder processing (for admin interface)
 */
export function previewPlaceholders(
  text: string,
  context: PlaceholderContext,
): { processed: string } {
  const processed = processPlaceholders(text, context);
  return { processed };
}
