/**
 * Warning message translation mappings
 * Maps warning codes to translation keys in the 'warnings' namespace
 * Used by useMessage hook and server-side translation routes
 */

export const warningMessageKeys: Record<string, string> = {
  // Session warnings
  sessionExpiring: "sessionExpiringWarning",
  sessionExpired: "sessionExpiredWarning",

  // Data warnings
  unsavedChanges: "unsavedChangesWarning",
  largeDataset: "largeDatasetDetected",
  duplicateData: "duplicateDataDetected",

  // Operation warnings
  deleteConfirmation: "deleteConfirmationWarning",
  permanentAction: "permanentActionWarning",
  dataLoss: "dataLossWarning",

  // Bulk operation warnings
  bulkDeletePartialSuccess: "bulkDeletePartialSuccess",
  bulkDeleteMembersPartialSuccess: "bulkDeleteMembersPartialSuccess",

  // System warnings
  maintenanceMode: "maintenanceModeWarning",
  limitReached: "limitReachedWarning",

  // Admin access warnings
  usingDefaultValuesDatabaseUnavailable: "usingDefaultValuesDatabaseUnavailable",
  needAdminLoginToViewSettings: "needAdminLoginToViewSettings",
  needToBeLoggedInAsAdmin: "needToBeLoggedInAsAdmin",

  // Default fallback
  default: "warningMessage",
};
