// Chart utilities and data processing functions

import type { ChartData } from "../../../../lib/utils/chart-data-formatter";
import { getChartColor, getColorByString } from "./constants";

// Generic data point interface
export interface BaseDataPoint {
	[key: string]: unknown;
}

// Common data processing function
export function processChartData<T extends BaseDataPoint>(
	data: unknown[],
	transformer?: (item: T, index: number) => T,
): T[] {
	return data.map((item, index) => {
		const typedItem = item as T;
		return transformer ? transformer(typedItem, index) : typedItem;
	});
}

// Add color to data points
export function addColorsToData<T extends BaseDataPoint>(
	data: T[],
	colorKey?: string,
): (T & { fill: string })[] {
	return data.map((item, index) => ({
		...item,
		fill:
			colorKey && item[colorKey]
				? getColorByString(String(item[colorKey]))
				: getChartColor(index),
	}));
}

// Process data for standard charts (bar, pie, line)
export interface StandardDataPoint {
	name: string;
	value: number;
	[key: string]: unknown;
}

export function processStandardChartData(data: unknown[]): StandardDataPoint[] {
	return processChartData<StandardDataPoint>(data, (item) => ({
		...item,
		name: String(item.name || "Unknown"),
		value: Number(item.value || 0),
	}));
}

// Process data for scatter plots
export interface ScatterDataPoint {
	x: number;
	y: number;
	size?: number;
	category?: string;
	name?: string;
	[key: string]: unknown;
}

export function processScatterData(
	data: unknown[],
): (ScatterDataPoint & { fill: string })[] {
	const processedData = processChartData<ScatterDataPoint>(data, (item) => ({
		...item,
		x: Number(item.x ?? 0),
		y: Number(item.y ?? 0),
		size:
			item.size !== undefined && item.size !== null
				? Number(item.size)
				: undefined,
		category:
			item.category !== undefined &&
			item.category !== null &&
			item.category !== ""
				? String(item.category)
				: undefined,
		name:
			item.name !== undefined && item.name !== null && item.name !== ""
				? String(item.name)
				: undefined,
	}));

	return addColorsToData(processedData, "category");
}

// Process data for heatmaps
export interface HeatmapDataPoint {
	x: string | number;
	y: string | number;
	value: number;
	[key: string]: unknown;
}

export function processHeatmapData(data: unknown[]): HeatmapDataPoint[] {
	return processChartData<HeatmapDataPoint>(data, (item) => ({
		...item,
		x: item.x !== undefined && item.x !== null ? item.x : 0,
		y: item.y !== undefined && item.y !== null ? item.y : 0,
		value: Number(item.value ?? 0),
	}));
}

// Process data for radar charts
export interface RadarDataPoint {
	name: string;
	[key: string]: unknown;
}

export function processRadarData(
	data: unknown[],
	metrics: string[],
): unknown[] {
	const processedData = processChartData<RadarDataPoint>(data);

	// Transform data for radar chart format
	return metrics.map((metric) => {
		const point: Record<string, unknown> = { metric };
		processedData.forEach((item) => {
			point[item.name] = item[metric];
		});
		return point;
	});
}

// Process data for treemaps
export interface TreemapDataPoint {
	name: string;
	value: number;
	category?: string;
	[key: string]: unknown;
}

export function processTreemapData(
	data: unknown[],
): (TreemapDataPoint & { fill: string; percentage: string })[] {
	const processedData = processChartData<TreemapDataPoint>(data, (item) => ({
		...item,
		name: String(item.name || "Unknown"),
		value: Number(item.value || 0),
		category: item.category ? String(item.category) : undefined,
	}));

	const total = processedData.reduce((sum, item) => sum + item.value, 0);

	return processedData.map((item, index) => ({
		...item,
		fill: item.category
			? getColorByString(item.category)
			: getChartColor(index),
		percentage: total > 0 ? ((item.value / total) * 100).toFixed(1) : "0",
	}));
}

// Process data for waffle charts
export interface WaffleDataPoint {
	name: string;
	value: number;
	[key: string]: unknown;
}

export function processWaffleData(
	data: unknown[],
): (WaffleDataPoint & { squares: number; color: string })[] {
	const processedData = processChartData<WaffleDataPoint>(data, (item) => ({
		...item,
		name: String(item.name || "Unknown"),
		value: Number(item.value || 0),
	}));

	const total = processedData.reduce((sum, item) => sum + item.value, 0);

	return processedData.map((item, index) => ({
		...item,
		squares: Math.round((item.value / total) * 100),
		color: getChartColor(index),
	}));
}

// Utility to calculate chart dimensions based on data
export function calculateOptimalDimensions(
	dataLength: number,
	chartType: string,
	containerWidth?: number,
): { width: number; height: number } {
	const baseWidth = containerWidth || 600;
	const baseHeight = 400;

	switch (chartType) {
		case "bar":
			// Wider for many bars
			return {
				width: Math.max(baseWidth, dataLength * 60),
				height: baseHeight,
			};

		case "pie":
		case "waffle":
			// Square aspect ratio
			return {
				width: Math.min(baseWidth, baseHeight),
				height: Math.min(baseWidth, baseHeight),
			};

		case "heatmap": {
			// Square cells
			const cellSize = 30;
			const uniqueX = Math.sqrt(dataLength);
			const uniqueY = Math.sqrt(dataLength);
			return {
				width: Math.max(baseWidth, uniqueX * cellSize),
				height: Math.max(300, uniqueY * cellSize),
			};
		}

		default:
			return { width: baseWidth, height: baseHeight };
	}
}

// Utility to format numbers for display
export function formatChartValue(
	value: number | string,
	type: "number" | "percentage" | "currency" = "number",
): string {
	const numValue = typeof value === "string" ? Number.parseFloat(value) : value;

	if (Number.isNaN(numValue)) {
		return String(value);
	}

	switch (type) {
		case "percentage":
			return `${numValue.toFixed(1)}%`;
		case "currency":
			return new Intl.NumberFormat("en-AU", {
				style: "currency",
				currency: "AUD",
			}).format(numValue);
		default:
			return numValue.toLocaleString();
	}
}

// Utility to generate chart descriptions for accessibility
export function generateChartDescription(data: ChartData): string {
	const { type, data: chartData, title } = data;
	const itemCount = chartData.length;

	let description = `${type.charAt(0).toUpperCase() + type.slice(1)} chart`;

	if (title) {
		description += ` titled "${title}"`;
	}

	description += ` with ${itemCount} data point${itemCount !== 1 ? "s" : ""}`;

	// Add specific insights based on chart type
	if (type === "pie" || type === "waffle") {
		const total = chartData.reduce(
			(sum, item) => sum + (Number(item.value) || 0),
			0,
		);
		if (total > 0) {
			const largest = chartData.reduce((max, item) =>
				(Number(item.value) || 0) > (Number(max.value) || 0) ? item : max,
			);
			const percentage = (((Number(largest.value) || 0) / total) * 100).toFixed(
				1,
			);
			description += `. Largest segment is ${largest.name} at ${percentage}%`;
		}
	}

	return description;
}

// Utility to calculate heatmap colours based on value intensity
export function getHeatmapColor(
	value: number | undefined,
	minValue: number,
	maxValue: number,
): string {
	if (value === undefined) {
		return "hsl(var(--muted))";
	}

	const valueRange = maxValue - minValue;
	const intensity = valueRange > 0 ? (value - minValue) / valueRange : 0;

	// Use a blue to orange gradient with CSS variables
	if (intensity < 0.5) {
		// Blue to light blue
		const blueIntensity = intensity * 2;
		return `hsl(var(--chart-2) / ${0.2 + blueIntensity * 0.6})`;
	}
	// Light blue to orange
	const orangeIntensity = (intensity - 0.5) * 2;
	return `hsl(var(--chart-1) / ${0.4 + orangeIntensity * 0.6})`;
}
