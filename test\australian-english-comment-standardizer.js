#!/usr/bin/env node

/**
 * WSCCC Census System - Australian English Comment Standardizer
 *
 * This tool scans all TypeScript/JavaScript files for code comments and identifies
 * American English spellings that should be converted to Australian English.
 *
 * Features:
 * - Scans single-line (//) and multi-line (/* */) comments
 * - Identifies American spellings and suggests Australian alternatives
 * - Respects technical terms, API names, and code-specific terminology
 * - Provides detailed reports with before/after examples
 * - Excludes build directories and generated files
 *
 * Usage: node test/australian-english-comment-standardizer.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Get project root directory
function getProjectRoot() {
  let currentDir = __dirname;
  while (currentDir !== path.dirname(currentDir)) {
    if (fs.existsSync(path.join(currentDir, 'package.json'))) {
      return currentDir;
    }
    currentDir = path.dirname(currentDir);
  }
  return process.cwd();
}

const PROJECT_ROOT = getProjectRoot();

// Configuration
const CONFIG = {
  // File extensions to scan
  fileExtensions: ['.ts', '.tsx', '.js', '.jsx'],

  // Directories to scan
  scanDirectories: [
    path.join(PROJECT_ROOT, 'src'),
    path.join(PROJECT_ROOT, 'app'),
    path.join(PROJECT_ROOT, 'components'),
    path.join(PROJECT_ROOT, 'lib'),
    path.join(PROJECT_ROOT, 'hooks'),
    path.join(PROJECT_ROOT, 'test'),
    path.join(PROJECT_ROOT, 'scripts'),
  ],

  // Directories and files to exclude
  excludePatterns: [
    'node_modules',
    '.next',
    '.git',
    'dist',
    'build',
    'out',
    'coverage',
    'public',
    '*.generated.*',
    'lang/en.d.json.ts', // Auto-generated file
    '*.backup',
    '*.log',
    'new_server',
  ],
};

// Australian English spelling conversions
const AUSTRALIAN_SPELLINGS = {
  // -or to -our
  color: 'colour',
  colors: 'colours',
  colored: 'coloured',
  coloring: 'colouring',
  colorful: 'colourful',
  colorless: 'colourless',
  behavior: 'behaviour',
  behaviors: 'behaviours',
  behavioral: 'behavioural',
  honor: 'honour',
  honors: 'honours',
  honored: 'honoured',
  honoring: 'honouring',
  honorable: 'honourable',
  favor: 'favour',
  favors: 'favours',
  favored: 'favoured',
  favoring: 'favouring',
  favorable: 'favourable',
  flavor: 'flavour',
  flavors: 'flavours',
  flavored: 'flavoured',
  flavoring: 'flavouring',
  humor: 'humour',
  humorous: 'humorous', // No change
  labor: 'labour',
  labored: 'laboured',
  laboring: 'labouring',
  neighbor: 'neighbour',
  neighbors: 'neighbours',
  neighboring: 'neighbouring',
  rumor: 'rumour',
  rumors: 'rumours',
  rumored: 'rumoured',
  vapor: 'vapour',
  vapors: 'vapours',

  // -er to -re
  center: 'centre',
  centers: 'centres',
  centered: 'centred',
  centering: 'centring',
  theater: 'theatre',
  theaters: 'theatres',
  meter: 'metre',
  meters: 'metres',
  fiber: 'fibre',
  fibers: 'fibres',
  liter: 'litre',
  liters: 'litres',
  caliber: 'calibre',
  saber: 'sabre',
  somber: 'sombre',

  // -ize to -ise
  realize: 'realise',
  realizes: 'realises',
  realized: 'realised',
  realizing: 'realising',
  realization: 'realisation',
  organize: 'organise',
  organizes: 'organises',
  organized: 'organised',
  organizing: 'organising',
  organization: 'organisation',
  organizations: 'organisations',
  organizational: 'organisational',
  recognize: 'recognise',
  recognizes: 'recognises',
  recognized: 'recognised',
  recognizing: 'recognising',
  recognition: 'recognition', // No change
  analyze: 'analyse',
  analyzes: 'analyses',
  analyzed: 'analysed',
  analyzing: 'analysing',
  analysis: 'analysis', // No change
  customize: 'customise',
  customizes: 'customises',
  customized: 'customised',
  customizing: 'customising',
  customization: 'customisation',
  optimize: 'optimise',
  optimizes: 'optimises',
  optimized: 'optimised',
  optimizing: 'optimising',
  optimization: 'optimisation',
  minimize: 'minimise',
  minimizes: 'minimises',
  minimized: 'minimised',
  minimizing: 'minimising',
  maximize: 'maximise',
  maximizes: 'maximises',
  maximized: 'maximised',
  maximizing: 'maximising',
  utilize: 'utilise',
  utilizes: 'utilises',
  utilized: 'utilised',
  utilizing: 'utilising',
  utilization: 'utilisation',
  synchronize: 'synchronise',
  synchronizes: 'synchronises',
  synchronized: 'synchronised',
  synchronizing: 'synchronising',
  synchronization: 'synchronisation',
  initialize: 'initialise',
  initializes: 'initialises',
  initialized: 'initialised',
  initializing: 'initialising',
  initialization: 'initialisation',
  finalize: 'finalise',
  finalizes: 'finalises',
  finalized: 'finalised',
  finalizing: 'finalising',
  finalization: 'finalisation',
  visualize: 'visualise',
  visualizes: 'visualises',
  visualized: 'visualised',
  visualizing: 'visualising',
  visualization: 'visualisation',
  categorize: 'categorise',
  categorizes: 'categorises',
  categorized: 'categorised',
  categorizing: 'categorising',
  categorization: 'categorisation',
  prioritize: 'prioritise',
  prioritizes: 'prioritises',
  prioritized: 'prioritised',
  prioritizing: 'prioritising',
  prioritization: 'prioritisation',
  standardize: 'standardise',
  standardizes: 'standardises',
  standardized: 'standardised',
  standardizing: 'standardising',
  standardization: 'standardisation',
  localize: 'localise',
  localizes: 'localises',
  localized: 'localised',
  localizing: 'localising',
  localization: 'localisation',
  globalize: 'globalise',
  globalizes: 'globalises',
  globalized: 'globalised',
  globalizing: 'globalising',
  globalization: 'globalisation',
  modernize: 'modernise',
  modernizes: 'modernises',
  modernized: 'modernised',
  modernizing: 'modernising',
  modernization: 'modernisation',
  capitalize: 'capitalise',
  capitalizes: 'capitalises',
  capitalized: 'capitalised',
  capitalizing: 'capitalising',
  capitalization: 'capitalisation',

  // Other common differences
  defense: 'defence',
  defenses: 'defences',
  defensive: 'defensive', // No change
  offense: 'offence',
  offenses: 'offences',
  offensive: 'offensive', // No change
  license: 'licence', // noun form
  practice: 'practise', // verb form (noun stays 'practice')
  advice: 'advice', // No change (noun)
  advise: 'advise', // No change (verb)
  device: 'device', // No change (noun)
  devise: 'devise', // No change (verb)
  gray: 'grey',
  grays: 'greys',
  grayish: 'greyish',
  aging: 'ageing',
  catalog: 'catalogue',
  catalogs: 'catalogues',
  dialog: 'dialogue',
  dialogs: 'dialogues',
  program: 'programme', // Context-dependent
  programs: 'programmes', // Context-dependent
  check: 'check', // No change in most contexts
  cheque: 'cheque', // Financial context
  tire: 'tyre', // Vehicle context
  tires: 'tyres', // Vehicle context
  pajamas: 'pyjamas',
  skeptical: 'sceptical',
  skepticism: 'scepticism',
  artifact: 'artefact',
  artifacts: 'artefacts',
  maneuver: 'manoeuvre',
  maneuvers: 'manoeuvres',
  maneuvered: 'manoeuvred',
  maneuvering: 'manoeuvring',
};

// Technical terms that should NOT be changed (API names, libraries, etc.)
const TECHNICAL_EXCLUSIONS = [
  // Programming terms that should stay American
  'color', // CSS property
  'backgroundColor', // CSS property
  'textColor', // CSS property
  'borderColor', // CSS property
  'colorScheme', // CSS property
  'colorSpace', // CSS property
  'colorProfile', // CSS property
  'colorMode', // CSS property
  'colorPicker', // Component name
  'colorPalette', // Component name
  'colorWheel', // Component name
  'colorValue', // Variable name
  'colorCode', // Variable name
  'colorHex', // Variable name
  'colorRgb', // Variable name
  'colorHsl', // Variable name

  // Library/framework specific terms
  'center', // CSS value
  'textAlign', // CSS property
  'alignItems', // CSS property
  'justifyContent', // CSS property
  'flexDirection', // CSS property
  'centerX', // Coordinate
  'centerY', // Coordinate
  'centerPoint', // Coordinate

  // API and method names
  'analyze', // Method name in libraries
  'analyzer', // Class name
  'customizer', // Class name
  'optimizer', // Class name
  'minimizer', // Class name
  'maximizer', // Class name
  'synchronizer', // Class name
  'initializer', // Class name
  'finalizer', // Class name
  'visualizer', // Class name
  'categorizer', // Class name
  'prioritizer', // Class name
  'standardizer', // Class name
  'localizer', // Class name
  'globalizer', // Class name
  'modernizer', // Class name
  'capitalizer', // Class name

  // Brand names and proper nouns
  'Color', // Brand name
  'Center', // Place name
  'Theater', // Place name
  'Organization', // Proper noun
  'Behavior', // Proper noun
  'Honor', // Proper noun
  'Favor', // Proper noun
  'Labor', // Proper noun
  'Neighbor', // Proper noun
  'Rumor', // Proper noun
  'Vapor', // Proper noun
  'Fiber', // Proper noun
  'Meter', // Proper noun
  'Liter', // Proper noun
  'Caliber', // Proper noun
  'Saber', // Proper noun
  'Defense', // Proper noun
  'Offense', // Proper noun
  'License', // Proper noun
  'Practice', // Proper noun
  'Program', // Proper noun
  'Dialog', // Proper noun
  'Catalog', // Proper noun
  'Artifact', // Proper noun
  'Maneuver', // Proper noun
];

// Statistics tracking
const stats = {
  filesScanned: 0,
  totalComments: 0,
  commentsWithAmericanSpellings: 0,
  totalReplacements: 0,
  filesByType: {},
  spellingsByType: {},
};

// Results storage
const results = [];

console.log(
  '🇦🇺 WSCCC Census System - Australian English Comment Standardizer\n'
);
console.log('📋 Scanning for American English spellings in code comments...\n');
