import { ChevronDown, ChevronUp } from "lucide-react";
import type React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface CollapsibleHeaderProps {
	title: string;
	isCollapsed: boolean;
	onToggle: () => void;
	icon?: React.ReactNode;
	badge?: React.ReactNode;
	actions?: React.ReactNode;
	className?: string;
	variant?: "default" | "chart" | "table";
}

/**
 * Shared collapsible header component
 * Eliminates duplication between chart and table headers
 */
export function CollapsibleHeader({
	title,
	isCollapsed,
	onToggle,
	icon,
	badge,
	actions,
	className,
	variant = "default",
}: CollapsibleHeaderProps) {
	const variantStyles = {
		default:
			"bg-slate-50 dark:bg-slate-800 border-slate-200 dark:border-slate-700",
		chart:
			"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-slate-800 dark:to-slate-700 border-blue-200 dark:border-slate-600",
		table:
			"bg-gradient-to-r from-green-50 to-emerald-50 dark:from-slate-800 dark:to-slate-700 border-green-200 dark:border-slate-600",
	};

	return (
		<div
			className={cn(
				"flex items-center justify-between rounded-t-lg border p-3 transition-colors",
				variantStyles[variant],
				className,
			)}
		>
			{/* Left side - Title and metadata */}
			<div className="flex min-w-0 flex-1 items-center gap-3">
				<Button
					aria-label={isCollapsed ? "Expand" : "Collapse"}
					className="h-8 w-8 p-0 hover:bg-white/50 dark:hover:bg-slate-700/50"
					onClick={onToggle}
					size="sm"
					variant="ghost"
				>
					{isCollapsed ? (
						<ChevronDown className="h-4 w-4" />
					) : (
						<ChevronUp className="h-4 w-4" />
					)}
				</Button>

				{icon && (
					<div className="flex-shrink-0 text-slate-600 dark:text-slate-400">
						{icon}
					</div>
				)}

				<div className="flex min-w-0 flex-1 items-center gap-2">
					<h3 className="truncate font-medium text-slate-900 dark:text-slate-100">
						{title}
					</h3>
					{badge && <div className="flex-shrink-0">{badge}</div>}
				</div>
			</div>

			{/* Right side - Actions */}
			{actions && (
				<div className="ml-3 flex flex-shrink-0 items-center gap-2">
					{actions}
				</div>
			)}
		</div>
	);
}
