"use client";

import type React from "react";
import { useCallback, useEffect, useState } from "react";
import { createPortal } from "react-dom";

interface ElementBounds {
  top: number;
  left: number;
  width: number;
  height: number;
}

interface TourOverlayProps {
  targetElement: HTMLElement;
  onClose: () => void;
  children?: React.ReactNode;
}

/**
 * Elegant Tour Overlay - Clean dark background with highlighted element
 */
export function TourOverlay({ targetElement, onClose, children }: TourOverlayProps) {
  const [elementBounds, setElementBounds] = useState<ElementBounds | null>(null);
  const [mounted, setMounted] = useState(false);

  // Calculate element bounds for highlighting
  const updateElementBounds = useCallback(() => {
    if (!targetElement) return;

    const rect = targetElement.getBoundingClientRect();
    const padding = 8;

    setElementBounds({
      top: Math.max(0, rect.top - padding),
      left: Math.max(0, rect.left - padding),
      width: rect.width + padding * 2,
      height: rect.height + padding * 2,
    });
  }, [targetElement]);

  // Update on mount and viewport changes
  useEffect(() => {
    setMounted(true);
    updateElementBounds();

    // Debounce function for performance
    let timeoutId: NodeJS.Timeout;
    const debouncedUpdate = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(updateElementBounds, 16); // ~60fps
    };

    window.addEventListener("resize", debouncedUpdate);
    window.addEventListener("scroll", debouncedUpdate, { passive: true });

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener("resize", debouncedUpdate);
      window.removeEventListener("scroll", debouncedUpdate);
    };
  }, [updateElementBounds]);

  // Handle escape key and prevent body scroll
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") onClose();
    };

    const originalOverflow = document.body.style.overflow;
    document.addEventListener("keydown", handleKeyDown);
    document.body.style.overflow = "hidden";

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
      document.body.style.overflow = originalOverflow;
    };
  }, [onClose]);

  if (!mounted) return null;

  return createPortal(
    <div className="pointer-events-none fixed inset-0 z-[9999]">
      {/* Dark overlay with CSS clip-path cutout */}
      {elementBounds && (
        <div
          className="pointer-events-auto absolute cursor-default bg-black/60 transition-all duration-300"
          onClick={onClose}
          style={{
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            clipPath: `polygon(
              0% 0%,
              0% 100%,
              ${elementBounds.left}px 100%,
              ${elementBounds.left}px ${elementBounds.top}px,
              ${elementBounds.left + elementBounds.width}px ${elementBounds.top}px,
              ${elementBounds.left + elementBounds.width}px ${elementBounds.top + elementBounds.height}px,
              ${elementBounds.left}px ${elementBounds.top + elementBounds.height}px,
              ${elementBounds.left}px 100%,
              100% 100%,
              100% 0%
            )`,
          }}
        />
      )}

      {/* Highlighted element border - pointer-events-none so clicks pass through */}
      {elementBounds && (
        <div
          className="pointer-events-none absolute rounded-lg border-2 border-white/40 transition-all duration-300"
          style={{
            top: elementBounds.top - 2,
            left: elementBounds.left - 2,
            width: elementBounds.width + 4,
            height: elementBounds.height + 4,
            boxShadow: "0 0 20px rgba(255, 255, 255, 0.3)",
          }}
        />
      )}

      {/* Popover - has pointer-events-auto */}
      <div className="pointer-events-auto">{children}</div>
    </div>,
    document.body,
  );
}
