"use client";

import { RefreshCw, Trash2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useCallback, useEffect, useState } from "react";
import { SettingsCard } from "@/components/admin/settings/SettingsCard";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useMessage } from "@/hooks/useMessage";

interface DatabaseServiceInfo {
	sessions: {
		total: number;
		expired: number;
	};
	auditLogs: {
		total: number;
		old: number;
	};
}

export function DatabaseServiceSettings() {
	const t = useTranslations("admin");
	const tCommon = useTranslations("common");
	const tErrors = useTranslations("errors");
	const { showError, showSuccess, showDirect } = useMessage();
	const [isLoading, setIsLoading] = useState(true);
	const [isRefreshing, setIsRefreshing] = useState(false);
	const [isPerformingAction, setIsPerformingAction] = useState(false);
	const [serviceInfo, setServiceInfo] = useState<DatabaseServiceInfo | null>(
		null,
	);
	const [confirmDialog, setConfirmDialog] = useState<{
		isOpen: boolean;
		title: string;
		description: string;
		action: () => Promise<void>;
	}>({
		isOpen: false,
		title: "",
		description: "",
		action: async () => {},
	});

	// Fetch database service information
	const fetchDatabaseServiceInfo = useCallback(async () => {
		try {
			setIsRefreshing(true);
			const response = await fetch("/api/database/service");

			if (!response.ok) {
				throw new Error(tErrors("failedToFetchDatabaseInfo"));
			}

			const data = await response.json();
			setServiceInfo(data);
		} catch (error) {
			console.error("Error fetching database service information:", error);
			showError("failedToFetchDatabaseInfo");
		} finally {
			setIsRefreshing(false);
			setIsLoading(false);
		}
	}, [tErrors, showError]); // showError is stable due to useCallback memoization

	// Initial data fetch
	useEffect(() => {
		fetchDatabaseServiceInfo();
	}, [fetchDatabaseServiceInfo]);

	// Perform a database service action
	const performServiceAction = async (action: string) => {
		try {
			setIsPerformingAction(true);
			const response = await fetch("/api/database/service", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ action }),
			});

			if (!response.ok) {
				throw new Error(t("failedToPerformAction", { action }));
			}

			const result = await response.json();
			showDirect(
				"success",
				result.message || t("actionCompletedSuccessfully", { action }),
			);

			// Refresh data after action
			await fetchDatabaseServiceInfo();
		} catch (error) {
			console.error(`Error performing ${action}:`, error);
			showDirect("error", t("failedToPerformAction", { action }));
		} finally {
			setIsPerformingAction(false);
		}
	};

	// Open confirmation dialogue
	const openConfirmDialog = (
		title: string,
		description: string,
		action: () => Promise<void>,
	) => {
		setConfirmDialog({
			isOpen: true,
			title,
			description,
			action,
		});
	};

	// Close confirmation dialogue
	const closeConfirmDialog = () => {
		setConfirmDialog({
			isOpen: false,
			title: "",
			description: "",
			action: async () => {},
		});
	};

	// Render loading state
	if (isLoading) {
		return (
			<SettingsCard
				description={t("maintainYourDatabase")}
				title={t("databaseMaintenance")}
			>
				<div className="space-y-4">
					<Skeleton className="h-[200px] w-full" />
				</div>
			</SettingsCard>
		);
	}

	return (
		<SettingsCard
			description={t("maintainYourDatabase")}
			footerContent={
				<div className="flex items-center justify-between">
					<div className="text-muted-foreground text-xs">
						{t("lastUpdated")}: {new Date().toLocaleTimeString()}
					</div>
					<Button
						className="cursor-pointer"
						disabled={isRefreshing}
						onClick={fetchDatabaseServiceInfo}
						size="sm"
						variant="outline"
					>
						<RefreshCw
							className={`mr-2 h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`}
						/>
						{tCommon("refresh")}
					</Button>
				</div>
			}
			title={t("databaseMaintenance")}
		>
			<div className="space-y-4">
				<h3 className="font-medium text-sm">{t("databaseMaintenance")}</h3>

				<div className="grid grid-cols-2 gap-4">
					<div>
						<h4 className="mb-1 text-muted-foreground text-xs">
							{t("totalSessions")}
						</h4>
						<p className="font-medium text-sm">{serviceInfo?.sessions.total}</p>
					</div>
					<div>
						<h4 className="mb-1 text-muted-foreground text-xs">
							{t("expiredSessions")}
						</h4>
						<p className="font-medium text-sm">
							{serviceInfo?.sessions.expired}
						</p>
					</div>
					<div>
						<h4 className="mb-1 text-muted-foreground text-xs">
							{t("totalAuditLogs")}
						</h4>
						<p className="font-medium text-sm">
							{serviceInfo?.auditLogs.total}
						</p>
					</div>
					<div>
						<h4 className="mb-1 text-muted-foreground text-xs">
							{t("logsOlderThan7Days")}
						</h4>
						<p className="font-medium text-sm">{serviceInfo?.auditLogs.old}</p>
					</div>
				</div>

				<div className="rounded-md bg-muted p-3 text-xs">
					<p>{t("regularMaintenanceCanImprove")}</p>
				</div>

				<div className="flex space-x-2">
					<Button
						className="cursor-pointer"
						disabled={
							isPerformingAction || (serviceInfo?.sessions.expired || 0) === 0
						}
						onClick={() =>
							openConfirmDialog(
								t("cleanExpiredSessions"),
								t("cleanExpiredSessionsWarning"),
								async () => {
									await performServiceAction("clean-sessions");
								},
							)
						}
						size="sm"
						variant="outline"
					>
						<Trash2 className="mr-2 h-4 w-4" />
						{t("cleanExpiredSessions")}
					</Button>
					<Button
						className="cursor-pointer"
						disabled={
							isPerformingAction || (serviceInfo?.auditLogs.old || 0) === 0
						}
						onClick={() =>
							openConfirmDialog(
								t("cleanOldAuditLogs"),
								t("cleanOldAuditLogsWarning"),
								async () => {
									await performServiceAction("clean-audit-logs");
								},
							)
						}
						size="sm"
						variant="outline"
					>
						<Trash2 className="mr-2 h-4 w-4" />
						{t("cleanOldAuditLogs")}
					</Button>
				</div>
			</div>

			{/* Confirmation Dialog */}
			<AlertDialog
				onOpenChange={closeConfirmDialog}
				open={confirmDialog.isOpen}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>{confirmDialog.title}</AlertDialogTitle>
						<AlertDialogDescription>
							{confirmDialog.description}
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>{tCommon("cancel")}</AlertDialogCancel>
						<AlertDialogAction
							onClick={async (e) => {
								e.preventDefault();
								closeConfirmDialog();
								await confirmDialog.action();
							}}
						>
							{tCommon("continue")}
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</SettingsCard>
	);
}
