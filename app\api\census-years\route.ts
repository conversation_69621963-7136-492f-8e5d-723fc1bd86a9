import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { authOptions } from "@/lib/auth/auth-options";
import { getCensusYears } from "@/lib/db/census-years";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";

export async function GET() {
	// Get locale from centralized utility at the beginning
	const locale = await getLocaleFromCookies();
	const t = await getTranslations({ locale, namespace: "errors" });

	try {
		// Check authentication
		const session = await getServerSession(authOptions);

		// Require admin authentication
		if (!session || session.user.role !== "admin") {
			return NextResponse.json({ error: t("unauthorized") }, { status: 401 });
		}

		// Get all census years
		const censusYears = await getCensusYears();

		return NextResponse.json(censusYears);
	} catch (_error) {
		return NextResponse.json(
			{ error: t("failedToFetchCensusYears") },
			{ status: 500 },
		);
	}
}
