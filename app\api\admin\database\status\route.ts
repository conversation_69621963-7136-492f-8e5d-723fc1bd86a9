import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { authOptions } from "@/lib/auth/auth-options";
import { prisma } from "@/lib/db/prisma";
import { getErrorMessage } from "@/lib/utils/error-handling";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";

// Environment-aware logging
const isDevelopment = process.env.NODE_ENV === "development";

function logError(_message: string, _error?: any) {
	if (isDevelopment) {
	}
}

/**
 * GET /api/admin/database/status
 *
 * Get the current database status
 * Only accessible to admin users
 */
export async function GET(_request: Request) {
	// Get locale from centralized utility at the beginning
	const locale = await getLocaleFromCookies();
	const t = await getTranslations({ locale, namespace: "errors" });

	// Check authentication
	const session = await getServerSession(authOptions);

	// Require admin authentication
	if (!session || session.user.role !== "admin") {
		return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
	}

	try {
		// Check Prisma connection status
		let databaseStatus = "unknown";
		let connectionInfo = {};

		try {
			// Simple connectivity test using Prisma
			await prisma.$queryRaw`SELECT 1 as status`;
			databaseStatus = "online";

			// Get basic connection info
			connectionInfo = {
				status: "connected",
				type: "PostgreSQL with Prisma",
				pooling: "automatic",
			};
		} catch (dbError) {
			logError("Failed to check database status", dbError);
			databaseStatus = "offline";
			connectionInfo = {
				status: "error",
				error: getErrorMessage(dbError),
			};
		}

		return NextResponse.json({
			success: true,
			databaseStatus,
			connectionInfo,
			timestamp: new Date().toISOString(),
		});
	} catch (error) {
		logError("Error getting database status", error);
		return NextResponse.json(
			{ error: t("databaseStatusGetFailed"), details: getErrorMessage(error) },
			{ status: 500 },
		);
	}
}

/**
 * POST /api/admin/database/status/check
 *
 * Perform a database health check and attempt recovery if needed
 * Only accessible to admin users
 */
export async function POST(_request: Request) {
	// Get locale from centralized utility at the beginning
	const locale = await getLocaleFromCookies();
	const t = await getTranslations({ locale, namespace: "admin" });

	// Check authentication
	const session = await getServerSession(authOptions);

	// Require admin authentication
	if (!session || session.user.role !== "admin") {
		return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
	}

	try {
		// Perform Prisma health check
		let healthStatus = "unknown";
		let healthMessage = "";

		try {
			// Test database connectivity
			await prisma.$queryRaw`SELECT 1 as health_check`;
			healthStatus = "healthy";
			healthMessage = "Database connection is healthy and responsive";
		} catch (healthError) {
			healthStatus = "unhealthy";
			healthMessage = `Database health check failed: ${getErrorMessage(healthError)}`;
		}

		return NextResponse.json({
			success: true,
			message: healthMessage,
			healthStatus,
			timestamp: new Date().toISOString(),
		});
	} catch (error) {
		return NextResponse.json(
			{
				error: t("databaseStatusCheckFailed"),
				details: getErrorMessage(error),
			},
			{ status: 500 },
		);
	}
}
