"use client";

import { <PERSON><PERSON>pen, ChevronDown, ChevronUp, Home, RefreshCw, ServerCrash } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const [isVisible, setIsVisible] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const isDev = process.env.NODE_ENV === "development";
  const t = useTranslations("errors");

  useEffect(() => {
    // Log the error to an error reporting service
    if (process.env.NODE_ENV === "development") {
    }

    // Trigger animation after component mounts
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="relative flex min-h-[80vh] flex-col items-center justify-center overflow-hidden px-4 py-10">
      {/* Background decorative elements */}
      <div className="-z-10 absolute inset-0 overflow-hidden">
        <div
          aria-hidden="true"
          className="-z-10 -translate-x-1/2 xl:-top-6 absolute top-0 left-1/2 blur-3xl"
        >
          <div
            className="aspect-[1155/678] w-[72.1875rem] bg-gradient-to-tr from-destructive/20 to-destructive-foreground/20 opacity-30"
            style={{
              clipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
            }}
          />
        </div>
      </div>

      <div className="relative z-10 mx-auto flex max-w-5xl flex-col items-center gap-8 md:flex-row md:gap-12">
        {/* Image */}
        <div
          className={`relative transition-all duration-700 ${isVisible ? "translate-y-0 opacity-100" : "-translate-y-8 opacity-0"}`}
        >
          <div className="relative">
            <div className="-inset-0.5 absolute rounded-2xl bg-gradient-to-r from-destructive/20 to-destructive/40 opacity-75 blur-xl" />
            <div className="relative overflow-hidden rounded-2xl border border-destructive/10 bg-background/50 backdrop-blur-sm">
              <Image
                alt="Server Error Illustration" // You can replace this with a server error specific image
                className="rounded-2xl object-cover"
                height={400}
                priority
                src="/images/404-right.webp"
                style={{ aspectRatio: "400/400", objectFit: "cover" }}
                width={400}
              />
            </div>
          </div>
        </div>

        {/* Content */}
        <div
          className={`text-center transition-all delay-300 duration-700 md:text-left ${isVisible ? "translate-y-0 opacity-100" : "translate-y-8 opacity-0"}`}
        >
          <div className="mb-4 inline-flex items-center justify-center rounded-full bg-destructive/10 p-2 text-destructive">
            <ServerCrash className="h-6 w-6" />
          </div>

          <h1 className="mb-2 bg-gradient-to-r from-destructive to-destructive/70 bg-clip-text font-bold text-7xl text-transparent tracking-tight">
            500
          </h1>
          <h2 className="mb-4 font-semibold text-3xl">{t("serverError")}</h2>

          {/* Enhanced Bible Quote Section */}
          <div className="mb-4 max-w-md">
            <div className="flex items-start gap-3">
              <BookOpen className="mt-1 h-5 w-5 flex-shrink-0 text-destructive" />
              <div>
                <p className="mb-2 text-lg italic">
                  &quot;I can do all things through Christ who strengthens me.&quot;
                </p>
                <p className="text-right text-muted-foreground text-sm">— Philippians 4:13</p>
              </div>
            </div>
          </div>

          <p className="mb-6 max-w-md text-sm">{t("errorDescription")}</p>

          {isDev && (
            <Collapsible className="mb-6 w-full">
              <div className="flex items-center justify-center space-x-2 md:justify-start">
                <CollapsibleTrigger asChild>
                  <Button
                    className="flex items-center gap-1 rounded-md border-destructive/20"
                    onClick={() => setShowDetails(!showDetails)}
                    size="sm"
                    variant="outline"
                  >
                    {showDetails ? t("hide") : t("show")} {t("errorDetails")}
                    {showDetails ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )}
                  </Button>
                </CollapsibleTrigger>
              </div>
              <CollapsibleContent className="mt-4">
                <div className="max-h-40 overflow-auto rounded-md border border-destructive/10 bg-muted p-4 text-left">
                  <p className="font-mono text-xs">{error.message}</p>
                  {error.stack && (
                    <pre className="mt-2 whitespace-pre-wrap font-mono text-xs">{error.stack}</pre>
                  )}
                  {error.digest && <p className="mt-2 font-mono text-xs">Digest: {error.digest}</p>}
                </div>
              </CollapsibleContent>
            </Collapsible>
          )}

          <div className="flex flex-wrap justify-center gap-4 md:justify-start">
            <Button
              className="gap-2 rounded-md px-6 shadow-lg transition-all hover:shadow-xl"
              onClick={reset}
              size="lg"
            >
              <RefreshCw className="h-5 w-5" />
              {t("tryAgain")}
            </Button>
            <Button
              asChild
              className="gap-2 rounded-md border-destructive/20 px-6 hover:bg-destructive/5"
              size="lg"
              variant="outline"
            >
              <Link href="/">
                <Home className="h-5 w-5" />
                {t("backToHome")}
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
