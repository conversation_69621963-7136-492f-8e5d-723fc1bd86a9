import { useCallback, useEffect, useRef } from "react";

interface PerformanceMetrics {
  memoryUsage?: number;
  renderTime?: number;
  inputLatency?: number;
  messageCount?: number;
}

interface UsePerformanceMonitorOptions {
  enabled?: boolean;
  logInterval?: number;
  onMetricsUpdate?: (metrics: PerformanceMetrics) => void;
}

/**
 * Hook for monitoring chatbot performance metrics
 * Helps identify performance bottlenecks and memory leaks
 */
export function usePerformanceMonitor({
  enabled = process.env.NODE_ENV === "development",
  logInterval = 30_000, // 30 seconds
  onMetricsUpdate,
}: UsePerformanceMonitorOptions = {}) {
  const metricsRef = useRef<PerformanceMetrics>({});
  const intervalRef = useRef<number | null>(null);
  const renderStartTime = useRef<number>(0);

  // Track render performance
  const trackRenderStart = useCallback(() => {
    if (enabled) {
      renderStartTime.current = performance.now();
    }
  }, [enabled]);

  const trackRenderEnd = useCallback(() => {
    if (enabled && renderStartTime.current > 0) {
      const renderTime = performance.now() - renderStartTime.current;
      metricsRef.current.renderTime = renderTime;
      renderStartTime.current = 0;
    }
  }, [enabled]);

  // Track input latency
  const trackInputLatency = useCallback(
    (startTime: number) => {
      if (enabled) {
        const latency = performance.now() - startTime;
        metricsRef.current.inputLatency = latency;
      }
    },
    [enabled],
  );

  // Track message count
  const trackMessageCount = useCallback(
    (count: number) => {
      if (enabled) {
        metricsRef.current.messageCount = count;
      }
    },
    [enabled],
  );

  // Get memory usage (if available)
  const getMemoryUsage = useCallback(() => {
    if (enabled && "memory" in performance) {
      try {
        const memory = (
          performance as {
            memory?: {
              usedJSHeapSize: number;
              totalJSHeapSize: number;
              jsHeapSizeLimit: number;
            };
          }
        ).memory;
        if (memory) {
          return {
            used: memory.usedJSHeapSize,
            total: memory.totalJSHeapSize,
            limit: memory.jsHeapSizeLimit,
          };
        }
      } catch {
        return null;
      }
    }
    return null;
  }, [enabled]);

  // Log performance metrics
  const logMetrics = useCallback(() => {
    if (!enabled) return;

    const memory = getMemoryUsage();
    const metrics: PerformanceMetrics = {
      ...metricsRef.current,
      memoryUsage: memory ? memory.used / 1024 / 1024 : undefined, // Convert to MB
    };

    if (process.env.NODE_ENV === "development") {
      console.group("🔍 Chatbot Performance Metrics");
      console.log(
        "📊 Render Time:",
        metrics.renderTime ? `${metrics.renderTime.toFixed(2)}ms` : "N/A",
      );
      console.log(
        "⌨️ Input Latency:",
        metrics.inputLatency ? `${metrics.inputLatency.toFixed(2)}ms` : "N/A",
      );
      console.log("💬 Message Count:", metrics.messageCount || 0);

      if (memory) {
        console.log("🧠 Memory Usage:", `${(memory.used / 1024 / 1024).toFixed(2)} MB`);
        console.log("📈 Memory Total:", `${(memory.total / 1024 / 1024).toFixed(2)} MB`);
        console.log("⚠️ Memory Limit:", `${(memory.limit / 1024 / 1024).toFixed(2)} MB`);

        const memoryUsagePercent = (memory.used / memory.limit) * 100;
        if (memoryUsagePercent > 70) {
          console.warn("⚠️ High memory usage detected:", `${memoryUsagePercent.toFixed(1)}%`);
        }
      }

      console.groupEnd();
    }

    // Call callback if provided
    if (onMetricsUpdate) {
      onMetricsUpdate(metrics);
    }
  }, [enabled, getMemoryUsage, onMetricsUpdate]);

  // Set up periodic logging
  useEffect(() => {
    if (enabled && logInterval > 0) {
      intervalRef.current = setInterval(logMetrics, logInterval) as unknown as number;

      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      };
    }
  }, [enabled, logInterval, logMetrics]);

  return {
    trackRenderStart,
    trackRenderEnd,
    trackInputLatency,
    trackMessageCount,
    logMetrics,
    getMemoryUsage,
    isEnabled: enabled,
  };
}
