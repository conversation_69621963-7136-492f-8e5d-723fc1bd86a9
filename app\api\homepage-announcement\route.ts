import { NextResponse } from "next/server";
import { prisma } from "@/lib/db/prisma";

/**
 * GET /api/homepage-announcement
 * Public endpoint to retrieve homepage announcement for display
 * No authentication required - this is public information
 */
export async function GET() {
	try {
		// Fetch homepage announcement settings
		const settings = await prisma.systemSettings.findMany({
			where: {
				settingKey: {
					in: [
						"homepage_announcement_enabled",
						"homepage_announcement_text",
						"homepage_announcement_type",
					],
				},
			},
		});

		// Convert to object format
		const settingsMap = settings.reduce(
			(acc, setting) => {
				acc[setting.settingKey] = setting.settingValue;
				return acc;
			},
			{} as Record<string, string | null>,
		);

		// Only return data if announcement is enabled
		const enabled = settingsMap.homepage_announcement_enabled === "true";

		if (!enabled) {
			return NextResponse.json({
				enabled: false,
				text: "",
				type: "info",
			});
		}

		return NextResponse.json({
			enabled: true,
			text: settingsMap.homepage_announcement_text || "",
			type: settingsMap.homepage_announcement_type || "info",
		});
	} catch (_error) {
		if (process.env.NODE_ENV === "development") {
		}

		// Graceful fallback - return disabled announcement
		return NextResponse.json({
			enabled: false,
			text: "",
			type: "info",
		});
	}
}

// Force dynamic rendering to prevent caching of announcement data
export const dynamic = "force-dynamic";
