import J<PERSON><PERSON><PERSON> from "jszip";
import { type NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { z } from "zod/v4";
import { authOptions } from "@/lib/auth/auth-options";
import { exportDatabase, logExport } from "@/lib/db/export-prisma";
import { getErrorMessage, isZodError } from "@/lib/utils/error-handling";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";

// Validation schema for export request
const exportSchema = z.object({
	format: z.enum(["sql", "csv", "json"]),
	tables: z.enum(["all", "members", "households", "settings"]),
});

export async function POST(request: NextRequest) {
	// Get locale from centralized utility at the beginning
	const locale = await getLocaleFromCookies();
	const tAdmin = await getTranslations({ locale, namespace: "admin" });

	try {
		// Check authentication
		const session = await getServerSession(authOptions);

		// Require admin authentication
		if (!session || session.user.role !== "admin") {
			return NextResponse.json(
				{ error: tAdmin("unauthorized") },
				{ status: 401 },
			);
		}

		// Get admin ID from session
		const adminId = Number.parseInt(session.user.id, 10);

		// Parse and validate request body
		const data = await request.json();
		const validatedData = exportSchema.parse(data);

		// Get client IP address
		const ipAddress =
			request.headers.get("x-forwarded-for") ||
			request.headers.get("x-real-ip") ||
			"unknown";

		// Export database
		const exportResult = await exportDatabase({
			format: validatedData.format,
			tables: validatedData.tables,
			adminId,
		});

		// Log the export activity
		await logExport(
			adminId,
			validatedData.tables,
			validatedData.format,
			exportResult.recordCount,
			ipAddress as string,
		);

		// Handle different export formats
		if (validatedData.format === "csv") {
			// For CSV, we need to create a zip file with multiple CSVs
			const zip = new JSZip();

			for (const [tableName, csvContent] of Object.entries(exportResult.data)) {
				zip.file(`${tableName}.csv`, csvContent as string);
			}

			const zipBuffer = await zip.generateAsync({ type: "arraybuffer" });

			return new NextResponse(zipBuffer, {
				headers: {
					"Content-Type": exportResult.contentType,
					"Content-Disposition": `attachment; filename="${exportResult.filename}"`,
				},
			});
		}
		if (validatedData.format === "json") {
			// For JSON, return the JSON data
			return NextResponse.json(exportResult.data, {
				headers: {
					"Content-Disposition": `attachment; filename="${exportResult.filename}"`,
				},
			});
		}
		// For SQL, return the SQL text
		return new NextResponse(exportResult.data as string, {
			headers: {
				"Content-Type": exportResult.contentType,
				"Content-Disposition": `attachment; filename="${exportResult.filename}"`,
			},
		});
	} catch (error) {
		// Handle validation errors
		if (isZodError(error)) {
			return NextResponse.json(
				{ error: tAdmin("validationFailed") },
				{ status: 400 },
			);
		}

		return NextResponse.json(
			{
				error: tAdmin("failedToExportDatabase"),
				details: getErrorMessage(error),
			},
			{ status: 500 },
		);
	}
}
