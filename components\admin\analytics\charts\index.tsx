"use client";

// Import all chart components to register them
import "./bar-chart";
import "./pie-chart";
import "./line-chart";
import "./scatter-chart";
import "./heatmap-chart";
import "./area-chart";
import "./radar-chart";
import "./treemap-chart";
import "./waffle-chart";

export type {
  BaseChartProps,
  ChartComponent,
  ChartRegistry,
  ChartWrapperProps,
} from "./chart-registry";
// Re-export the registry and factory
export {
  ChartFactory,
  ChartWrapper,
  chartRegistry,
  getAvailableChartTypes,
  getChartComponent,
  registerChart,
} from "./chart-registry";
// Re-export utilities and constants
export {
  CHART_COLORS,
  CHART_CONFIGS,
  CHART_DEFAULTS,
  type ChartColorPalette,
  type ChartSize,
  getChartColor,
  getColorByString,
  getSemanticColor,
  MOBILE_CHART_CONFIGS,
  type SemanticColorType,
} from "./constants";
// Re-export export functionality (simplified to CSV and PNG only)
export {
  type ExportFormat,
  type ExportOptions,
  exportChart,
  exportChartAsImage,
  exportToCSV,
  getAvailableExportFormats,
  validateExportOptions,
} from "./export";
export { ExportButton } from "./export-button";
// Re-export mobile responsive wrapper
export {
  MobileResponsiveChartWrapper,
  useResponsiveChartConfig,
} from "./mobile-responsive-chart-wrapper";
export {
  addColorsToData,
  type BaseDataPoint,
  calculateOptimalDimensions,
  formatChartValue,
  generateChartDescription,
  type HeatmapDataPoint,
  processChartData,
  processHeatmapData,
  processRadarData,
  processScatterData,
  processStandardChartData,
  processTreemapData,
  processWaffleData,
  type RadarDataPoint,
  type ScatterDataPoint,
  type StandardDataPoint,
  type TreemapDataPoint,
  type WaffleDataPoint,
} from "./utils";
// Re-export validation
export {
  type ValidationResult,
  validateAreaChartData,
  validateBarChartData,
  validateChartData as validateChartDataComprehensive,
  validateHeatmapData,
  validateLineChartData,
  validatePieChartData,
  validateRadarChartData,
  validateScatterChartData,
  validateTreemapData,
  validateWaffleData,
} from "./validation";
// Re-export advanced components
export { shouldUseVirtualization, VirtualizedChart } from "./virtualized-chart";
