/**
 * Census localStorage cleanup utilities
 * Provides centralized cleanup for all census-related localStorage data
 * Maintains complete separation from admin system storage
 */

/**
 * Census-specific localStorage keys that should be cleared on account deletion
 * Add new census-related keys here as they are introduced
 */
const CENSUS_STORAGE_KEYS = [
  "census-welcome-dismissed",
  "census-progress-state", // Progress tracking cache
  "census-tour-state", // Tour completion state
  // Add other census-specific keys here as needed
] as const;

/**
 * Census-specific localStorage key prefixes that should be cleared on account deletion
 * Any key starting with these prefixes will be removed
 */
const CENSUS_STORAGE_PREFIXES = [
  "census-",
  // Add other census-specific prefixes here as needed
] as const;

/**
 * Clears all census-related localStorage data
 * This function should be called when:
 * 1. Census participant deletes their own account
 * 2. Admin deletes a census participant's household
 * 3. Account deletion is detected during authentication
 */
export function clearCensusLocalStorage(): void {
  if (typeof window === "undefined") {
    return;
  }

  try {
    let clearedCount = 0;

    // Clear specific census keys
    for (const key of CENSUS_STORAGE_KEYS) {
      try {
        if (localStorage.getItem(key) !== null) {
          localStorage.removeItem(key);
          clearedCount++;
        }
      } catch (error) {
        // Silently handle individual key removal errors
      }
    }

    // Clear keys with census prefixes
    const allKeys = Object.keys(localStorage);
    for (const key of allKeys) {
      const shouldClear = CENSUS_STORAGE_PREFIXES.some((prefix) => key.startsWith(prefix));
      if (shouldClear) {
        try {
          localStorage.removeItem(key);
          clearedCount++;
        } catch (error) {
          // Silently handle individual key removal errors
        }
      }
    }
  } catch (error) {
    // Handle localStorage access errors gracefully
  }
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use clearCensusLocalStorage() instead
 */
export function resetWelcomeModalPreferences(): void {
  if (typeof window === "undefined") return;

  try {
    localStorage.removeItem("census-welcome-dismissed");
  } catch (error) {
    // Silently handle localStorage errors
  }
}

/**
 * Checks if any census-related localStorage data exists
 * Useful for debugging or conditional cleanup logic
 */
export function hasCensusLocalStorageData(): boolean {
  if (typeof window === "undefined") return false;

  try {
    // Check specific keys
    for (const key of CENSUS_STORAGE_KEYS) {
      if (localStorage.getItem(key) !== null) {
        return true;
      }
    }

    // Check prefixed keys
    const allKeys = Object.keys(localStorage);
    return allKeys.some((key) => CENSUS_STORAGE_PREFIXES.some((prefix) => key.startsWith(prefix)));
  } catch (error) {
    return false;
  }
}
