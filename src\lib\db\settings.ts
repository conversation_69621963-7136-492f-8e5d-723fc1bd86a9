/**
 * Settings Database Operations with Prisma
 * Replaces MySQL-based implementation with Prisma ORM
 */

import appCache from "@/lib/cache";
import { prisma } from "./prisma";

// import type { SystemSetting as _SystemSetting } from '@prisma/client';

// Cache keys
const CHURCH_INFO_CACHE_KEY = "church:info";
const SITE_URL_CACHE_KEY = "site:url";

// Cache TTL in milliseconds
const SETTINGS_CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Get a system setting by key
 */
export async function getSystemSetting(key: string): Promise<string | null> {
	const setting = await prisma.systemSettings.findUnique({
		where: { settingKey: key },
	});

	return setting ? setting.settingValue : null;
}

/**
 * Get multiple system settings by keys
 */
export async function getSystemSettings(
	keys: string[],
): Promise<Record<string, string>> {
	if (keys.length === 0) return {};

	const settings = await prisma.systemSettings.findMany({
		where: {
			settingKey: {
				in: keys,
			},
		},
	});

	const result: Record<string, string> = {};
	settings.forEach((setting) => {
		result[setting.settingKey] = setting.settingValue || "";
	});

	return result;
}

/**
 * Update a system setting
 */
export async function updateSystemSetting(
	key: string,
	value: string,
): Promise<void> {
	await prisma.systemSettings.upsert({
		where: { settingKey: key },
		update: { settingValue: value },
		create: { settingKey: key, settingValue: value },
	});
}

/**
 * Update multiple system settings
 */
export async function updateSystemSettings(
	settings: Record<string, string>,
): Promise<void> {
	const keys = Object.keys(settings);
	if (keys.length === 0) return;

	// Use transaction for atomic updates
	await prisma.$transaction(
		keys.map((key) =>
			prisma.systemSettings.upsert({
				where: { settingKey: key },
				update: { settingValue: settings[key] },
				create: { settingKey: key, settingValue: settings[key] },
			}),
		),
	);
}

/**
 * Get church information settings with caching
 */
export async function getChurchInfo(): Promise<{
	churchName: string;
	email: string;
	contactNumber: string;
	address: string;
}> {
	// Try to get from cache first
	return appCache.getOrSet(
		CHURCH_INFO_CACHE_KEY,
		async () => {
			const settings = await getSystemSettings([
				"church_name",
				"admin_email",
				"church_phone",
				"church_address",
			]);

			return {
				churchName: settings.church_name || "",
				email: settings.admin_email || "",
				contactNumber: settings.church_phone || "",
				address: settings.church_address || "",
			};
		},
		SETTINGS_CACHE_TTL,
	);
}

/**
 * Clear church info cache
 * Call this after updating church info to ensure the latest values are used
 */
export function clearChurchInfoCache(): void {
	appCache.delete(CHURCH_INFO_CACHE_KEY);
}

/**
 * Update church information settings
 */
export async function updateChurchInfo(churchInfo: {
	churchName: string;
	email: string;
	contactNumber: string;
	address: string;
}): Promise<void> {
	await updateSystemSettings({
		church_name: churchInfo.churchName,
		admin_email: churchInfo.email,
		church_phone: churchInfo.contactNumber,
		church_address: churchInfo.address,
	});

	// Clear the cache to ensure the latest values are used
	clearChurchInfoCache();
}

/**
 * Get site URL setting with caching
 */
export async function getSiteUrl(): Promise<string> {
	// Try to get from cache first
	return appCache.getOrSet(
		SITE_URL_CACHE_KEY,
		async () => {
			const siteUrl = await getSystemSetting("site_url");
			return siteUrl || "http://localhost:3000";
		},
		SETTINGS_CACHE_TTL,
	);
}

/**
 * Clear site URL cache
 * Call this after updating site URL to ensure the latest value is used
 */
export function clearSiteUrlCache(): void {
	appCache.delete(SITE_URL_CACHE_KEY);
}

/**
 * Update site URL setting
 */
export async function updateSiteUrl(url: string): Promise<void> {
	await updateSystemSetting("site_url", url);

	// Clear the cache to ensure the latest value is used
	clearSiteUrlCache();
}

/**
 * Get rate limiting settings with caching
 */
export async function getRateLimitSettings(): Promise<{
	maxAttempts: number;
	lockoutMinutes: number;
	escalationMinutes: number;
}> {
	// Try to get from cache first
	return appCache.getOrSet(
		"rate_limit:settings",
		async () => {
			const settings = await getSystemSettings([
				"rate_limit_max_attempts",
				"rate_limit_lockout_minutes",
				"rate_limit_escalation_minutes",
			]);

			// Parse and validate settings with fallbacks
			const maxAttempts = Number.parseInt(
				settings.rate_limit_max_attempts || "5",
				10,
			);
			const lockoutMinutes = Number.parseInt(
				settings.rate_limit_lockout_minutes || "15",
				10,
			);
			const escalationMinutes = Number.parseInt(
				settings.rate_limit_escalation_minutes || "15",
				10,
			);

			// Validate parsed values and use defaults if invalid
			return {
				maxAttempts:
					isNaN(maxAttempts) || maxAttempts < 1 || maxAttempts > 20
						? 5
						: maxAttempts,
				lockoutMinutes:
					isNaN(lockoutMinutes) || lockoutMinutes < 1 || lockoutMinutes > 1440
						? 15
						: lockoutMinutes,
				escalationMinutes:
					isNaN(escalationMinutes) ||
					escalationMinutes < 0 ||
					escalationMinutes > 120
						? 15
						: escalationMinutes,
			};
		},
		SETTINGS_CACHE_TTL,
	);
}

/**
 * Clear rate limit settings cache
 * Call this after updating rate limit settings to ensure the latest values are used
 */
export function clearRateLimitSettingsCache(): void {
	appCache.delete("rate_limit:settings");
}

/**
 * Update rate limiting settings
 */
export async function updateRateLimitSettings(rateLimitSettings: {
	maxAttempts: number;
	lockoutMinutes: number;
	escalationMinutes: number;
}): Promise<void> {
	// Validate inputs to prevent cache poisoning
	if (
		!Number.isInteger(rateLimitSettings.maxAttempts) ||
		rateLimitSettings.maxAttempts < 1 ||
		rateLimitSettings.maxAttempts > 20
	) {
		throw new Error("Invalid maxAttempts: must be integer between 1 and 20");
	}

	if (
		!Number.isInteger(rateLimitSettings.lockoutMinutes) ||
		rateLimitSettings.lockoutMinutes < 1 ||
		rateLimitSettings.lockoutMinutes > 1440
	) {
		throw new Error(
			"Invalid lockoutMinutes: must be integer between 1 and 1440",
		);
	}

	if (
		!Number.isInteger(rateLimitSettings.escalationMinutes) ||
		rateLimitSettings.escalationMinutes < 0 ||
		rateLimitSettings.escalationMinutes > 120
	) {
		throw new Error(
			"Invalid escalationMinutes: must be integer between 0 and 120",
		);
	}

	await updateSystemSettings({
		rate_limit_max_attempts: rateLimitSettings.maxAttempts.toString(),
		rate_limit_lockout_minutes: rateLimitSettings.lockoutMinutes.toString(),
		rate_limit_escalation_minutes:
			rateLimitSettings.escalationMinutes.toString(),
	});

	// Clear the cache to ensure the latest values are used
	clearRateLimitSettingsCache();
}
