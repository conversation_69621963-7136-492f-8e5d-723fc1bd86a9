#!/usr/bin/env node

/**
 * Complete setup script for new server deployment
 * This script sets up the WSCCC Census System from scratch
 */

import { execSync } from "child_process";
import { existsSync, readFileSync } from "fs";
import { join } from "path";

const projectRoot = process.cwd();

console.log("🚀 WSCCC Census System - New Server Setup");
console.log("==========================================\n");

// Utility function for running commands (currently unused but kept for future use)
// function runCommand(command, description) {
//   console.log(`📋 ${description}...`);
//   try {
//     execSync(command, { stdio: 'inherit', cwd: projectRoot });
//     console.log(`   ✅ ${description} completed\n`);
//   } catch (error) {
//     console.error(`   ❌ ${description} failed:`, error.message);
//     process.exit(1);
//   }
// }

function checkFile(filePath, description) {
  const fullPath = join(projectRoot, filePath);
  if (existsSync(fullPath)) {
    console.log(`   ✅ ${description}: ${filePath}`);
    return true;
  }
  console.log(`   ❌ ${description}: ${filePath} - MISSING`);
  return false;
}

// Step 1: Check prerequisites
console.log("1. Checking prerequisites...");
try {
  execSync("node --version", { stdio: "pipe" });
  console.log("   ✅ Node.js is installed");
} catch {
  console.error("   ❌ Node.js is not installed");
  process.exit(1);
}

try {
  execSync("npm --version", { stdio: "pipe" });
  console.log("   ✅ npm is available");
} catch {
  console.error("   ❌ npm is not available");
  process.exit(1);
}

try {
  execSync("psql --version", { stdio: "pipe" });
  console.log("   ✅ PostgreSQL client is installed");
} catch {
  console.error("   ❌ PostgreSQL client is not installed");
  console.error("      Please install PostgreSQL first");
  process.exit(1);
}

// Step 2: Check required files
console.log("\n2. Checking required files...");
const requiredFiles = [
  { path: "../package.json", desc: "Package configuration" },
  { path: "./database-postgresql.sql", desc: "PostgreSQL schema" },
  { path: "../prisma/schema.prisma", desc: "Prisma schema" },
  { path: "../.env.local", desc: "Environment configuration" },
];

let allFilesExist = true;
for (const file of requiredFiles) {
  if (!checkFile(file.path, file.desc)) {
    allFilesExist = false;
  }
}

if (!allFilesExist) {
  console.error("\n❌ Some required files are missing. Please ensure all files are present.");
  process.exit(1);
}

// Step 3: Check environment configuration
console.log("\n3. Checking environment configuration...");
const envPath = join(projectRoot, "../.env.local");
const envContent = readFileSync(envPath, "utf8");

if (envContent.includes("DATABASE_URL")) {
  console.log("   ✅ DATABASE_URL is configured");
} else {
  console.error("   ❌ DATABASE_URL is not configured in .env.local");
  console.error(
    '      Please add: DATABASE_URL="postgresql://user:password@localhost:5432/database_name"',
  );
  process.exit(1);
}

// Step 4: Install dependencies (run from parent directory)
console.log("4. Installing dependencies...");
try {
  execSync("npm install", { stdio: "inherit", cwd: join(projectRoot, "..") });
  console.log("   ✅ Dependencies installed successfully");
} catch (error) {
  console.error("   ❌ Failed to install dependencies:", error.message);
  process.exit(1);
}

// Step 5: Setup database
console.log("5. Setting up PostgreSQL database...");
console.log("   ℹ️  Make sure PostgreSQL is running and you have created the database");
console.log("   ℹ️  Example: createdb wsccc_census_db_pg");
console.log("   ℹ️  Applying database schema...");

try {
  // Extract database name from DATABASE_URL for schema application
  const dbUrlMatch = envContent.match(/DATABASE_URL="postgresql:\/\/[^/]+\/([^"?]+)/);
  if (dbUrlMatch) {
    const dbName = dbUrlMatch[1];
    console.log(`   📋 Applying schema to database: ${dbName}`);

    // Apply the schema
    execSync(`psql -d ${dbName} -f database-postgresql.sql`, {
      stdio: "inherit",
      cwd: projectRoot,
    });
    console.log("   ✅ Database schema applied successfully");
  } else {
    console.error("   ❌ Could not extract database name from DATABASE_URL");
    process.exit(1);
  }
} catch (error) {
  console.error("   ❌ Failed to apply database schema:", error.message);
  console.error("   💡 Make sure:");
  console.error("      - PostgreSQL is running");
  console.error("      - Database exists (createdb your_database_name)");
  console.error("      - User has proper permissions");
  process.exit(1);
}

// Step 6: Generate Prisma client
console.log("6. Generating Prisma client...");
try {
  execSync("npm run db:generate", {
    stdio: "inherit",
    cwd: join(projectRoot, ".."),
  });
  console.log("   ✅ Prisma client generated successfully");
} catch (error) {
  console.error("   ❌ Failed to generate Prisma client:", error.message);
  process.exit(1);
}

// Step 7: Sync Prisma schema with database
console.log("7. Syncing Prisma schema with database...");
try {
  execSync("npm run db:push", {
    stdio: "inherit",
    cwd: join(projectRoot, ".."),
  });
  console.log("   ✅ Prisma schema synced successfully");
} catch (error) {
  console.error("   ❌ Failed to sync Prisma schema:", error.message);
  process.exit(1);
}

// Step 8: Database is ready (seeding handled by database-postgresql.sql)
console.log("8. Database setup complete - initial data included in schema file");

// Step 9: Final verification
console.log("9. Final verification...");
try {
  execSync("npm run db:generate", {
    stdio: "pipe",
    cwd: join(projectRoot, ".."),
  });
  console.log("   ✅ Prisma client generation successful");
} catch {
  console.error("   ❌ Prisma client generation failed");
  process.exit(1);
}

console.log("\n🎉 Setup completed successfully!");
console.log("=====================================");
console.log("");
console.log("📋 What was set up:");
console.log("   ✅ Dependencies installed");
console.log("   ✅ PostgreSQL database schema applied");
console.log("   ✅ Prisma client generated");
console.log("   ✅ Database schema applied with initial data");
console.log("");
console.log("🚀 Next steps:");
console.log("   1. Start the development server: npm run dev");
console.log("   2. Access the application at: http://localhost:3000");
console.log("   3. Login to admin panel with: admin / password");
console.log("");
console.log("⚠️  IMPORTANT SECURITY NOTES:");
console.log("   - Change the default admin password immediately");
console.log("   - Update environment variables for production");
console.log("   - Review and update system settings as needed");
console.log("");
console.log("📚 Additional commands:");
console.log("   - View database: npm run db:studio");
console.log("   - Backup database: npm run db:backup");
console.log("   - Reset database: psql -d database_name -f database-postgresql.sql");
console.log("");
