"use client";

import React from "react";
import { TourOverlay } from "./TourOverlay";
import { TourPopover } from "./TourPopover";
import { useTourContext } from "./TourProvider";

/**
 * Minimal Tour System
 */
export function TourSystem() {
  const { activeTour, setActiveTour, isVisible } = useTourContext();

  if (!(activeTour && isVisible)) return null;

  const handleClose = () => setActiveTour(null);

  return (
    <TourOverlay onClose={handleClose} targetElement={activeTour.element}>
      <TourPopover
        content={activeTour.content}
        onClose={handleClose}
        targetElement={activeTour.element}
      />
    </TourOverlay>
  );
}
