import { type NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { z } from "zod/v4";
import { authOptions } from "@/lib/auth/auth-options";
import { prisma } from "@/lib/db/prisma";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";

// Simple rate limiting for admin API (following existing patterns)
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 30; // 30 requests per minute per admin

const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

// Rate limiting helper with automatic cleanup
function checkRateLimit(userId: string): boolean {
  const now = Date.now();

  // Clean up expired entries periodically
  if (rateLimitMap.size > 50) {
    for (const [key, value] of rateLimitMap.entries()) {
      if (now > value.resetTime) {
        rateLimitMap.delete(key);
      }
    }
  }

  const userLimit = rateLimitMap.get(userId);

  if (!userLimit || now > userLimit.resetTime) {
    // Reset or create new limit window
    rateLimitMap.set(userId, {
      count: 1,
      resetTime: now + RATE_LIMIT_WINDOW,
    });
    return true;
  }

  if (userLimit.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false; // Rate limit exceeded
  }

  // Increment count
  userLimit.count++;
  return true;
}

// Validation schema for suburb search (same as census version)
const suburbSearchSchema = z.object({
  q: z
    .string()
    .min(2, { error: "Search term must be at least 2 characters" })
    .max(50, { error: "Search term cannot exceed 50 characters" })
    .trim()
    // Permissive validation for Australian place names - allows letters, numbers, spaces,
    // apostrophes, hyphens, periods, parentheses, and ampersands
    // Examples: O'Connor, St. Kilda, Mt. Gravatt, Kings Cross, D'Aguilar Heights
    .regex(/^[a-zA-Z0-9\s\-'.&()]+$/, {
      error: "Please use only letters, numbers, spaces, and common punctuation",
    })
    // Additional security: prevent potential script injection patterns
    .refine((val) => !/<|>|script|javascript|onload|onerror/i.test(val), {
      error: "Invalid characters detected",
    }),
});

// Using Prisma-generated types instead of manual interface

/**
 * GET /api/admin/suburbs/search
 * Search suburbs for admin autocomplete
 * Protected by admin authentication middleware
 */
export async function GET(request: NextRequest) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const t = await getTranslations({ locale, namespace: "errors" });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require admin authentication
    if (!session || session.user.role !== "admin") {
      return NextResponse.json({ error: t("unauthorized") }, { status: 401 });
    }

    // Apply rate limiting
    const userId = session.user.id || session.user.email || "unknown";
    if (!checkRateLimit(userId)) {
      return NextResponse.json(
        {
          error: "Rate limit exceeded",
          details: "Too many requests. Please try again later.",
        },
        { status: 429 },
      );
    }

    // Get search query from URL parameters
    const { searchParams } = new URL(request.url);
    const searchQuery = searchParams.get("q");

    if (!searchQuery) {
      return NextResponse.json(
        {
          error: "Search query is required",
          details: 'Please provide a search term using the "q" parameter',
        },
        { status: 400 },
      );
    }

    // Validate search query
    const validationResult = suburbSearchSchema.safeParse({ q: searchQuery });

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: "Invalid search query",
          details: validationResult.error.issues.map((issue) => issue.message).join(", "),
        },
        { status: 400 },
      );
    }

    const validatedQuery = validationResult.data.q;

    // Search terms no longer needed with Prisma contains search
    // const searchTerm = `%${validatedQuery}%`;
    // const prefixTerm = `${validatedQuery}%`;

    // Search suburbs using Prisma with PostgreSQL ILIKE for case-insensitive search
    const suburbs = await prisma.suburb.findMany({
      select: {
        id: true,
        displayName: true,
        suburbName: true,
        stateCode: true,
      },
      where: {
        OR: [
          { displayName: { contains: validatedQuery, mode: "insensitive" } },
          { searchText: { contains: validatedQuery, mode: "insensitive" } },
          { suburbName: { contains: validatedQuery, mode: "insensitive" } },
        ],
      },
      orderBy: [
        {
          displayName: "asc",
        },
      ],
      take: 50,
    });

    // Log search activity in development
    if (process.env.NODE_ENV === "development") {
    }

    // Transform Prisma data to match frontend expectations (camelCase)
    const transformedSuburbs = suburbs.map((suburb) => ({
      id: suburb.id,
      displayName: suburb.displayName,
      suburbName: suburb.suburbName,
      stateCode: suburb.stateCode,
    }));

    return NextResponse.json({
      success: true,
      results: transformedSuburbs,
      count: transformedSuburbs.length,
      query: searchQuery,
    });
  } catch (_error) {
    // Return user-friendly error message without exposing internals
    return NextResponse.json(
      {
        error: "Failed to search suburbs",
        details: "Please try again later",
      },
      { status: 500 },
    );
  }
}
