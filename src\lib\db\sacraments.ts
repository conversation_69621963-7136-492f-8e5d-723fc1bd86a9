/**
 * Sacraments Database Operations with Prisma
 * Replaces MySQL-based implementation with Prisma ORM
 */

import type { Sacrament, SacramentType } from "@prisma/client";
import { prisma } from "./prisma";

export async function getSacraments(): Promise<Sacrament[]> {
  return await prisma.sacrament.findMany({
    orderBy: [{ memberId: "asc" }, { sacramentTypeId: "asc" }],
  });
}

export async function getSacramentById(id: number): Promise<Sacrament | null> {
  return await prisma.sacrament.findUnique({
    where: { id },
  });
}

export async function getSacramentsByMemberId(memberId: number): Promise<Sacrament[]> {
  return await prisma.sacrament.findMany({
    where: { memberId },
    orderBy: { sacramentTypeId: "asc" },
  });
}

export async function getSacramentsByType(sacramentTypeId: number): Promise<Sacrament[]> {
  return await prisma.sacrament.findMany({
    where: { sacramentTypeId },
    orderBy: { memberId: "asc" },
  });
}

export async function getSacramentsByCensusYear(censusYearId: number): Promise<Sacrament[]> {
  return await prisma.sacrament.findMany({
    where: { censusYearId },
    orderBy: [{ memberId: "asc" }, { sacramentTypeId: "asc" }],
  });
}

export async function createSacrament(data: {
  memberId: number;
  sacramentTypeId: number;
  date?: Date | null;
  place?: string | null;
  notes?: string | null;
  censusYearId: number;
}): Promise<Sacrament> {
  return await prisma.sacrament.create({
    data: {
      memberId: data.memberId,
      sacramentTypeId: data.sacramentTypeId,
      date: data.date,
      place: data.place,
      notes: data.notes,
      censusYearId: data.censusYearId,
    },
  });
}

export async function updateSacrament(
  id: number,
  data: Partial<{
    sacramentTypeId: number;
    date: Date | null;
    place: string | null;
    notes: string | null;
    censusYearId: number;
  }>,
): Promise<Sacrament | null> {
  try {
    return await prisma.sacrament.update({
      where: { id },
      data,
    });
  } catch (error) {
    console.error("Error updating sacrament:", error);
    return null;
  }
}

export async function deleteSacrament(id: number): Promise<boolean> {
  try {
    await prisma.sacrament.delete({
      where: { id },
    });
    return true;
  } catch (error) {
    console.error("Error deleting sacrament:", error);
    return false;
  }
}

// Define interface for member sacrament summary
export interface MemberSacramentSummary {
  memberId: number;
  firstName: string;
  lastName: string;
  received_sacraments: string;
  baptism_date: Date | null;
  confirmation_date: Date | null;
  communion_date: Date | null;
  matrimony_date: Date | null;
}

export async function getMemberSacramentSummary(
  memberId: number,
): Promise<MemberSacramentSummary[]> {
  const member = await prisma.member.findUnique({
    where: { id: memberId },
    include: {
      sacraments: {
        include: {
          sacramentType: true,
        },
      },
    },
  });

  if (!member) return [];

  // Process sacraments to create summary
  const sacramentsByType: Record<string, Date | null> = {};
  const sacramentNames: string[] = [];

  member.sacraments.forEach((sacrament) => {
    const typeName = sacrament.sacramentType.name;
    sacramentNames.push(typeName);
    sacramentsByType[typeName.toLowerCase()] = sacrament.date;
  });

  return [
    {
      memberId: member.id,
      firstName: member.firstName,
      lastName: member.lastName,
      received_sacraments: sacramentNames.join(", "),
      baptism_date: sacramentsByType["baptism"] || null,
      confirmation_date: sacramentsByType["confirmation"] || null,
      communion_date: sacramentsByType["communion"] || null,
      matrimony_date: sacramentsByType["matrimony"] || null,
    },
  ];
}

export async function getSacramentTypes(): Promise<SacramentType[]> {
  return await prisma.sacramentType.findMany({
    orderBy: { id: "asc" },
  });
}

export async function getSacramentTypeById(id: number): Promise<SacramentType | null> {
  return await prisma.sacramentType.findUnique({
    where: { id },
  });
}
