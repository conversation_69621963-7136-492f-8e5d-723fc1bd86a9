import appCache from "@/lib/cache/index";
import {
  getCensusControlsSettings,
  updateCensusControlsSettings,
} from "@/lib/db/census-controls-settings";
import { getCurrentSydneyTime, isDateAfter, isDateBefore } from "@/lib/utils/date-time";

// Cache keys
const CENSUS_STATUS_CACHE_KEY = "census:status";
const CENSUS_SETTINGS_CACHE_KEY = "census:settings";

// Cache TTL in milliseconds - short TTL for real-time admin control updates
const CENSUS_STATUS_CACHE_TTL = 10 * 1000; // 10 seconds

/**
 * Checks if the census is currently open based on manual settings and schedule
 * Also updates the system state based on the schedule if needed
 * @returns Object containing census availability status
 */
export async function isCensusOpen(): Promise<{
  isOpen: boolean;
  isManualOverride: boolean;
  scheduledState: boolean | null;
  nextChangeTime: Date | null;
}> {
  // Try to get from cache first
  return appCache.getOrSet(
    CENSUS_STATUS_CACHE_KEY,
    async () => {
      // Get current settings from database
      const settings = await getCensusControlsSettings();

      // Get current time in Australia/Sydney timezone
      const sydneyTime = getCurrentSydneyTime();

      // Initialize result
      let isOpen = settings.systemOpen;
      const isManualOverride = settings.manualOverride;
      let scheduledState = null;
      let nextChangeTime = null;
      let needsUpdate = false;

      // Check if automatic scheduling is enabled
      if (settings.autoOpenClose && settings.censusStartDate && settings.censusEndDate) {
        const startDate = new Date(settings.censusStartDate);
        const endDate = new Date(settings.censusEndDate);

        // Determine scheduled state
        scheduledState = !(isDateBefore(sydneyTime, startDate) || isDateAfter(sydneyTime, endDate));

        // Determine next change time
        if (isDateBefore(sydneyTime, startDate)) {
          nextChangeTime = startDate; // Next change is opening
        } else if (isDateBefore(sydneyTime, endDate)) {
          nextChangeTime = endDate; // Next change is closing
        }

        // If there's no manual override and the system state doesn't match the scheduled state,
        // update the system state
        if (!isManualOverride && isOpen !== scheduledState) {
          isOpen = scheduledState;
          needsUpdate = true;
        }
      }

      // If we need to update the database, do it now
      if (needsUpdate) {
        try {
          await updateCensusControlsSettings({
            ...settings,
            systemOpen: isOpen,
          });

          // Clear settings cache since we've updated the database
          appCache.delete(CENSUS_SETTINGS_CACHE_KEY);
        } catch (error) {
          if (process.env.NODE_ENV === "development") {
            console.error("Error updating census status:", error);
          }
          // Continue with the current state even if the update fails
        }
      }

      return {
        isOpen,
        isManualOverride,
        scheduledState,
        nextChangeTime,
      };
    },
    CENSUS_STATUS_CACHE_TTL,
  );
}

/**
 * Clears the census status cache
 * Call this after updating census settings to ensure the latest values are used
 */
export function clearCensusStatusCache(): void {
  // Clear all census-related cache keys
  appCache.delete(CENSUS_STATUS_CACHE_KEY);
  appCache.delete(CENSUS_SETTINGS_CACHE_KEY);
  appCache.delete("census:lastStatusCheck");

  // Also clear any other census-related cache keys
  const allKeys = appCache.getAllKeys();
  allKeys.forEach((key) => {
    if (key.startsWith("census:")) {
      appCache.delete(key);
    }
  });

  if (process.env.NODE_ENV === "development") {
    console.log("Census status cache cleared - admin controls updated");
  }
}
