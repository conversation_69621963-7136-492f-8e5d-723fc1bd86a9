import { redirect } from "next/navigation";
import { getServerSession } from "next-auth";
import { authOptions } from "./auth-options";

/**
 * Server-side authentication check for admin pages
 * Redirects to login page if user is not authenticated or not an admin
 */
export async function requireAdmin() {
  const session = await getServerSession(authOptions);

  if (!session) {
    // Redirect to our API route that will set the cookie and redirect
    redirect("/api/auth/toast-redirect?reason=unauthenticated&redirectTo=/admin/login");
  }

  if (session.user.role !== "admin") {
    // Redirect to our API route that will set the cookie and redirect
    redirect("/api/auth/toast-redirect?reason=unauthorized&redirectTo=/admin/login");
  }

  return session;
}

/**
 * Server-side authentication check for any authenticated user
 * Redirects to login page if user is not authenticated
 */
export async function requireAuth() {
  const session = await getServerSession(authOptions);

  if (!session) {
    // Redirect to our API route that will set the cookie and redirect
    redirect("/api/auth/toast-redirect?reason=unauthenticated&redirectTo=/admin/login");
  }

  return session;
}
