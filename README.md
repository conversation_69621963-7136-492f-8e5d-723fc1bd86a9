# WSCCC Census System

[![Next.js](https://img.shields.io/badge/Next.js-15.3.1-black)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue)](https://www.typescriptlang.org/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-15+-blue)](https://www.postgresql.org/)
[![Prisma](https://img.shields.io/badge/Prisma-ORM-2D3748)](https://www.prisma.io/)
[![shadcn/ui](https://img.shields.io/badge/shadcn%2Fui-UI%20Components-000000)](https://ui.shadcn.com/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

> A comprehensive, production-ready web application for Catholic community census management with enterprise-grade security and modern UI/UX design.

## 📋 Table of Contents

- [Overview](#overview)
- [Key Features](#-key-features)
- [Technology Stack](#-technology-stack)
- [Architecture](#-architecture)
- [Getting Started](#-getting-started)
- [Installation](#installation)
- [Configuration](#configuration)
- [Usage](#-usage)
- [Analytics & Reporting](#-analytics--reporting)
- [API Documentation](#-api-documentation)
- [Security Features](#-security-features)
- [Deployment](#-deployment)
- [Documentation](#-documentation)
- [Contributing](#-contributing)
- [License](#-license)
- [Support & Community](#-support--community)

## Overview

The **WSCCC Census System** is a sophisticated, enterprise-grade web application designed specifically for Catholic community census management. Built with cutting-edge web technologies including Next.js 15.3.1, TypeScript 5, and PostgreSQL, it provides a secure, scalable platform for managing household and member information, tracking sacraments, and generating comprehensive analytics.

### 🎯 Purpose

This system addresses the unique needs of Catholic parishes and communities by providing:
- **Dual Authentication Systems**: Completely independent admin and census participant portals
- **Secure Data Management**: Enterprise-grade security with rate limiting and audit logging
- **Modern User Experience**: Responsive design with accessibility features
- **Comprehensive Analytics**: AI-powered insights and detailed reporting
- **Flexible Deployment**: Support for various hosting environments including VPS and cloud platforms

## ✨ Key Features

### 🔐 **Two Independent Authentication Systems**
- **Admin Authentication**: Separate NextAuth.js system with username/password + optional 2FA (TOTP)
- **Census Authentication**: Independent unique code-based system with QR code support
- **Complete Isolation**: Users can be logged into both systems simultaneously without interference
- **Different Cookie Names**: Separate session tokens (admin vs census) prevent conflicts
- **Rate Limiting**: IP-based protection for census auth (admin protected by 2FA)
- **Session Management**: JWT sessions with HTTP-only cookies for both systems

### 👥 **Comprehensive Member Management**
- **Household Management**: Complete family unit tracking
- **Member Profiles**: Detailed demographic information
- **Relationship Mapping**: Family relationship tracking
- **Sacrament Records**: Baptism, Confirmation, Marriage, and more
- **Census Year Tracking**: Multi-year data management

### 📊 **Advanced Analytics Dashboard**
- **Real-time Statistics**: Member counts, household data, census progress
- **Hybrid Chart System**: Advanced chart registry with Chart.js and shadcn/recharts integration
- **AI-Powered Chatbot**: Vercel AI SDK with Gemini 2.5 Flash for professional streaming responses
- **Chart Virtualization**: Automatic optimization for large datasets with pagination and windowing
- **Geographic Analysis**: Suburb-based distribution tracking

### 🎫 **Unique Code System**
- **QR Code Generation**: Professional QR codes for easy access
- **Print Management**: Bulk printing with customizable layouts
- **Code Tracking**: Assignment status and usage monitoring
- **Secure Distribution**: Single-use codes with expiration

### 🛡️ **Enterprise Security**
- **Content Security Policy**: Comprehensive CSP headers
- **SQL Injection Protection**: Parameterized queries throughout
- **XSS Protection**: Input sanitization and validation
- **Audit Logging**: Complete action tracking for compliance
- **Environment Validation**: Secure configuration management

### 💾 **Database Management**
- **Professional Architecture**: Consolidated database layer with single source of truth
- **Prisma ORM**: Type-safe database operations with PostgreSQL 15+
- **Automated Backups**: Scheduled database backups with retention
- **Export/Import**: Multiple formats (SQL, CSV, JSON)
- **Data Validation**: Comprehensive input validation with Zod v3 schema validation
- **Connection Pooling**: Optimized database performance
- **Migration Support**: Schema versioning and updates

### 🎨 **Modern UI/UX**
- **Responsive Design**: Mobile-first approach with adaptive layouts
- **Dark/Light Theme**: System preference detection with manual toggle
- **Accessibility**: ARIA labels, keyboard navigation, screen reader support
- **Modern Aesthetics**: Card-based layouts with harmonious color schemes
- **Interactive Components**: Smooth animations and user feedback

### 🌐 **Census Portal Features**
- **Public Access**: Secure entry point with unique codes
- **Form Management**: Dynamic, multi-step census forms
- **Data Validation**: Real-time validation with error handling
- **Progress Tracking**: Save and continue functionality
- **Account Management**: Participant data review and updates

## 🏗️ Architecture

The WSCCC Census System follows a modern, enterprise-grade architecture with complete separation of concerns:

### 🔐 **Dual Authentication Architecture**
```mermaid
graph TB
    subgraph "Admin Portal (/admin/*)"
        A1[Admin Login] --> A2[NextAuth Admin Handler]
        A2 --> A3[admin-session-token]
        A3 --> A4[Admin Dashboard]
    end

    subgraph "Census Portal (/)"
        C1[Unique Code Entry] --> C2[NextAuth Census Handler]
        C2 --> C3[census-session-token]
        C3 --> C4[Census Form]
    end

    A2 -.->|NEXTAUTH_SECRET_ADMIN| A3
    C2 -.->|NEXTAUTH_SECRET_CENSUS| C3
```

### 📊 **Data Flow Architecture**
- **Client Layer**: React components with shadcn/ui
- **API Layer**: Next.js API routes with validation
- **Service Layer**: Business logic and data processing
- **Database Layer**: PostgreSQL with Prisma ORM

### 🛡️ **Security Architecture**
- **Rate Limiting**: Session-based protection with progressive timeouts
- **CSRF Protection**: HTTP-only cookies with secure flags
- **Input Validation**: Comprehensive Zod schema validation
- **Audit Logging**: Complete action tracking for compliance

## 🛠️ Technology Stack

### **Frontend Technologies**
- **[Next.js 15.3.1](https://nextjs.org/)** - React framework with App Router and Server Components
- **[React 19.0.0](https://reactjs.org/)** - UI library with latest concurrent features
- **[TypeScript 5](https://www.typescriptlang.org/)** - Type-safe development with strict mode
- **[Tailwind CSS 4](https://tailwindcss.com/)** - Utility-first CSS framework with modern features
- **[shadcn/ui](https://ui.shadcn.com/)** - Professional component library with accessibility

### **Backend & Database**
- **[PostgreSQL 15+](https://www.postgresql.org/)** - Advanced relational database with JSON support
- **[Prisma ORM](https://www.prisma.io/)** - Type-safe database toolkit with migration support
- **[NextAuth.js 4.24.11](https://next-auth.js.org/)** - Authentication framework with dual system support
- **[Zod v3.25.56](https://zod.dev/)** - Runtime type validation and schema parsing
- **[Argon2](https://www.npmjs.com/package/@node-rs/argon2)** - Modern password hashing (OWASP 2025 recommendation)

### **AI & Analytics**
- **[Vercel AI SDK](https://sdk.vercel.ai/)** - Professional AI integration with streaming responses
- **[Google Gemini 2.5 Flash](https://ai.google.dev/)** - Advanced language model for analytics chatbot
- **[Chart.js](https://www.chartjs.org/)** - Flexible charting library for complex visualisations
- **[Recharts](https://recharts.org/)** - React-native charting components

### **Development & Deployment**
- **[React Hook Form](https://react-hook-form.com/)** - Performant form management with validation
- **[QR Code Styling](https://www.npmjs.com/package/qr-code-styling)** - Customisable QR code generation
- **[Sonner](https://sonner.emilkowal.ski/)** - Modern toast notification system
- **[date-fns](https://date-fns.org/)** - Comprehensive date utility library

## 🚀 Getting Started

### Prerequisites
- **Node.js 18+** and npm/yarn/pnpm/bun
- **PostgreSQL 15+** database server
- **Git** for version control

### Installation

1. **Clone the repository:**
   ```bash
   git clone https://github.com/Ayuyyae/wsccc-census-system.git
   cd wsccc-census-system
   ```

2. **Install dependencies:**
   ```bash
   npm install
   # or yarn install / pnpm install / bun install
   ```

3. **Database Setup:**
   ```bash
   # Create PostgreSQL database
   createdb wsccc_census_db_pg

   # Import the PostgreSQL schema
   psql -d wsccc_census_db_pg -f new_server/database-postgresql.sql

   # Initialise Prisma
   npm run db:generate
   npm run db:push
   ```

4. **Environment Configuration:**
   Create a `.env.local` file in the project root:
   ```bash
   cp .env.example .env.local
   ```

   **Required Environment Variables:**
   ```dotenv
   # Database Configuration (PostgreSQL with Prisma)
   DATABASE_URL="postgresql://username:password@localhost:5432/wsccc_census_db_pg?schema=public"

   # Authentication Secrets (TWO INDEPENDENT SYSTEMS)
   NEXTAUTH_URL=http://localhost:3000
   NEXTAUTH_SECRET_ADMIN=your_admin_secret_key_here_32_chars_minimum     # Admin portal
   NEXTAUTH_SECRET_CENSUS=your_census_secret_key_here_32_chars_minimum   # Census participants

   # AI Analytics (Vercel AI SDK with Google Gemini 2.5 Flash)
   GOOGLE_GEMINI_API_KEY=your_google_gemini_api_key_here
   ```

   **Optional Environment Variables:**
   ```dotenv
   # Database Connection Pooling (for hosting providers like Vercel)
   POSTGRES_URL_NON_POOLING="postgresql://username:password@localhost:5432/wsccc_census_db_pg?schema=public"

   # Backup Configuration
   BACKUP_DIR=/path/to/your/backup/directory  # Defaults to ./backups
   ```

   **Generate secure secrets:**
   ```bash
   # Method 1: Using Vercel's official NextAuth secret generator (recommended)
   # Visit: https://generate-secret.vercel.app/32
   # This generates cryptographically secure 32-byte secrets specifically for NextAuth

   # Method 2: Using OpenSSL (run this 2 times for each secret)
   openssl rand -base64 32

   # Method 3: Using Node.js
   node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"

   # Example of properly configured secrets in .env.local:
   NEXTAUTH_SECRET_ADMIN=AbCdEf1234567890AbCdEf1234567890AbCdEf12
   NEXTAUTH_SECRET_CENSUS=XyZ9876543210XyZ9876543210XyZ9876543210XyZ
   ```

5. **Start Development Server:**
   ```bash
   npm run dev
   ```

   Open [http://localhost:3000](http://localhost:3000) to view the application.

### 🔑 Initial Setup

1. **Admin Account Creation:**
   - Navigate to `/admin/login`
   - Use the initial setup process to create your first admin account
   - Enable 2FA for enhanced security (recommended)

2. **System Configuration:**
   - Access Admin Settings to configure church information
   - Set up census year and scheduling preferences
   - Configure rate limiting and security settings

3. **Census Preparation:**
   - Generate unique codes for households
   - Print QR code cards for distribution to parishioners
   - Open census portal for participant access

### 🎯 Admin Account Setup

The system requires initial admin account creation during setup:
- Use the database setup script to create your first admin account
- Configure admin credentials during the installation process
- Follow the setup wizard for secure account creation

> ⚠️ **Security Note**: Always use strong, unique credentials and enable 2FA for production environments.

## 📋 Available Scripts

### Development Scripts
```bash
npm run dev          # Start development server on http://localhost:3000
npm run build        # Build for production (includes Prisma client generation)
npm run start        # Start production server
npm run lint         # Run ESLint for code quality checks
```

### Database Management Scripts (Prisma)
```bash
npm run db:generate  # Generate Prisma client from schema
npm run db:push      # Push schema changes to database (development)
npm run db:migrate   # Run database migrations (production-safe)
npm run db:studio    # Open Prisma Studio (database GUI)
npm run db:backup    # Create PostgreSQL database backup
```

### Environment-Aware Database Commands
All database commands use the `.env.local` file automatically via the `dotenv` package:
- Commands read `DATABASE_URL` from `.env.local`
- No need to manually specify connection strings
- Supports both development and production environments

## 💻 Usage

### Admin Portal Features

#### 🏠 **Household Management**
- Create and manage household records
- Track family relationships and demographics
- Assign unique codes for census participation
- Monitor form completion status

#### 👥 **Member Management**
- Add, edit, and remove household members
- Track personal information and relationships
- Manage sacrament records (Baptism, Confirmation, Marriage, etc.)
- Generate member reports and statistics

#### 🎫 **Unique Code System**
- Generate secure, single-use codes for households
- Print QR code cards for easy distribution
- Track code usage and assignment status
- Manage code expiration and renewal

#### 📊 **Analytics Dashboard**
- Real-time census completion statistics
- Interactive charts and visualisations
- AI-powered chatbot for data insights
- Export capabilities in multiple formats

#### ⚙️ **System Settings**
- Configure church information and branding
- Manage census year settings and scheduling
- Control census portal availability
- Configure security and rate limiting settings

### Census Portal Features

#### 🔐 **Secure Access**
- Unique code authentication with QR support
- Session-based security with automatic timeout
- Rate limiting protection against abuse

#### 📝 **Census Form Completion**
- Multi-step form with save/continue functionality
- Real-time validation and error handling
- Automatic data synchronisation
- Progress tracking and completion status

#### 👨‍👩‍👧‍👦 **Household Management**
- Add and edit household members
- Manage family relationships
- Track sacrament information
- Review and update personal details

## 🏗️ Project Structure

```
wsccc-census-system/
├── 📁 app/                          # Next.js 15 App Router
│   ├── 🔐 admin/                   # Admin Portal Pages
│   │   ├── dashboard/              # Admin dashboard with analytics
│   │   ├── household/              # Household management
│   │   ├── members/                # Member management
│   │   ├── unique-code/            # Code generation & QR printing
│   │   ├── analytics/              # AI chatbot & reporting
│   │   ├── settings/               # System configuration
│   │   └── login/                  # Admin authentication
│   ├── 🌐 api/                     # API Routes
│   │   ├── admin/                  # Admin-only endpoints
│   │   │   ├── analytics/chat/     # AI chatbot endpoint
│   │   │   ├── export/             # Data export APIs
│   │   │   └── settings/           # Configuration APIs
│   │   ├── census/                 # Census participant endpoints
│   │   │   ├── auth/               # Census authentication
│   │   │   ├── form/               # Form submission
│   │   │   └── members/            # Member management
│   │   └── auth/                   # Admin authentication
│   ├── 📝 census/                  # Census Portal Pages
│   │   └── [code]/                 # Dynamic code-based routing
│   ├── 📄 privacy-policy/          # Privacy policy page
│   ├── 📄 terms/                   # Terms of service
│   └── 🎨 globals.css              # Global Tailwind styles
├── 📁 src/                          # Source Code
│   ├── 🧩 components/              # React Components
│   │   ├── ui/                     # shadcn/ui base components
│   │   ├── admin/                  # Admin portal components
│   │   ├── census/                 # Census form components
│   │   ├── auth/                   # Authentication components
│   │   ├── charts/                 # Chart components
│   │   └── shared/                 # Shared components
│   ├── 📚 lib/                     # Core Libraries
│   │   ├── 🗄️ db/                  # Database Layer (Single Source of Truth)
│   │   │   ├── prisma.ts           # Prisma client & connection
│   │   │   ├── households.ts       # Household operations
│   │   │   ├── members.ts          # Member operations
│   │   │   ├── census-years.ts     # Census year management
│   │   │   ├── unique-codes.ts     # Code generation & validation
│   │   │   └── export-prisma.ts    # Data export utilities
│   │   ├── 🔐 auth/                # Authentication Systems
│   │   │   ├── auth-options.ts     # Admin NextAuth config
│   │   │   ├── census-auth/        # Census authentication
│   │   │   └── rate-limiting/      # Security & rate limiting
│   │   ├── 🤖 ai/                  # AI Integration
│   │   │   └── analytics-chat.ts   # Gemini AI chatbot
│   │   ├── 📊 utils/               # Utility functions
│   │   └── ✅ validation/          # Zod schemas
│   ├── 🎣 hooks/                   # Custom React hooks
│   ├── 🔧 types/                   # TypeScript definitions
│   ├── 🌐 contexts/                # React contexts
│   └── 🛡️ middleware/              # Next.js middleware
├── 📁 components/                   # Additional Components
│   └── admin/analytics/charts/     # Advanced chart registry
├── 🗄️ prisma/                      # Database Configuration
│   └── schema.prisma               # PostgreSQL schema
├── 🚀 new_server/                  # Deployment Resources
│   ├── database-postgresql.sql     # Database schema
│   ├── setup-new-server.mjs        # Automated setup script
│   └── readme for new deployment first.txt
├── 📖 guide/                       # Comprehensive Documentation
│   ├── README.md                   # Documentation overview
│   ├── ARCHITECTURE.md             # System architecture guide
│   ├── SECURITY.md                 # Security implementation
│   ├── UI-GUIDELINES.md            # Design system guidelines
│   ├── DATABASE.md                 # Database management
│   ├── API.md                      # API documentation
│   ├── TROUBLESHOOTING.md          # Issue resolution
│   ├── DEPLOYMENT.md               # Production deployment
│   └── AI-ANALYTICS.md             # AI chatbot guide
├── 🔧 scripts/                     # Utility Scripts
│   └── backup-database.mjs         # Database backup
├── ⚙️ Configuration Files
│   ├── .env.example                # Environment template
│   ├── package.json                # Dependencies & scripts
│   ├── tsconfig.json               # TypeScript config
│   ├── tailwind.config.ts          # Tailwind CSS config
│   ├── next.config.mjs             # Next.js configuration
│   └── eslint.config.mjs           # ESLint configuration
└── 📄 README.md                    # This file
```

### 🎯 **Key Architecture Principles**

#### **Single Source of Truth Database Layer**
```typescript
// 🎯 Centralised database operations
src/lib/db/
├── households.ts        ← All household CRUD operations
├── members.ts           ← All member CRUD operations
├── census-years.ts      ← All census year operations
└── unique-codes.ts      ← All code generation & validation

// ✅ Clean import patterns
import { getHouseholds, createHousehold } from '@/lib/db/households';
import { getMembers, updateMember } from '@/lib/db/members';
```

#### **Dual Authentication Architecture**
- **Admin System**: `/api/auth/[...nextauth]` with `NEXTAUTH_SECRET_ADMIN`
- **Census System**: `/api/census/auth/[...nextauth]` with `NEXTAUTH_SECRET_CENSUS`
- **Complete Isolation**: Independent cookie sessions and middleware

## 🚀 Deployment

### Production Checklist

- [ ] **Environment Variables**: Configure production environment variables
- [ ] **Database**: Set up production PostgreSQL database with Prisma
- [ ] **SSL Certificate**: Enable HTTPS for security
- [ ] **Domain Configuration**: Point domain to hosting server
- [ ] **Backup Strategy**: Configure automated backups
- [ ] **Monitoring**: Set up error tracking and uptime monitoring

### Recommended Hosting Platforms

The system is optimised for deployment on various platforms:

#### **🌟 Recommended Platforms**
- **[Hostuno](https://hostuno.com/)** - Optimised for Next.js projects with Australian servers
- **[Vercel](https://vercel.com/)** - Seamless Next.js integration with global CDN
- **[DigitalOcean](https://www.digitalocean.com/)** - Full control with droplets and managed databases
- **[AWS](https://aws.amazon.com/)** - Enterprise-scale deployment with comprehensive services

#### **🏗️ VPS Deployment**
The system includes comprehensive VPS deployment guides:
- **Oracle Cloud** - Free tier compatible
- **Ubuntu Server** - Complete setup instructions
- **Nginx + PM2** - Production-ready configuration
- **SSL/TLS** - Automated certificate management with Let's Encrypt

### Environment Variables for Production

```dotenv
# Production Database (PostgreSQL with Prisma)
DATABASE_URL="********************************************************/wsccc_census_production?schema=public"

# Optional: Direct database URL for connection pooling (hosting providers)
POSTGRES_URL_NON_POOLING="********************************************************/wsccc_census_production?schema=public"

# Security - TWO INDEPENDENT AUTH SYSTEMS (use strong, unique secrets)
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET_ADMIN=your_admin_auth_production_secret_32_chars_minimum     # Admin portal
NEXTAUTH_SECRET_CENSUS=your_census_auth_production_secret_32_chars_minimum   # Census participants

# AI Analytics (Vercel AI SDK with Google Gemini 2.5 Flash)
GOOGLE_GEMINI_API_KEY=your_production_api_key

# Production Backup Configuration
BACKUP_DIR=/var/backups/wsccc-census

# Node.js Environment (automatically set by hosting providers)
NODE_ENV=production
```

## 🔧 Configuration

### Environment Variables Reference

#### **Database Configuration**
| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `DATABASE_URL` | ✅ | Primary PostgreSQL connection string | `postgresql://user:pass@localhost:5432/db` |
| `POSTGRES_URL_NON_POOLING` | ❌ | Direct connection for hosting providers | Same as DATABASE_URL |

#### **Authentication Configuration**
| Variable | Required | Description | Purpose |
|----------|----------|-------------|---------|
| `NEXTAUTH_URL` | ✅ | Base URL of your application | `http://localhost:3000` |
| `NEXTAUTH_SECRET_ADMIN` | ✅ | Admin portal authentication secret | 32+ character random string |
| `NEXTAUTH_SECRET_CENSUS` | ✅ | Census portal authentication secret | 32+ character random string |

#### **AI Analytics Configuration**
| Variable | Required | Description | How to Get |
|----------|----------|-------------|------------|
| `GOOGLE_GEMINI_API_KEY` | ✅ | Google Gemini API key for AI chatbot | [Get API Key](https://aistudio.google.com/app/apikey) |

#### **Optional Configuration**
| Variable | Required | Description | Default |
|----------|----------|-------------|---------|
| `BACKUP_DIR` | ❌ | Custom backup directory path | `./backups` |
| `NODE_ENV` | ❌ | Node.js environment | Auto-detected |

### Admin Portal Access
- **URL**: `/admin/login`
- **Features**: Dashboard, member management, settings, analytics
- **Security**: 2FA enabled, audit logging

### Census Portal Access
- **URL**: `/` (public entry point)
- **Authentication**: Unique codes with QR support
- **Features**: Form completion, data review, account management

## 📊 Analytics & Reporting

The system provides comprehensive analytics and reporting capabilities:

### 📈 **Dashboard Metrics**
- Real-time census completion statistics
- Household and member count tracking
- Geographic distribution analysis
- Sacrament participation rates
- Progress tracking and completion trends

### 🤖 **AI-Powered Analytics Chatbot**
- Natural language queries for data insights
- Professional streaming responses using Vercel AI SDK
- Integration with Google Gemini 2.5 Flash
- Contextual data analysis and recommendations
- Export query results in multiple formats

### 📊 **Advanced Chart System**
- Hybrid Chart.js and Recharts integration
- Chart virtualisation for large datasets
- Interactive visualisations with drill-down capabilities
- Responsive design for all device sizes
- Export charts as PNG or PDF

### 📤 **Data Export Capabilities**
- **CSV Format**: Excel-compatible with proper encoding
- **JSON Format**: Structured data for API integration
- **SQL Format**: Database backup and migration
- **PDF Reports**: Professional formatted documents
- **Bulk Export**: Complete database export functionality

## 🔌 API Documentation

The system provides comprehensive REST APIs for both admin and census operations:

### 🔐 **Admin APIs** (`/api/admin/*`)
- **Authentication**: Requires admin session token
- **Household Management**: CRUD operations for households
- **Member Management**: Complete member lifecycle management
- **Analytics**: Statistical data and reporting endpoints
- **System Settings**: Configuration and control endpoints

### 🎫 **Census APIs** (`/api/census/*`)
- **Authentication**: Unique code-based authentication
- **Form Submission**: Multi-step form handling
- **Data Validation**: Real-time validation and error handling
- **Session Management**: Secure session handling

### 🤖 **AI Analytics API** (`/api/admin/analytics/chat`)
- **Streaming Responses**: Real-time AI-powered insights
- **Natural Language Processing**: Query census data using plain English
- **Context Awareness**: Maintains conversation context
- **Data Security**: Admin-only access with session validation

## 🛡️ Security Features

### **Two Independent Authentication Systems**
The system implements **complete authentication isolation** to ensure security and prevent conflicts:

#### **1. Admin Portal Authentication**
- **Secret Key**: `NEXTAUTH_SECRET_ADMIN`
- **Cookie Names**: `admin-session-token`, `admin-callback-url`, `admin-csrf-token`
- **Features**: Username/password + optional 2FA (TOTP)
- **Protection**: 2FA protection (no rate limiting needed)
- **Session Management**: JWT with HTTP-only cookies

#### **2. Census Portal Authentication**
- **Secret Key**: `NEXTAUTH_SECRET_CENSUS`
- **Cookie Names**: `census-session-token`, `census-callback-url`, `census-csrf-token`
- **Features**: Unique code-based authentication with QR support
- **Protection**: IP-based rate limiting with progressive timeouts
- **Session Management**: Separate JWT tokens with custom cookie names



### **Additional Security Measures**
- **Rate Limiting**: Progressive timeout increases (5 attempts → 15min block, then +15min increments)
- **Content Security Policy**: Comprehensive CSP headers with Google Gemini API allowlist
- **SQL Injection Protection**: Parameterized queries with Prisma ORM throughout
- **XSS Protection**: Input sanitization and validation with Zod schemas
- **Session Security**: HTTP-only cookies with secure flags in production
- **Environment Validation**: Secure configuration management with runtime checks
- **Audit Logging**: Complete action tracking for compliance and monitoring

## 🏗️ Architecture Highlights

### **Professional Database Layer**
The system features a **consolidated database architecture** with industry-standard patterns:

```typescript
// 🎯 SINGLE SOURCE OF TRUTH PATTERN
src/lib/db/
├── households.ts        ← All household database operations
├── members.ts           ← All member database operations
├── household-members.ts ← All relationship operations
└── census-years.ts      ← All census year operations

// � CLEAN IMPORT PATTERNS
import { getHouseholds, createHousehold } from '@/lib/db/households';
import { getMembers, updateMember } from '@/lib/db/members';
```

**Benefits:**
- ✅ **No Code Duplication**: Eliminated duplicate database functions
- ✅ **Type Safety**: Full TypeScript + Prisma ORM integration
- ✅ **Consistent APIs**: Standardized data structures across all entities
- ✅ **Professional Standards**: Industry-standard layered architecture
- ✅ **Easy Maintenance**: Single source of truth for each entity

### **Migration from MySQL to PostgreSQL**
- **Modern Database**: PostgreSQL 15+ with advanced features
- **Prisma ORM**: Type-safe database operations
- **Performance**: Optimized queries with proper indexing
- **Scalability**: Enterprise-ready database architecture

## 🔧 Troubleshooting

### Environment Configuration Issues

#### **Database Connection Errors**
```bash
# Error: DATABASE_URL environment variable is required
# Solution: Ensure DATABASE_URL is set in .env.local
DATABASE_URL="postgresql://username:password@localhost:5432/wsccc_census_db_pg?schema=public"

# Error: Connection refused
# Solution: Ensure PostgreSQL is running and accessible
sudo systemctl start postgresql  # Linux
brew services start postgresql   # macOS
```

#### **Authentication Errors**
```bash
# Error: NEXTAUTH_SECRET_ADMIN is not defined
# Solution: Generate and set both required authentication secrets

# Recommended: Use Vercel's official NextAuth secret generator
# Visit: https://generate-secret.vercel.app/32

# Alternative: Generate with OpenSSL
openssl rand -base64 32  # Run 2 times for each secret
```

#### **AI Chatbot Errors**
```bash
# Error: Google Gemini API key not configured
# Solution: Get API key from Google AI Studio
# Visit: https://aistudio.google.com/app/apikey
GOOGLE_GEMINI_API_KEY=your_api_key_here
```

#### **Backup Script Errors**
```bash
# Error: pg_dump command not found
# Solution: Install PostgreSQL client tools
sudo apt-get install postgresql-client  # Ubuntu/Debian
brew install postgresql                 # macOS
```

### Common Setup Issues

1. **Missing .env.local file**: Copy from .env.example and update values
2. **Database not created**: Run `createdb wsccc_census_db_pg` before setup
3. **Prisma client not generated**: Run `npm run db:generate`
4. **Port 3000 in use**: Change port with `npm run dev -- -p 3001`

## 📚 Documentation

### 📖 **Comprehensive Documentation Suite**

The WSCCC Census System includes extensive documentation organized into focused guides:

#### **Core Documentation** (`/guide/`)
- **[📋 Documentation Overview](guide/README.md)** - Navigation and quick start guide
- **[🏗️ System Architecture](guide/ARCHITECTURE.md)** - Complete system architecture and design principles
- **[🛡️ Security Guide](guide/SECURITY.md)** - Security implementation and best practices
- **[🎨 UI Guidelines](guide/UI-GUIDELINES.md)** - Design system and component guidelines
- **[🗄️ Database Guide](guide/DATABASE.md)** - Database schema, management, and optimization
- **[🔌 API Documentation](guide/API.md)** - Complete API reference and endpoints
- **[🔧 Troubleshooting](guide/TROUBLESHOOTING.md)** - Common issues and solutions
- **[🚀 Deployment Guide](guide/DEPLOYMENT.md)** - Production deployment procedures
- **[🤖 AI Analytics](guide/AI-ANALYTICS.md)** - AI chatbot setup and usage

#### **Technical Resources**
- **[Database Schema](new_server/database-postgresql.sql)** - Complete PostgreSQL database structure
- **[Server Setup Script](new_server/setup-new-server.mjs)** - Automated deployment script
- **[Backup Utilities](scripts/backup-database.mjs)** - Database backup and maintenance

#### **Quick Start Guides**
- **For Developers**: Start with [Architecture](guide/ARCHITECTURE.md) → [Security](guide/SECURITY.md) → [UI Guidelines](guide/UI-GUIDELINES.md)
- **For Administrators**: Begin with [Deployment](guide/DEPLOYMENT.md) → [Troubleshooting](guide/TROUBLESHOOTING.md)
- **For End Users**: Review [AI Analytics](guide/AI-ANALYTICS.md) for chatbot usage

#### **Documentation Features**
- **📋 Organized Structure**: Focused guides for specific domains (architecture, security, deployment)
- **🔄 Up-to-Date**: Modernized content reflecting PostgreSQL migration and latest features
- **🎯 Role-Based**: Tailored content for developers, administrators, and end users
- **🔍 Searchable**: Easy navigation with cross-references and clear structure
- **📖 Comprehensive**: Complete coverage from setup to advanced troubleshooting
- **🛡️ Security-Focused**: Proper handling of sensitive information and best practices

## 🤝 Contributing

We welcome contributions to improve the WSCCC Census System! Here's how you can help:

### 🚀 **Getting Started**
1. Fork the repository on GitHub
2. Clone your fork locally: `git clone https://github.com/your-username/wsccc-census-system.git`
3. Create a feature branch: `git checkout -b feature/amazing-feature`
4. Make your changes and test thoroughly
5. Commit with descriptive messages: `git commit -m 'Add amazing feature'`
6. Push to your branch: `git push origin feature/amazing-feature`
7. Open a Pull Request with detailed description

### 📋 **Contribution Guidelines**
- Follow the existing code style and conventions
- Write comprehensive tests for new features
- Update documentation for any API changes
- Ensure all tests pass before submitting
- Use Australian English in documentation and comments

### 🐛 **Bug Reports**
- Use the GitHub issue tracker
- Include detailed reproduction steps
- Provide system information and error logs
- Check existing issues before creating new ones

### 💡 **Feature Requests**
- Describe the feature and its benefits
- Explain the use case and requirements
- Consider backward compatibility
- Discuss implementation approach

## 📄 License

This project is licensed under the **MIT License**.

### MIT License Summary
- ✅ **Commercial Use** - Use in commercial projects
- ✅ **Modification** - Modify and adapt the code
- ✅ **Distribution** - Distribute original or modified versions
- ✅ **Private Use** - Use for private projects
- ❌ **Liability** - No warranty or liability
- ❌ **Trademark Use** - No trademark rights granted

See the [LICENSE](LICENSE) file for complete terms and conditions.

## 🆘 Support & Community

### 📞 **Getting Help**
- **Documentation**: Start with the [Documentation Overview](guide/README.md)
- **Architecture**: Review [System Architecture](guide/ARCHITECTURE.md) for technical details
- **Troubleshooting**: Check [Troubleshooting Guide](guide/TROUBLESHOOTING.md) for common issues
- **Security**: Reference [Security Guide](guide/SECURITY.md) for security implementations
- **Deployment**: Follow [Deployment Guide](guide/DEPLOYMENT.md) for production setup
- **Issues**: Check existing [GitHub Issues](https://github.com/Ayuyyae/wsccc-census-system/issues)
- **Discussions**: Join community discussions for questions and ideas
- **Bug Reports**: Create detailed issue reports with reproduction steps

### 🔧 **Professional Support**
For enterprise deployments or custom development:
- System architecture consultation
- Custom feature development
- Performance optimisation
- Security auditing and compliance
- Training and documentation

### 🌟 **Community**
- Star the repository if you find it useful
- Share your deployment experiences
- Contribute improvements and bug fixes
- Help other users in discussions

---

<div align="center">

**🏛️ Built with dedication for the Catholic community**

*Empowering parishes with modern technology for better community management*

[![GitHub Stars](https://img.shields.io/github/stars/Ayuyyae/wsccc-census-system?style=social)](https://github.com/Ayuyyae/wsccc-census-system)
[![GitHub Forks](https://img.shields.io/github/forks/Ayuyyae/wsccc-census-system?style=social)](https://github.com/Ayuyyae/wsccc-census-system/fork)

</div>
