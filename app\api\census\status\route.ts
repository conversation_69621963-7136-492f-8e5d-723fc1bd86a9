import { NextResponse } from "next/server";
import { getTranslations } from "next-intl/server";
import { isCensusOpen } from "@/lib/census/census-availability";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";

/**
 * API route to check if the census is currently open
 * This is used by the homepage to determine whether to show the census form
 * SECURITY: Returns minimal information to prevent data leakage
 */

// Force dynamic rendering to prevent caching of census status
export const dynamic = "force-dynamic";

export async function GET() {
	// Get locale from centralized utility at the beginning
	const locale = await getLocaleFromCookies();
	const _t = await getTranslations({ locale, namespace: "errors" });

	try {
		// Check if census is open
		const censusStatus = await isCensusOpen();

		// Return ONLY the essential status information
		// Do not expose internal details like manual override or scheduled state
		return NextResponse.json({
			isOpen: censusStatus.isOpen,
		});
	} catch (_error) {
		// SECURITY: Do not log sensitive error details
		if (process.env.NODE_ENV === "development") {
		}

		// Return a safe default response
		return NextResponse.json(
			{
				isOpen: false,
			},
			{ status: 500 },
		);
	}
}
