import crypto from "node:crypto";
import { type NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { authOptions } from "@/lib/auth/auth-options";
import { prisma } from "@/lib/db/prisma";
import { getAdminById, setTwoFactorBackupCodes } from "@/lib/db/users";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";

// Function to generate backup codes
function generateBackupCodes(count = 10): string[] {
	const codes = [];
	for (let i = 0; i < count; i++) {
		// Generate a random 6-character alphanumeric code
		const code = crypto.randomBytes(3).toString("hex").toUpperCase();
		codes.push(code);
	}
	return codes;
}

// POST endpoint to generate new backup codes
export async function POST(_request: NextRequest) {
	try {
		// Check authentication
		const session = await getServerSession(authOptions);

		// Require authentication for generating backup codes
		if (!session || session.user.role !== "admin") {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Get admin ID from session
		const adminId = Number.parseInt(session.user.id, 10);

		// Get admin from database
		const admin = await getAdminById(adminId);

		if (!admin) {
			return NextResponse.json({ error: "Admin not found" }, { status: 404 });
		}

		// Check if 2FA is enabled
		if (!admin.twoFactorEnabled) {
			return NextResponse.json(
				{ error: "Two-factor authentication not enabled" },
				{ status: 400 },
			);
		}

		// Generate new backup codes
		const backupCodes = generateBackupCodes();

		// Save the backup codes
		await setTwoFactorBackupCodes(adminId, backupCodes);

		// Log the action
		try {
			await prisma.auditLog.create({
				data: {
					userType: "admin",
					userId: adminId,
					action: "regenerate-backup-codes",
					entityType: "admins",
					entityId: adminId,
					newValues: JSON.stringify({ backup_codes_regenerated: true }),
				},
			});
		} catch (_logError) {}

		// Return the new backup codes
		const locale = await getLocaleFromCookies();
		const t = await getTranslations({ locale, namespace: "admin" });
		return NextResponse.json({
			success: true,
			message: t("newBackupCodesGenerated"),
			backupCodes,
		});
	} catch (_error) {
		return NextResponse.json(
			{ error: "Failed to generate backup codes" },
			{ status: 500 },
		);
	}
}
