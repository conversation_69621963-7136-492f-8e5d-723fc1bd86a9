"use client";

import {
  Building,
  ChevronDown,
  ClipboardList,
  FileQuestion,
  Mail,
  MapPin,
  Phone,
  ShieldCheck,
} from "lucide-react";
import { useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { SessionAwareNavigation } from "@/components/navigation/session-aware-navigation";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface ChurchInfo {
  churchName: string;
  email: string;
  contactNumber: string;
  address: string;
}

interface HelpPageClientProps {
  churchInfo: ChurchInfo;
}

// Help category dropdown for mobile view
function HelpCategoryDropdown({
  value,
  onChange,
}: {
  value: string;
  onChange: (value: string) => void;
}) {
  const t = useTranslations("help");

  // Categories with their icons and labels
  const categories = [
    { value: "faq", label: t("faq"), icon: FileQuestion },
    {
      value: "getting-started",
      label: t("gettingStarted"),
      icon: ClipboardList,
    },
    { value: "privacy", label: t("privacySecurity"), icon: ShieldCheck },
  ];

  // Find the current category object
  const currentCategory = categories.find((cat) => cat.value === value) || categories[0];

  // Handle selection from dropdown
  const handleCategorySelect = (newValue: string) => {
    onChange(newValue);
  };

  // Create a ref for the dropdown button to measure its width
  const buttonRef = React.useRef<HTMLButtonElement>(null);
  const [buttonWidth, setButtonWidth] = useState(0);

  // Update button width on mount and window resize
  useEffect(() => {
    const updateWidth = () => {
      if (buttonRef.current) {
        setButtonWidth(buttonRef.current.offsetWidth);
      }
    };

    // Initial measurement
    updateWidth();

    // Update on resize
    window.addEventListener("resize", updateWidth);
    return () => window.removeEventListener("resize", updateWidth);
  }, []);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button className="w-full cursor-pointer justify-between" ref={buttonRef} variant="outline">
          <div className="flex items-center">
            <currentCategory.icon className="mr-2 h-4 w-4" />
            <span>{currentCategory.label}</span>
          </div>
          <ChevronDown className="ml-2 h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" sideOffset={4} style={{ width: `${buttonWidth}px` }}>
        {categories.map((category) => (
          <DropdownMenuItem
            className="cursor-pointer"
            key={category.value}
            onClick={() => handleCategorySelect(category.value)}
          >
            <category.icon className="mr-2 h-4 w-4" />
            <span>{category.label}</span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Contact Information Component
function ContactCard({
  churchInfo,
  hideHeader = false,
}: {
  churchInfo: ChurchInfo;
  hideHeader?: boolean;
}) {
  const t = useTranslations("help");
  return (
    <div className="fade-in-50 w-full animate-in duration-300">
      {/* Divider and Header - Can be hidden for side column display */}
      {!hideHeader && (
        <div className="mb-6">
          <Separator className="my-6" />
          <div className="mb-4 flex items-center gap-2">
            <Building className="h-5 w-5 text-primary" />
            <h3 className="font-semibold text-xl leading-none tracking-tight">
              {t("contactInformation")}
            </h3>
          </div>
        </div>
      )}

      {/* When header is hidden (for side column), show a different header */}
      {hideHeader && (
        <div className="mb-6">
          <div className="mb-4 flex items-center gap-2">
            <Building className="h-5 w-5 text-primary" />
            <h3 className="font-semibold text-xl leading-none tracking-tight">
              {t("contactInformation")}
            </h3>
          </div>
          <p className="text-muted-foreground text-sm">{t("needHelpContact")}</p>
        </div>
      )}

      {/* Contact details */}
      <div className={`grid grid-cols-1 ${hideHeader ? "" : "md:grid-cols-2"} gap-6`}>
        {/* Left Column */}
        <div className="space-y-4">
          {churchInfo.churchName && (
            <div className="group flex items-start space-x-4 rounded-md p-2 transition-all hover:bg-accent">
              <div className="flex h-9 w-9 shrink-0 items-center justify-center rounded-md border bg-background shadow-sm">
                <Building className="h-5 w-5 text-foreground" />
              </div>
              <div className="space-y-1">
                <p className="font-medium text-sm leading-none">{t("churchName")}</p>
                <p className="text-muted-foreground text-sm">{churchInfo.churchName}</p>
              </div>
            </div>
          )}

          {churchInfo.email && (
            <div className="group flex items-start space-x-4 rounded-md p-2 transition-all hover:bg-accent">
              <div className="flex h-9 w-9 shrink-0 items-center justify-center rounded-md border bg-background shadow-sm">
                <Mail className="h-5 w-5 text-foreground" />
              </div>
              <div className="space-y-1">
                <p className="font-medium text-sm leading-none">{t("email")}</p>
                <a
                  className="text-primary text-sm hover:underline"
                  href={`mailto:${churchInfo.email}`}
                >
                  {churchInfo.email}
                </a>
              </div>
            </div>
          )}
        </div>

        {/* Right Column */}
        <div className="space-y-4">
          {churchInfo.contactNumber && (
            <div className="group flex items-start space-x-4 rounded-md p-2 transition-all hover:bg-accent">
              <div className="flex h-9 w-9 shrink-0 items-center justify-center rounded-md border bg-background shadow-sm">
                <Phone className="h-5 w-5 text-foreground" />
              </div>
              <div className="space-y-1">
                <p className="font-medium text-sm leading-none">{t("phone")}</p>
                <a
                  className="text-primary text-sm hover:underline"
                  href={`tel:${churchInfo.contactNumber}`}
                >
                  {churchInfo.contactNumber.replace(/(\d{4})(\d{3})(\d{3})/, "$1 $2 $3")}
                </a>
              </div>
            </div>
          )}

          {churchInfo.address && (
            <div className="group flex items-start space-x-4 rounded-md p-2 transition-all hover:bg-accent">
              <div className="flex h-9 w-9 shrink-0 items-center justify-center rounded-md border bg-background shadow-sm">
                <MapPin className="h-5 w-5 text-foreground" />
              </div>
              <div className="space-y-1">
                <p className="font-medium text-sm leading-none">{t("address")}</p>
                <p className="text-muted-foreground text-sm">{churchInfo.address}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export function HelpPageClient({ churchInfo }: HelpPageClientProps) {
  const t = useTranslations("help");

  // State to control the active tab
  const [activeTab, setActiveTab] = useState("faq");

  // FAQ data organised by category
  const faqCategories = {
    general: [
      {
        id: "submit-census",
        question: t("faqGeneralSubmitCensusQuestion"),
        answer: t("faqGeneralSubmitCensusAnswer"),
      },
      {
        id: "lost-code",
        question: t("faqGeneralLostCodeQuestion"),
        answer: t("faqGeneralLostCodeAnswer"),
      },
      {
        id: "census-period",
        question: t("faqGeneralCensusPeriodQuestion"),
        answer: t("faqGeneralCensusPeriodAnswer"),
      },
    ],
    process: [
      {
        id: "census-purpose",
        question: t("faqProcessCensusPurposeQuestion"),
        answer: t("faqProcessCensusPurposeAnswer"),
      },
      {
        id: "required-info",
        question: t("faqProcessRequiredInfoQuestion"),
        answer: t("faqProcessRequiredInfoAnswer"),
      },
      {
        id: "time-to-complete",
        question: t("faqProcessTimeToCompleteQuestion"),
        answer: t("faqProcessTimeToCompleteAnswer"),
      },
    ],
    privacy: [
      {
        id: "data-protection",
        question: t("faqPrivacyDataProtectionQuestion"),
        answer: t("faqPrivacyDataProtectionAnswer"),
      },
      {
        id: "data-usage",
        question: t("faqPrivacyDataUsageQuestion"),
        answer: t("faqPrivacyDataUsageAnswer"),
      },
      {
        id: "data-access",
        question: t("faqPrivacyDataAccessQuestion"),
        answer: t("faqPrivacyDataAccessAnswer"),
      },
    ],
  };

  return (
    <>
      <SessionAwareNavigation />
      <div className="container mx-auto max-w-6xl px-4 py-8">
        {/* Hero Section - without question mark icon and back button */}
        <div className="mb-10 text-center">
          <h1 className="mb-4 font-bold text-4xl tracking-tight">{t("helpAndFaq")}</h1>
          <div className="mx-auto mb-6 max-w-xl">
            <p className="mb-1 text-base italic">
              &quot;And whatever you do, do it all in the name of the Lord Jesus.&quot;
            </p>
            <p className="text-right text-muted-foreground text-sm">— Colossians 3:17</p>
          </div>
        </div>

        <div className="mx-auto max-w-5xl space-y-8">
          {/* Two-column layout for large screens */}
          <div className="grid grid-cols-1 items-start gap-8 lg:grid-cols-12">
            {/* Left column: Tabs/Dropdown - Takes 8/12 columns on large screens */}
            <div className="lg:col-span-8">
              {/* Responsive tabs/dropdown for different help categories */}
              <Tabs className="w-full" onValueChange={setActiveTab} value={activeTab}>
                {/* Desktop Tabs - Hidden on mobile */}
                <div className="hidden md:block">
                  <TabsList className="mb-6 grid grid-cols-3 bg-accent">
                    <TabsTrigger className="cursor-pointer" value="faq">
                      <FileQuestion className="mr-2 h-4 w-4" />
                      <span>{t("faq")}</span>
                    </TabsTrigger>
                    <TabsTrigger className="cursor-pointer" value="getting-started">
                      <ClipboardList className="mr-2 h-4 w-4" />
                      <span>{t("gettingStarted")}</span>
                    </TabsTrigger>
                    <TabsTrigger className="cursor-pointer" value="privacy">
                      <ShieldCheck className="mr-2 h-4 w-4" />
                      <span>{t("privacySecurity")}</span>
                    </TabsTrigger>
                  </TabsList>
                </div>

                {/* Mobile Dropdown - Visible only on mobile */}
                <div className="mb-6 md:hidden">
                  <HelpCategoryDropdown onChange={setActiveTab} value={activeTab} />
                </div>

                {/* FAQ Tab Content */}
                <TabsContent className="space-y-6" value="faq">
                  <div className="rounded-lg bg-background/50 p-6 backdrop-blur-sm">
                    <div className="mb-6">
                      <h2 className="mb-2 font-semibold text-xl">
                        {t("frequentlyAskedQuestions")}
                      </h2>
                      <p className="text-muted-foreground">{t("findAnswersToCommonQuestions")}</p>
                    </div>
                    <div className="space-y-4">
                      <h3 className="mb-4 flex items-center gap-2 font-medium text-lg">
                        <FileQuestion className="h-5 w-5 text-primary" />
                        {t("generalQuestions")}
                      </h3>
                      <Accordion className="w-full" collapsible type="single">
                        {faqCategories.general.map((faq) => (
                          <AccordionItem
                            className="overflow-hidden border-border/50 border-b"
                            key={faq.id}
                            value={faq.id}
                          >
                            <AccordionTrigger className="flex cursor-pointer gap-2 rounded-md px-3 py-4 text-left transition-all hover:bg-muted/30">
                              <span>{faq.question}</span>
                            </AccordionTrigger>
                            <AccordionContent className="slide-in-from-top-2 animate-in px-3 pt-2 pb-4 text-muted-foreground duration-200">
                              {faq.answer}
                            </AccordionContent>
                          </AccordionItem>
                        ))}
                      </Accordion>

                      <Separator className="my-6" />

                      <h3 className="mb-4 flex items-center gap-2 font-medium text-lg">
                        <ClipboardList className="h-5 w-5 text-primary" />
                        {t("censusProcess")}
                      </h3>
                      <Accordion className="w-full" collapsible type="single">
                        {faqCategories.process.map((faq) => (
                          <AccordionItem
                            className="overflow-hidden border-border/50 border-b"
                            key={faq.id}
                            value={faq.id}
                          >
                            <AccordionTrigger className="flex cursor-pointer gap-2 rounded-md px-3 py-4 text-left transition-all hover:bg-muted/30">
                              <span>{faq.question}</span>
                            </AccordionTrigger>
                            <AccordionContent className="slide-in-from-top-2 animate-in px-3 pt-2 pb-4 text-muted-foreground duration-200">
                              {faq.answer}
                            </AccordionContent>
                          </AccordionItem>
                        ))}
                      </Accordion>

                      <Separator className="my-6" />

                      <h3 className="mb-4 flex items-center gap-2 font-medium text-lg">
                        <ShieldCheck className="h-5 w-5 text-primary" />
                        {t("privacyAndData")}
                      </h3>
                      <Accordion className="w-full" collapsible type="single">
                        {faqCategories.privacy.map((faq) => (
                          <AccordionItem
                            className="overflow-hidden border-border/50 border-b"
                            key={faq.id}
                            value={faq.id}
                          >
                            <AccordionTrigger className="flex cursor-pointer gap-2 rounded-md px-3 py-4 text-left transition-all hover:bg-muted/30">
                              <span>{faq.question}</span>
                            </AccordionTrigger>
                            <AccordionContent className="slide-in-from-top-2 animate-in px-3 pt-2 pb-4 text-muted-foreground duration-200">
                              {faq.answer}
                            </AccordionContent>
                          </AccordionItem>
                        ))}
                      </Accordion>
                    </div>
                  </div>
                </TabsContent>

                {/* Getting Started Tab Content */}
                <TabsContent className="space-y-6" value="getting-started">
                  <div className="rounded-lg bg-background/50 p-6 backdrop-blur-sm">
                    <div className="mb-6">
                      <h2 className="mb-2 font-semibold text-xl">{t("gettingStarted")}</h2>
                      <p className="text-muted-foreground">{t("learnHowToUseCensusSystem")}</p>
                    </div>
                    <div className="space-y-6">
                      <div>
                        <h3 className="mb-2 flex items-center gap-2 font-medium text-lg">
                          <ClipboardList className="h-5 w-5 text-primary" />
                          {t("censusOverview")}
                        </h3>
                        <p className="text-muted-foreground">{t("censusOverviewDescription")}</p>
                      </div>

                      <Separator />

                      <div>
                        <h3 className="mb-2 flex items-center gap-2 font-medium text-lg">
                          <ClipboardList className="h-5 w-5 text-primary" />
                          {t("stepByStepGuide")}
                        </h3>
                        <p className="mb-4 text-muted-foreground">
                          {t("stepByStepGuideDescription")}
                        </p>
                        <ol className="list-inside list-decimal space-y-3 text-muted-foreground">
                          <li>{t("step1")}</li>
                          <li>{t("step2")}</li>
                          <li>{t("step3")}</li>
                          <li>{t("step4")}</li>
                          <li>{t("step5")}</li>
                          <li>{t("step6")}</li>
                        </ol>
                      </div>

                      <Separator />

                      <div>
                        <h3 className="mb-2 font-medium text-lg">{t("tipsForCompletion")}</h3>
                        <ul className="space-y-2 text-muted-foreground">
                          <li>• {t("tip1")}</li>
                          <li>• {t("tip2")}</li>
                          <li>• {t("tip3")}</li>
                          <li>• {t("tip4")}</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                {/* Privacy & Security Tab Content */}
                <TabsContent className="space-y-6" value="privacy">
                  <div className="rounded-lg bg-background/50 p-6 backdrop-blur-sm">
                    <div className="mb-6">
                      <h2 className="mb-2 font-semibold text-xl">{t("privacySecurityTitle")}</h2>
                      <p className="text-muted-foreground">{t("privacySecurityDescription")}</p>
                    </div>
                    <div className="space-y-6">
                      <div>
                        <h3 className="mb-2 flex items-center gap-2 font-medium text-lg">
                          <ShieldCheck className="h-5 w-5 text-primary" />
                          {t("dataProtection")}
                        </h3>
                        <p className="text-muted-foreground">{t("dataProtectionDescription")}</p>
                      </div>

                      <Separator />

                      <div>
                        <h3 className="mb-2 flex items-center gap-2 font-medium text-lg">
                          <ShieldCheck className="h-5 w-5 text-primary" />
                          {t("informationUsage")}
                        </h3>
                        <p className="text-muted-foreground">{t("informationUsageDescription")}</p>
                      </div>

                      <Separator />

                      <div>
                        <h3 className="mb-2 flex items-center gap-2 font-medium text-lg">
                          <ShieldCheck className="h-5 w-5 text-primary" />
                          {t("accessControls")}
                        </h3>
                        <p className="text-muted-foreground">{t("accessControlsDescription")}</p>
                      </div>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </div>

            {/* Right column: Contact Card - Takes 4/12 columns on large screens */}
            <div className="mt-8 lg:col-span-4 lg:mt-0">
              {/* Contact Information - Only visible on large screens */}
              <div className="sticky top-24 hidden lg:block">
                <div className="border-l pl-6">
                  <ContactCard churchInfo={churchInfo} hideHeader={true} />
                </div>
              </div>
            </div>
          </div>

          {/* Contact Information - Only visible on mobile and medium screens */}
          <div className="lg:hidden">
            <ContactCard churchInfo={churchInfo} hideHeader={false} />
          </div>
        </div>
      </div>
    </>
  );
}
