import fs from "fs";
import createNextIntlPlugin from "next-intl/plugin";
import path from "path";

const withNextIntl = createNextIntlPlugin({
  experimental: {
    // Auto-generate TypeScript definitions from the English messages
    createMessagesDeclaration: "./lang/en.json",
  },
});

// Copy QR scanner worker file to public directory
const copyQrScannerWorker = () => {
  try {
    const workerFile = "node_modules/qr-scanner/qr-scanner-worker.min.js";
    const publicDir = path.join(process.cwd(), "public");

    // Ensure public directory exists
    if (!fs.existsSync(publicDir)) {
      fs.mkdirSync(publicDir, { recursive: true });
    }

    const srcPath = path.join(process.cwd(), workerFile);
    const destPath = path.join(publicDir, "qr-scanner-worker.min.js");

    if (fs.existsSync(srcPath) && !fs.existsSync(destPath)) {
      fs.copyFileSync(srcPath, destPath);
    }
  } catch (error) {
    // Silently handle worker copy errors - QR scanner will still work
  }
};

// Copy worker file during build
copyQrScannerWorker();

const nextConfig: import("next").NextConfig = {
  // Your existing config
  images: {
    remotePatterns: [
      {
        protocol: "https" as const,
        hostname: "images.unsplash.com",
        pathname: "/**",
      },
      {
        protocol: "https" as const,
        hostname: "demos.pixinvent.com",
        pathname: "/**",
      },
    ],
  },
  // Security headers
  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "X-Frame-Options",
            value: "DENY",
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          {
            key: "Referrer-Policy",
            value: "strict-origin-when-cross-origin",
          },
          {
            key: "X-XSS-Protection",
            value: "1; mode=block",
          },
          {
            key: "Permissions-Policy",
            value: "camera=*, microphone=(), geolocation=(), display-capture=()",
          },
          {
            key: "Content-Security-Policy",
            value:
              "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob:; worker-src 'self' blob:; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://generativelanguage.googleapis.com; frame-ancestors 'none'; object-src 'none'; base-uri 'self';",
          },
        ],
      },
    ];
  },
  serverExternalPackages: ["@prisma/client"],
};

export default withNextIntl(nextConfig);
