"use client";

import { Bar<PERSON><PERSON>3, <PERSON>, <PERSON><PERSON>, Users } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { AgeDistributionChart } from "@/components/admin/dashboard/age-distribution-chart";
import { GenderDistributionChart } from "@/components/admin/dashboard/gender-distribution-chart";
import { SacramentsDistributionChart } from "@/components/admin/dashboard/sacraments-distribution-chart";
import { Skeleton } from "@/components/ui/skeleton";
import { useMessage } from "@/hooks/useMessage";

interface DashboardStats {
	members: {
		total: number;
		thisYear: number;
		growth: number;
	};
	households: {
		total: number;
		thisYear: number;
		growth: number;
	};
	uniqueCodes: {
		total: number;
		assigned: number;
		available: number;
	};
	census: {
		currentYear: number;
		isOpen: boolean;
		progress: number;
	};
}

export function DashboardClient({ userName }: { userName: string }) {
	const { showError } = useMessage();
	const [stats, setStats] = useState<DashboardStats | null>(null);
	const [loading, setLoading] = useState(true);
	const t = useTranslations("admin");

	// Fetch dashboard data
	useEffect(() => {
		const fetchDashboardData = async () => {
			try {
				setLoading(true);

				// Fetch both stats and demographics simultaneously to prevent double loading
				const [statsResponse] = await Promise.all([
					fetch("/api/admin/dashboard/stats"),
					// Pre-warm the demographics cache
					fetch("/api/admin/dashboard/demographics"),
				]);

				if (statsResponse.ok) {
					const statsData = await statsResponse.json();
					setStats(statsData);
				}
			} catch (error) {
				console.error("Error fetching dashboard data:", error);
				showError("failedToLoadDashboardData");
			} finally {
				setLoading(false);
			}
		};

		fetchDashboardData();
	}, [showError]); // showError is stable due to useCallback memoization

	if (loading) {
		return <DashboardSkeleton />;
	}

	return (
		<div className="space-y-8">
			{/* Clean Welcome Header */}
			<div className="py-4">
				<div className="space-y-2">
					<h1 className="font-light text-3xl text-slate-900 tracking-tight dark:text-slate-50">
						{t("welcomeBack")},{" "}
						<span className="bg-gradient-to-r from-[#FF6308] to-[#97A4FF] bg-clip-text font-medium text-transparent">
							{userName}
						</span>
					</h1>
					<p className="font-normal text-base text-slate-600 dark:text-slate-300">
						{t("dashboardOverview")}
					</p>
				</div>
			</div>

			{/* Key Performance Indicators - 精致的卡片式布局 */}
			<div className="grid gap-6 md:grid-cols-2 xl:grid-cols-4">
				<StatCard
					accentColor="bg-[#3B82F6]"
					change={stats?.members.growth || 0}
					description={`${stats?.members.thisYear || 0} ${t("currentCensus")}`}
					gradient="from-blue-500 to-blue-600"
					icon={<Users className="h-5 w-5" />}
					title={t("totalMembers")}
					value={stats?.members.total || 0}
				/>
				<StatCard
					accentColor="bg-[#10B981]"
					change={stats?.households.growth || 0}
					description={`${stats?.households.thisYear || 0} ${t("currentCensus")}`}
					gradient="from-emerald-500 to-emerald-600"
					icon={<Home className="h-5 w-5" />}
					title={t("households")}
					value={stats?.households.total || 0}
				/>
				<StatCard
					accentColor="bg-[#FF6308]"
					description={`${stats?.uniqueCodes.available || 0} ${t("available")}`}
					gradient="from-[#FF6308] to-orange-500"
					icon={<Scroll className="h-5 w-5" />}
					subValue={stats?.uniqueCodes.total || 0}
					title={t("uniqueCodes")}
					value={stats?.uniqueCodes.assigned || 0}
				/>
				<StatCard
					accentColor="bg-[#97A4FF]"
					description={`${stats?.census.currentYear || new Date().getFullYear()} ${t("census")}`}
					gradient="from-[#97A4FF] to-indigo-500"
					icon={<BarChart3 className="h-5 w-5" />}
					isPercentage
					showProgressBar={false}
					title={t("censusProgress")}
					value={stats?.census.progress || 0}
				/>
			</div>

			{/* Age & Gender Distribution - 2 Column Layout */}
			<div className="grid gap-6 lg:grid-cols-2">
				{/* Age Distribution */}
				<AgeDistributionChart />

				{/* Gender Distribution */}
				<GenderDistributionChart />
			</div>

			{/* Sacraments Distribution */}
			<SacramentsDistributionChart />
		</div>
	);
}

// Stat Card Component
interface StatCardProps {
	title: string;
	value: number;
	subValue?: number;
	change?: number;
	isPercentage?: boolean;
	icon: React.ReactNode;
	description: string;
	status?: "success" | "warning" | "info";
	gradient?: string;
	accentColor?: string;
	showProgressBar?: boolean;
}

function StatCard({
	title,
	value,
	subValue,
	change,
	isPercentage,
	icon,
	description,
	status,
	gradient,
	accentColor,
	showProgressBar = true,
}: StatCardProps) {
	const formatValue = (val: number) => {
		if (isPercentage) {
			return `${val}%`;
		}
		return val.toLocaleString();
	};

	// Extract color value from bg-[#COLOR] format
	const colorValue = accentColor?.replace("bg-", "") || "[#64748B]";

	const getChangeColor = (changeValue?: number) => {
		if (!changeValue) {
			return "text-muted-foreground";
		}
		if (changeValue > 0) {
			return "text-emerald-600";
		}
		if (changeValue < 0) {
			return "text-red-500";
		}
		return "text-muted-foreground";
	};

	const getStatusColor = (statusType?: string) => {
		switch (statusType) {
			case "success":
				return "text-emerald-600";
			case "warning":
				return "text-amber-600";
			case "info":
				return "text-blue-600";
			default:
				return "text-muted-foreground";
		}
	};

	return (
		<div className="group relative">
			{/* 背景光晕效果 */}
			<div
				className={`absolute inset-0 bg-gradient-to-r ${gradient || "from-slate-400 to-slate-500"} rounded-2xl opacity-0 blur-xl transition-opacity duration-500 group-hover:opacity-10 dark:group-hover:opacity-20`}
			/>

			{/* 主卡片 */}
			<div className="relative rounded-2xl border border-white/30 bg-white/80 p-6 shadow-lg shadow-slate-200/30 backdrop-blur-xl transition-all duration-300 hover:shadow-slate-200/40 hover:shadow-xl group-hover:scale-[1.02] dark:border-slate-700/30 dark:bg-slate-800/80 dark:shadow-slate-900/30 dark:hover:shadow-slate-900/40">
				{/* 顶部装饰条 */}
				<div
					className={`absolute top-0 right-6 left-6 h-1 bg-gradient-to-r ${gradient || "from-slate-400 to-slate-500"} rounded-b-full`}
				/>

				{/* 卡片头部 */}
				<div className="mb-4 flex items-center justify-between">
					<div className="flex items-center gap-3">
						<div
							className="rounded-xl p-2.5"
							style={{
								backgroundColor: `${colorValue.replace("[", "").replace("]", "")}1A`, // 10% opacity
							}}
						>
							<div
								className={`text-white ${accentColor || "bg-slate-500"} rounded-lg p-1.5`}
							>
								{icon}
							</div>
						</div>
						<h3 className="font-medium text-slate-700 text-sm tracking-wide dark:text-slate-200">
							{title}
						</h3>
					</div>
					{change !== undefined && (
						<div
							className={`flex items-center gap-1 rounded-lg bg-slate-50/80 px-2 py-1 dark:bg-slate-700/50 ${getChangeColor(change)}`}
						>
							<span className="font-semibold text-xs">
								{(() => {
									if (change > 0) {
										return "↗ +";
									}
									if (change < 0) {
										return "↘ ";
									}
									return "→ ";
								})()}
								{change}%
							</span>
						</div>
					)}
				</div>

				{/* 主要数值 */}
				<div className="space-y-2">
					<div className="flex items-baseline gap-2">
						<span className="font-light text-3xl text-slate-800 tracking-tight dark:text-slate-100">
							{formatValue(value)}
						</span>
						{subValue && (
							<span className="font-light text-lg text-slate-500 dark:text-slate-400">
								/ {formatValue(subValue)}
							</span>
						)}
					</div>

					{/* 描述文字 */}
					<p className={`font-medium text-sm ${getStatusColor(status)}`}>
						{description}
					</p>
				</div>

				{/* 底部进度指示器（可选） */}
				{isPercentage && showProgressBar && (
					<div className="mt-4 space-y-2">
						<div className="h-1.5 w-full overflow-hidden rounded-full bg-slate-100 dark:bg-slate-700">
							<div
								className={`h-full bg-gradient-to-r ${gradient || "from-slate-400 to-slate-500"} rounded-full transition-all duration-1000 ease-out`}
								style={{ width: `${Math.min(value, 100)}%` }}
							/>
						</div>
					</div>
				)}
			</div>
		</div>
	);
}

// Dashboard Skeleton Component - 现代简约加载状态
function DashboardSkeleton() {
	return (
		<div className="space-y-8">
			{/* Header Skeleton */}
			<div className="relative">
				<div className="rounded-xl border border-white/20 bg-white/70 p-4 shadow-lg shadow-slate-200/50 backdrop-blur-xl dark:border-slate-700/30 dark:bg-slate-800/70 dark:shadow-slate-900/50">
					<div className="flex items-center gap-3">
						<Skeleton className="h-6 w-1.5 rounded-full" />
						<div className="flex-1 space-y-2">
							<Skeleton className="h-6 w-80" />
							<Skeleton className="h-4 w-64" />
						</div>
					</div>
				</div>
			</div>

			{/* Stats Cards Skeleton */}
			<div className="grid gap-6 md:grid-cols-2 xl:grid-cols-4">
				{Array.from({ length: 4 }).map((_, i) => (
					<div className="relative" key={`stat-card-${i}`}>
						<div className="rounded-2xl border border-white/30 bg-white/80 p-6 shadow-lg shadow-slate-200/30 backdrop-blur-xl dark:border-slate-700/30 dark:bg-slate-800/80 dark:shadow-slate-900/30">
							<div className="absolute top-0 right-6 left-6 h-1 rounded-b-full bg-slate-200 dark:bg-slate-600" />

							<div className="mb-4 flex items-center justify-between">
								<div className="flex items-center gap-3">
									<div className="rounded-xl bg-slate-100 p-2.5 dark:bg-slate-700">
										<Skeleton className="h-6 w-6 rounded-lg" />
									</div>
									<Skeleton className="h-4 w-20" />
								</div>
								<Skeleton className="h-6 w-12 rounded-lg" />
							</div>

							<div className="space-y-2">
								<Skeleton className="h-8 w-16" />
								<Skeleton className="h-4 w-32" />
							</div>

							<div className="mt-4">
								<Skeleton className="h-1.5 w-full rounded-full" />
							</div>
						</div>
					</div>
				))}
			</div>

			{/* Demographics Chart Skeleton */}
			<div className="relative">
				<div className="rounded-2xl border border-white/30 bg-white/80 p-6 shadow-lg shadow-slate-200/30 backdrop-blur-xl dark:border-slate-700/30 dark:bg-slate-800/80 dark:shadow-slate-900/30">
					<div className="space-y-6">
						<div className="flex items-center gap-3">
							<Skeleton className="h-6 w-6 rounded-lg" />
							<Skeleton className="h-6 w-48" />
						</div>
						<Skeleton className="h-4 w-64" />

						<div className="space-y-6">
							{Array.from({ length: 3 }).map((_, sectionIndex) => (
								<div className="space-y-4" key={`demo-section-${sectionIndex}`}>
									<Skeleton className="h-5 w-32" />
									<div className="space-y-3">
										{Array.from({ length: 4 }).map((_, itemIndex) => (
											<div
												className="flex items-center justify-between"
												key={`demo-item-${itemIndex}`}
											>
												<div className="flex flex-1 items-center gap-3">
													<Skeleton className="h-4 w-16" />
													<Skeleton className="h-2 flex-1 rounded-full" />
												</div>
												<div className="ml-3 flex items-center gap-2">
													<Skeleton className="h-4 w-8" />
													<Skeleton className="h-5 w-12 rounded-md" />
												</div>
											</div>
										))}
									</div>
								</div>
							))}
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
