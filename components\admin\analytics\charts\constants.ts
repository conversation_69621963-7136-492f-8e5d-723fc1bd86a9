// Chart system constants and shared utilities

// Brand colour palette - centralised for consistency
export const CHART_COLORS = {
  // Primary brand colours
  PRIMARY: "#FF6308", // Orange
  SECONDARY: "#97A4FF", // Light Blue

  // Extended palette for charts - keeping original colours for compatibility
  PALETTE: [
    "#FF6308", // Orange (Primary)
    "#97A4FF", // Light Blue (Secondary)
    "#3B82F6", // Blue
    "#10B981", // Green
    "#F59E0B", // Amber
    "#EF4444", // Red
    "#8B5CF6", // Purple
    "#06B6D4", // Cyan
    "#F97316", // Orange variant
    "#6366F1", // Indigo
    "#EC4899", // Pink
    "#14B8A6", // Teal
  ],

  // Semantic colours
  SUCCESS: "#10B981",
  WARNING: "#F59E0B",
  ERROR: "#EF4444",
  INFO: "#3B82F6",

  // Chart-specific palettes
  HEATMAP: {
    LOW: "#F1F5F9", // slate-100
    MEDIUM: "#94A3B8", // slate-400
    HIGH: "#FF6308", // Primary orange
  },

  BACKGROUND: {
    LIGHT: "#FFFFFF",
    DARK: "#0F172A",
    CARD_LIGHT: "#F8FAFC",
    CARD_DARK: "#1E293B",
  },
};

// Chart configuration defaults
export const CHART_DEFAULTS = {
  HEIGHT: 400,
  ANIMATION_DURATION: 800,
  TOOLTIP_DELAY: 0,

  MARGINS: {
    DEFAULT: { top: 30, right: 50, bottom: 40, left: 50 },
    LARGE: { top: 40, right: 60, bottom: 60, left: 60 },
    COMPACT: { top: 20, right: 30, bottom: 20, left: 30 },
    LEGEND: { top: 40, right: 80, bottom: 60, left: 80 }, // Extra space for legends
  },

  RESPONSIVE: {
    MOBILE_BREAKPOINT: 640,
    TABLET_BREAKPOINT: 768,
    DESKTOP_BREAKPOINT: 1024,
  },
};

// Mobile-specific chart configurations
export const MOBILE_CHART_CONFIGS = {
  HEIGHT: {
    MOBILE: 250,
    TABLET: 300,
    DESKTOP: 400,
  },
  MARGINS: {
    MOBILE: { top: 15, right: 15, bottom: 25, left: 25 },
    TABLET: { top: 20, right: 25, bottom: 30, left: 30 },
    DESKTOP: { top: 30, right: 50, bottom: 40, left: 50 },
  },
  FONT_SIZES: {
    MOBILE: {
      TICK: 10,
      LABEL: 11,
      LEGEND: 12,
    },
    TABLET: {
      TICK: 11,
      LABEL: 12,
      LEGEND: 13,
    },
    DESKTOP: {
      TICK: 12,
      LABEL: 13,
      LEGEND: 14,
    },
  },
};

// Shared constants across chart types
export const SHARED_CHART_CONSTANTS = {
  LABEL_ANGLE_THRESHOLD: 6, // Threshold for rotating X-axis labels
};

// Chart type configurations
export const CHART_CONFIGS = {
  BAR: {
    MIN_BAR_SIZE: 20,
    BORDER_RADIUS: [4, 4, 0, 0],
    LABEL_ANGLE_THRESHOLD: SHARED_CHART_CONSTANTS.LABEL_ANGLE_THRESHOLD,
  },

  PIE: {
    OUTER_RADIUS: 120,
    INNER_RADIUS: 0,
    LABEL_THRESHOLD: 0.05, // 5% minimum for labels
  },

  LINE: {
    STROKE_WIDTH: 3,
    DOT_RADIUS: 5,
    ACTIVE_DOT_RADIUS: 7,
    LABEL_ANGLE_THRESHOLD: SHARED_CHART_CONSTANTS.LABEL_ANGLE_THRESHOLD,
  },

  AREA: {
    STROKE_WIDTH: 2,
    FILL_OPACITY: 0.6,
    LABEL_ANGLE_THRESHOLD: SHARED_CHART_CONSTANTS.LABEL_ANGLE_THRESHOLD,
  },

  SCATTER: {
    DEFAULT_RADIUS: 6,
    HOVER_RADIUS: 8,
    CATEGORY_SPACING: 100, // Animation delay between categories
  },

  HEATMAP: {
    CELL_PADDING: 1,
    MIN_CELL_SIZE: 20,
  },

  TREEMAP: {
    MIN_CELL_WIDTH: 40,
    MIN_CELL_HEIGHT: 20,
    STROKE_WIDTH: 2,
  },
};

// Utility function to get color by index
export function getChartColor(index: number): string {
  return CHART_COLORS.PALETTE[index % CHART_COLORS.PALETTE.length];
}

// Utility function to generate color based on string hash
export function getColorByString(str: string): string {
  const hash = Math.abs(str.split("").reduce((a, b) => a + b.charCodeAt(0), 0));
  return CHART_COLORS.PALETTE[hash % CHART_COLORS.PALETTE.length];
}

// Utility function to get semantic color
export function getSemanticColor(type: "success" | "warning" | "error" | "info"): string {
  switch (type) {
    case "success":
      return CHART_COLORS.SUCCESS;
    case "warning":
      return CHART_COLORS.WARNING;
    case "error":
      return CHART_COLORS.ERROR;
    case "info":
      return CHART_COLORS.INFO;
    default:
      return CHART_COLORS.PRIMARY;
  }
}

// Chart size presets
export const CHART_SIZES = {
  SMALL: { width: 300, height: 200 },
  MEDIUM: { width: 500, height: 300 },
  LARGE: { width: 800, height: 400 },
  EXTRA_LARGE: { width: 1200, height: 600 },
};

// Export types for TypeScript
export type ChartColorPalette = typeof CHART_COLORS.PALETTE;
export type ChartSize = keyof typeof CHART_SIZES;
export type SemanticColorType = "success" | "warning" | "error" | "info";
