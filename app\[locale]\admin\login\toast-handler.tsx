"use client";

import { useEffect } from "react";
import { useAlert } from "@/contexts/AlertContext";
import { useToastTranslation } from "@/hooks/useToastTranslation";

type ToastHandlerProps = {
  toast: {
    type: "success" | "error" | "info" | "warning";
    message: string;
  } | null;
};

export function ToastHandler({ toast }: ToastHandlerProps) {
  const { showAlert } = useAlert();
  const { enhanceToastMessage } = useToastTranslation();

  useEffect(() => {
    if (toast) {
      // Enhance the message with translation if needed
      const enhancedMessage = enhanceToastMessage(toast.message, "auth");

      // Use a delay to ensure the toast system is fully initialized
      setTimeout(() => {
        showAlert(toast.type, enhancedMessage);
        // Note: Cookie cleanup is handled by short expiry time (10 seconds)
        // No programmatic deletion needed - cookies expire naturally
      }, 500);
    }
  }, [toast, showAlert, enhanceToastMessage]);

  // This component doesn't render anything
  return null;
}
