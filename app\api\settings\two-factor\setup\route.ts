import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { generateTOTPQRCode, generateTOTPSecret } from "@/lib/auth";
import { authOptions } from "@/lib/auth/auth-options";
import { prisma } from "@/lib/db/prisma";
import { getAdminById, setupTwoFactorAuth } from "@/lib/db/users";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";

// POST endpoint to initialize 2FA setup
export async function POST() {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const t = await getTranslations({ locale, namespace: "errors" });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require authentication for setting up 2FA
    if (!session || session.user.role !== "admin") {
      return NextResponse.json({ error: t("unauthorized") }, { status: 401 });
    }

    // Get admin ID from session
    const adminId = Number.parseInt(session.user.id, 10);

    // Get admin from database
    const admin = await getAdminById(adminId);

    if (!admin) {
      return NextResponse.json({ error: t("adminNotFound") }, { status: 404 });
    }

    // Generate a new TOTP secret
    const secret = generateTOTPSecret();

    // Store the secret in the database (but don't enable 2FA yet)
    await setupTwoFactorAuth(adminId, secret, false);

    // Generate a QR code for the secret
    const qrCode = await generateTOTPQRCode(secret, admin.username, "WSCCC Census");

    // Log the action
    try {
      await prisma.auditLog.create({
        data: {
          userType: "admin",
          userId: Number.parseInt(adminId.toString(), 10),
          action: "setup-2fa",
          entityType: "admins",
          entityId: Number.parseInt(adminId.toString(), 10),
          newValues: JSON.stringify({ two_factor_secret_generated: true }),
        },
      });
    } catch (_logError) {}

    // Return the secret and QR code
    return NextResponse.json({
      secret,
      qrCode,
    });
  } catch (_error) {
    return NextResponse.json({ error: t("failedToSetupTwoFactor") }, { status: 500 });
  }
}
