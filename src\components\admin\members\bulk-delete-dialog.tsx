"use client";

import {
	<PERSON>ert<PERSON>ircle,
	AlertTriangle,
	CheckCircle,
	Home,
	Trash2,
	Users,
	XCircle,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useCallback, useEffect, useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import {
	Drawer,
	DrawerContent,
	DrawerDescription,
	DrawerHeader,
	DrawerTitle,
} from "@/components/ui/drawer";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useIsMobile } from "@/hooks/use-mobile";
import { useMessage } from "@/hooks/useMessage";

interface BulkDeleteDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	memberIds: number[];
	onMembersDeleted: () => void;
}

interface ValidationResult {
	canProceed: boolean;
	summary: {
		total: number;
		deletable: number;
		undeletable: number;
		householdsToDelete: number;
		regularMembers: number;
		householdHeads: number;
	};
	undeletableMembers: Array<{
		memberId: number;
		reason: string;
		deleteType: string;
	}>;
	householdDeletions: Array<{
		memberId: number;
		householdId: number;
	}>;
	warnings: string[];
}

export function BulkDeleteDialog({
	open,
	onOpenChange,
	memberIds,
	onMembersDeleted,
}: BulkDeleteDialogProps) {
	const { showError } = useMessage();
	const isMobile = useIsMobile();
	const t = useTranslations();
	const tCommon = useTranslations("common");
	const tDialogs = useTranslations("dialogs");
	const [validation, setValidation] = useState<ValidationResult | null>(null);
	const [loading, setLoading] = useState(true);
	const [isDeleting, setIsDeleting] = useState(false);

	const fetchValidation = useCallback(async () => {
		try {
			setLoading(true);
			const response = await fetch(
				"/api/admin/members/bulk-delete-validation",
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify({ memberIds }),
				},
			);

			if (!response.ok) {
				throw new Error(t("errors.failedToValidateBulkDelete"));
			}

			const data = await response.json();
			setValidation(data);
		} catch (error) {
			console.error("Error validating bulk delete:", error);
			showError("failedToValidateBulkDeleteOperation");
			onOpenChange(false);
		} finally {
			setLoading(false);
		}
	}, [memberIds, showError, onOpenChange, t]);

	// Fetch validation info when dialog opens
	useEffect(() => {
		if (open && memberIds.length > 0) {
			fetchValidation();
		}
	}, [open, memberIds, fetchValidation]);

	const handleBulkDelete = async () => {
		if (!validation?.canProceed) return;

		try {
			setIsDeleting(true);
			const response = await fetch("/api/admin/members/bulk-delete", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ memberIds }),
			});

			const data = await response.json();

			if (!response.ok) {
				throw new Error(data.error || t("common.failedToDeleteMembers"));
			}

			// Success message is now handled by server via centralized alert system
			// No need for showDirect - the toast will appear automatically
			onOpenChange(false);
			onMembersDeleted();
		} catch (error) {
			console.error("Error deleting members:", error);
			showError("failedToDeleteMembers");
		} finally {
			setIsDeleting(false);
		}
	};

	// Shared content component for both mobile and desktop
	const DialogContentComponent = () => {
		if (loading) {
			return (
				<div className="py-6 text-center">
					<div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent" />
					<p className="text-muted-foreground">
						{t("common.validatingBulkDeleteOperation")}
					</p>
				</div>
			);
		}

		if (!validation) {
			return (
				<div className="py-6 text-center">
					<AlertCircle className="mx-auto mb-4 h-8 w-8 text-destructive" />
					<p className="text-muted-foreground">
						{t("common.failedToValidateBulkDeleteOper")}
					</p>
				</div>
			);
		}

		return (
			<div className="space-y-4">
				{/* Summary */}
				<div className="grid grid-cols-2 gap-4">
					<div className="rounded-lg bg-muted/20 p-3">
						<div className="flex items-center gap-2">
							<Users className="h-4 w-4 text-primary" />
							<span className="font-medium text-sm">
								{t("common.totalSelected")}
							</span>
						</div>
						<p className="font-semibold text-lg">{validation.summary.total}</p>
					</div>
					<div className="rounded-lg bg-muted/20 p-3">
						<div className="flex items-center gap-2">
							<CheckCircle className="h-4 w-4 text-green-600" />
							<span className="font-medium text-sm">
								{t("common.canDelete")}
							</span>
						</div>
						<p className="font-semibold text-green-600 text-lg">
							{validation.summary.deletable}
						</p>
					</div>
				</div>

				{/* Warnings */}
				{validation.warnings.length > 0 && (
					<div className="rounded-lg border border-orange-200 bg-orange-50 p-3 dark:border-orange-800 dark:bg-orange-950/20">
						<div className="mb-2 flex items-center gap-2">
							<AlertTriangle className="h-4 w-4 text-orange-600 dark:text-orange-400" />
							<span className="font-medium text-orange-800 dark:text-orange-200">
								{t("common.importantWarnings")}
							</span>
						</div>
						<ul className="space-y-1 text-orange-700 text-sm dark:text-orange-300">
							{validation.warnings.map((warning, index) => (
								<li className="flex items-center gap-2" key={index}>
									<Home className="h-3 w-3" />
									{warning}
								</li>
							))}
						</ul>
					</div>
				)}

				{/* Undeletable Members */}
				{validation.undeletableMembers.length > 0 && (
					<div className="rounded-lg border border-destructive/20 bg-destructive/10 p-3">
						<div className="mb-2 flex items-center gap-2">
							<XCircle className="h-4 w-4 text-destructive" />
							<span className="font-medium text-destructive">
								{tDialogs("cannotDeleteCount", {
									count: validation.undeletableMembers.length.toString(),
								})}
							</span>
						</div>
						<ScrollArea className="max-h-32">
							<div className="space-y-1">
								{validation.undeletableMembers.map((member) => (
									<div
										className="text-muted-foreground text-sm"
										key={member.memberId}
									>
										<span className="font-medium">
											{t("admin.member")} #{member.memberId}:
										</span>{" "}
										{member.reason}
									</div>
								))}
							</div>
						</ScrollArea>
					</div>
				)}

				{/* Breakdown */}
				{validation.canProceed && (
					<div className="space-y-2 text-sm">
						<div className="flex items-center justify-between">
							<span className="text-muted-foreground">
								{t("common.regularMembers")}:
							</span>
							<Badge variant="outline">
								{validation.summary.regularMembers}
							</Badge>
						</div>
						<div className="flex items-center justify-between">
							<span className="text-muted-foreground">
								{t("common.householdHeads")}:
							</span>
							<Badge variant="outline">
								{validation.summary.householdHeads}
							</Badge>
						</div>
						{validation.summary.householdsToDelete > 0 && (
							<div className="flex items-center justify-between">
								<span className="text-muted-foreground">
									{t("common.householdsToDelete")}:
								</span>
								<Badge variant="destructive">
									{validation.summary.householdsToDelete}
								</Badge>
							</div>
						)}
					</div>
				)}

				{/* Action Description */}
				{validation.canProceed ? (
					<div className="rounded-lg bg-muted/10 p-3">
						<p className="text-muted-foreground text-sm">
							{tDialogs("thisWillPermanentlyDeleteMembers", {
								count: validation.summary.deletable.toString(),
								members:
									validation.summary.deletable === 1
										? t("admin.member")
										: t("admin.members"),
							})}
							{validation.summary.householdsToDelete > 0 && (
								<>
									{" "}
									{tDialogs("householdsWillBeRemovedAndCodesUnassigned", {
										count: validation.summary.householdsToDelete.toString(),
										households:
											validation.summary.householdsToDelete === 1
												? t("admin.household")
												: t("admin.households"),
									})}
								</>
							)}
						</p>
					</div>
				) : (
					<div className="rounded-lg bg-destructive/10 p-3">
						<p className="text-destructive text-sm">
							{tDialogs("cannotProceedBulkDeleteResolveIssues")}
						</p>
					</div>
				)}
			</div>
		);
	};

	// Action buttons component for both mobile and desktop
	const ActionButtons = () => {
		if (loading || !validation) {
			return (
				<Button onClick={() => onOpenChange(false)} variant="outline">
					{tCommon("cancel")}
				</Button>
			);
		}

		if (!validation.canProceed) {
			return (
				<Button onClick={() => onOpenChange(false)} variant="outline">
					{tCommon("close")}
				</Button>
			);
		}

		return (
			<>
				<Button onClick={() => onOpenChange(false)} variant="outline">
					{tCommon("cancel")}
				</Button>
				<Button
					className="flex items-center gap-2"
					disabled={isDeleting}
					onClick={handleBulkDelete}
					variant="destructive"
				>
					{isDeleting ? (
						<>
							<div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
							{tCommon("deleting")}
						</>
					) : (
						<>
							<Trash2 className="h-4 w-4" />
							{tCommon("delete")} {validation.summary.deletable}{" "}
							{validation.summary.deletable === 1
								? t("admin.member")
								: t("admin.members")}
						</>
					)}
				</Button>
			</>
		);
	};

	// Mobile implementation using Drawer (native swipe-to-dismiss from bottom)
	// Features: Visual handle bar, smooth swipe gestures, and hidden scrollbar
	if (isMobile) {
		return (
			<Drawer onOpenChange={onOpenChange} open={open}>
				<DrawerContent className="flex max-h-[80vh] min-h-[60vh] flex-col">
					<DrawerHeader className="pb-4 text-left">
						<DrawerTitle className="font-semibold text-lg">
							{t("common.bulkDeleteMembers")}
						</DrawerTitle>
						<DrawerDescription className="text-muted-foreground">
							{validation?.canProceed
								? tDialogs("pleaseReviewInformationBulkDelete")
								: tDialogs("someMembersCannotBeDeletedReview")}
						</DrawerDescription>
					</DrawerHeader>

					<div className="scrollbar-hide flex-1 overflow-y-auto px-4">
						<DialogContentComponent />
					</div>

					<div className="border-t px-4 pt-4 pb-4">
						<div className="flex justify-end gap-2">
							<ActionButtons />
						</div>
					</div>
				</DrawerContent>
			</Drawer>
		);
	}

	// Desktop implementation using Dialog (unchanged)
	return (
		<Dialog onOpenChange={onOpenChange} open={open}>
			<DialogContent className="max-h-[80vh] sm:max-w-[600px]">
				<DialogHeader>
					<DialogTitle>{t("common.bulkDeleteMembers")}</DialogTitle>
					<DialogDescription>
						{validation?.canProceed
							? tDialogs("pleaseReviewInformationBulkDelete")
							: tDialogs("someMembersCannotBeDeletedReview")}
					</DialogDescription>
				</DialogHeader>

				<ScrollArea className="max-h-[60vh]">
					<DialogContentComponent />
				</ScrollArea>

				<DialogFooter className="flex gap-2">
					<ActionButtons />
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
