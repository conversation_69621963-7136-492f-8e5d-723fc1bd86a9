"use client";

import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { ChartWrapper, type ChartWrapperProps } from "./chart-registry";
import { MOBILE_CHART_CONFIGS } from "./constants";

interface MobileResponsiveChartWrapperProps extends ChartWrapperProps {
  // Additional mobile-specific props can be added here
  forceMobileLayout?: boolean;
  mobileHeight?: number;
}

/**
 * Mobile-responsive chart wrapper that adapts chart dimensions and styling
 * based on screen size to prevent clipping and improve mobile UX
 */
export function MobileResponsiveChartWrapper({
  data,
  isAnimationActive,
  className,
  title,
  description,
  actions,
  enableExport = false,
  enableVirtualization = false,
  showDataSummary = false,
  forceMobileLayout = false,
  mobileHeight,
  ...props
}: MobileResponsiveChartWrapperProps) {
  const t = useTranslations("admin");
  const [screenSize, setScreenSize] = useState<"mobile" | "tablet" | "desktop">("desktop");
  const [isClient, setIsClient] = useState(false);

  // Determine screen size category
  useEffect(() => {
    setIsClient(true);

    const updateScreenSize = () => {
      const width = window.innerWidth;
      if (width < 640 || forceMobileLayout) {
        setScreenSize("mobile");
      } else if (width < 1024) {
        setScreenSize("tablet");
      } else {
        setScreenSize("desktop");
      }
    };

    updateScreenSize();
    window.addEventListener("resize", updateScreenSize);
    return () => window.removeEventListener("resize", updateScreenSize);
  }, [forceMobileLayout]);

  // Don't render until client-side to prevent hydration mismatch
  if (!isClient) {
    return (
      <div className="flex h-64 w-full animate-pulse items-center justify-center rounded-lg bg-muted">
        <div className="text-muted-foreground text-sm">{t("loadingChart")}</div>
      </div>
    );
  }

  // Get responsive configurations
  const getResponsiveHeight = () => {
    if (mobileHeight && (screenSize === "mobile" || forceMobileLayout)) {
      return mobileHeight;
    }
    return MOBILE_CHART_CONFIGS.HEIGHT[
      screenSize.toUpperCase() as keyof typeof MOBILE_CHART_CONFIGS.HEIGHT
    ];
  };

  const getResponsiveMargins = () => {
    return MOBILE_CHART_CONFIGS.MARGINS[
      screenSize.toUpperCase() as keyof typeof MOBILE_CHART_CONFIGS.MARGINS
    ];
  };

  // Create mobile-optimized chart data with responsive configurations
  const responsiveData = {
    ...data,
    // Add responsive configurations to data object
    _responsive: {
      height: getResponsiveHeight(),
      margins: getResponsiveMargins(),
      screenSize,
      isMobile: screenSize === "mobile" || forceMobileLayout,
      fontSize:
        MOBILE_CHART_CONFIGS.FONT_SIZES[
          screenSize.toUpperCase() as keyof typeof MOBILE_CHART_CONFIGS.FONT_SIZES
        ],
    },
  };

  // Mobile-specific container styles
  const containerClassName = `
    w-full 
    ${screenSize === "mobile" ? "px-2" : screenSize === "tablet" ? "px-3" : "px-4"}
    ${className || ""}
  `.trim();

  return (
    <div className="w-full overflow-hidden">
      {/* Mobile-specific container with proper padding and overflow handling */}
      <div className={containerClassName}>
        <ChartWrapper
          actions={actions}
          className="w-full border-0 bg-transparent"
          data={responsiveData}
          description={description}
          enableExport={enableExport}
          enableVirtualization={enableVirtualization}
          isAnimationActive={isAnimationActive}
          showDataSummary={showDataSummary}
          title={title}
          {...props}
        />
      </div>

      {/* Mobile-specific helper text for better UX */}
      {screenSize === "mobile" && data.data.length > 5 && (
        <div className="mt-2 px-4 text-center text-muted-foreground text-xs">
          Tap chart elements for details • Pinch to zoom
        </div>
      )}
    </div>
  );
}

/**
 * Hook to get responsive chart configurations based on current screen size
 */
export function useResponsiveChartConfig(forceMobile = false) {
  const [config, setConfig] = useState({
    height: MOBILE_CHART_CONFIGS.HEIGHT.DESKTOP,
    margins: MOBILE_CHART_CONFIGS.MARGINS.DESKTOP,
    fontSize: MOBILE_CHART_CONFIGS.FONT_SIZES.DESKTOP,
    screenSize: "desktop" as "mobile" | "tablet" | "desktop",
  });

  useEffect(() => {
    const updateConfig = () => {
      const width = window.innerWidth;
      let screenSize: "mobile" | "tablet" | "desktop";

      if (width < 640 || forceMobile) {
        screenSize = "mobile";
      } else if (width < 1024) {
        screenSize = "tablet";
      } else {
        screenSize = "desktop";
      }

      setConfig({
        height:
          MOBILE_CHART_CONFIGS.HEIGHT[
            screenSize.toUpperCase() as keyof typeof MOBILE_CHART_CONFIGS.HEIGHT
          ],
        margins:
          MOBILE_CHART_CONFIGS.MARGINS[
            screenSize.toUpperCase() as keyof typeof MOBILE_CHART_CONFIGS.MARGINS
          ],
        fontSize:
          MOBILE_CHART_CONFIGS.FONT_SIZES[
            screenSize.toUpperCase() as keyof typeof MOBILE_CHART_CONFIGS.FONT_SIZES
          ],
        screenSize,
      });
    };

    updateConfig();
    window.addEventListener("resize", updateConfig);
    return () => window.removeEventListener("resize", updateConfig);
  }, [forceMobile]);

  return config;
}

export default MobileResponsiveChartWrapper;
