"use client";

import { usePathname } from "next/navigation";
import { useTranslations } from "next-intl";
import { AdminSidebar } from "@/components/admin/admin-sidebar";
import { CensusStatusIndicator } from "@/components/admin/census-status-indicator";
import { UserDropdown } from "@/components/admin/user-dropdown";
import { LanguageSelector } from "@/components/language/language-selector";
import { ThemeToggle } from "@/components/theme/theme-toggle";
import { Separator } from "@/components/ui/separator";
import { SidebarInset, SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";

// Removed breadcrumb imports as we're using a simple heading instead

interface AdminLayoutClientProps {
  children: React.ReactNode;
  userName?: string;
}

export function AdminLayoutClient({ children, userName }: AdminLayoutClientProps) {
  // Check if the current path is the login page or print cards page
  const pathname = usePathname();
  const t = useTranslations();
  const isLoginPage = pathname === "/admin/login";
  const isPrintCardsPage = pathname === "/admin/unique-code/print-cards";

  // If it's the login page or print cards page, don't show the sidebar
  if (isLoginPage || isPrintCardsPage) {
    return <>{children}</>;
  }

  // Get the current page name from the pathname
  const pageName = pathname ? getPageNameFromPath(pathname, t) : t("common.adminPortal");

  // For all other admin pages, show the sidebar layout
  return (
    <SidebarProvider defaultOpen={true}>
      <AdminSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" style={{ cursor: "pointer" }} />
            <Separator className="mr-2 h-4" orientation="vertical" />
            <h1 className="font-semibold text-lg">{pageName}</h1>
          </div>

          {/* Header Controls */}
          <div className="ml-auto flex items-center gap-2">
            <LanguageSelector variant="header" />
            <ThemeToggle />
            <CensusStatusIndicator />
            <Separator className="mr-2 h-4" orientation="vertical" />
            <div className="pr-4">
              <UserDropdown userName={userName || "Admin"} />
            </div>
          </div>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          <main
            className={`flex-1 bg-background ${
              pathname === "/admin/analytics"
                ? "min-h-[calc(100vh-6rem)] p-0"
                : "min-h-[calc(100vh-6rem)] rounded-xl p-6"
            }`}
          >
            {children}
          </main>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}

// Helper function to get a friendly page name from the path
function getPageNameFromPath(path: string, t: any): string {
  const segments = path.split("/");
  const lastSegment = segments[segments.length - 1];

  // Handle special cases
  if (lastSegment === "dashboard") return t("navigation.dashboard");
  if (lastSegment === "unique-code") return t("common.uniqueCode");
  if (lastSegment === "household") return t("navigation.household");
  if (lastSegment === "members") return t("navigation.members");
  if (lastSegment === "analytics") return t("navigation.analytics");
  if (lastSegment === "settings") return t("navigation.settings");

  // Default: capitalize the first letter
  return lastSegment.charAt(0).toUpperCase() + lastSegment.slice(1);
}
