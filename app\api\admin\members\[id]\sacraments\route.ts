import { type NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { authOptions } from "@/lib/auth/auth-options";
import { prisma } from "@/lib/db/prisma";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";

/**
 * GET /api/admin/members/:id/sacraments
 *
 * Fetches all sacraments for a specific member
 * Only accessible to admin users
 */
export async function GET(
	_request: NextRequest,
	{ params }: { params: Promise<{ id: string }> },
) {
	// Get locale from centralized utility at the beginning
	const locale = await getLocaleFromCookies();
	const t = await getTranslations({ locale, namespace: "errors" });

	try {
		// Check authentication
		const session = await getServerSession(authOptions);

		// Require admin authentication
		if (!session || session.user.role !== "admin") {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// In Next.js 15+, params is a Promise that must be awaited
		const { id: idString } = await params;
		const memberId = Number.parseInt(idString, 10);

		if (Number.isNaN(memberId)) {
			return NextResponse.json({ error: "Invalid member ID" }, { status: 400 });
		}

		// Get all sacraments for this member with sacrament type details using Prisma
		const sacraments = await prisma.sacrament.findMany({
			where: {
				memberId,
			},
			include: {
				sacramentType: true,
				censusYear: true,
			},
			orderBy: [{ sacramentType: { id: "asc" } }, { date: "desc" }],
		});

		// Get sacrament types for reference using Prisma
		const sacramentTypes = await prisma.sacramentType.findMany({
			orderBy: { id: "asc" },
		});

		// Format the response to match the expected structure
		const formattedSacraments = sacraments.map((s) => ({
			...s,
			sacrament_name: s.sacramentType.name,
			sacrament_code: s.sacramentType.code,
			sacrament_description: s.sacramentType.description,
			census_year: s.censusYear.year,
		}));

		return NextResponse.json({
			sacraments: formattedSacraments,
			sacramentTypes,
		});
	} catch (error) {
		return NextResponse.json(
			{
				error: t("memberSacramentsFetchFailed"),
				details: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 },
		);
	}
}
