import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { isCensusOpen } from "@/lib/census/census-availability";

/**
 * Middleware to check and update census status on each request
 * This ensures the census is automatically opened/closed based on schedule
 */
export async function censusMiddleware(request: NextRequest) {
	// Skip for admin routes, API routes, and static assets
	const path = request.nextUrl.pathname;
	if (
		path.startsWith("/admin") ||
		path.startsWith("/api") ||
		path.startsWith("/_next") ||
		path.includes(".") // Static files like .js, .css, etc.
	) {
		return NextResponse.next();
	}

	// Check and update census status if needed
	// This now uses caching to reduce database connections
	try {
		await isCensusOpen();
	} catch (error) {
		if (process.env.NODE_ENV === "development") {
			console.error("Error checking census status:", error);
		}
		// Continue with the request even if there's an error
	}

	// Continue with the request
	return NextResponse.next();
}
