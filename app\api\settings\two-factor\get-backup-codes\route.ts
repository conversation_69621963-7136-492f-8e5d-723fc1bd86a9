import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { authOptions } from "@/lib/auth/auth-options";
import { getAdminById, getTwoFactorBackupCodes } from "@/lib/db/users";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";

// GET endpoint to fetch backup codes
export async function GET() {
	// Get locale from centralized utility at the beginning
	const locale = await getLocaleFromCookies();
	const t = await getTranslations({ locale, namespace: "errors" });

	try {
		// Check authentication
		const session = await getServerSession(authOptions);

		// Require authentication for accessing backup codes
		if (!session || session.user.role !== "admin") {
			return NextResponse.json({ error: t("unauthorized") }, { status: 401 });
		}

		// Get admin ID from session
		const adminId = Number.parseInt(session.user.id, 10);

		// Get admin from database
		const admin = await getAdminById(adminId);

		if (!admin) {
			return NextResponse.json({ error: t("adminNotFound") }, { status: 404 });
		}

		// Check if 2FA is enabled
		if (!admin.twoFactorEnabled) {
			return NextResponse.json(
				{ error: t("twoFactorNotEnabled") },
				{ status: 400 },
			);
		}

		// Get backup codes
		const backupCodes = await getTwoFactorBackupCodes(adminId);

		if (!backupCodes) {
			return NextResponse.json(
				{ error: t("noBackupCodesFound") },
				{ status: 404 },
			);
		}

		// Return backup codes
		return NextResponse.json({
			success: true,
			backupCodes,
		});
	} catch (_error) {
		return NextResponse.json(
			{ error: t("failedToFetchBackupCodes") },
			{ status: 500 },
		);
	}
}
