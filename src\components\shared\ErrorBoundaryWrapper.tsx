import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Bug, Refresh<PERSON><PERSON> } from "lucide-react";
import { useTranslations } from "next-intl";
import React, { Component, type ReactNode } from "react";
import { Button } from "@/components/ui/button";

interface ErrorBoundaryState {
	hasError: boolean;
	error?: Error;
	errorInfo?: React.ErrorInfo;
	retryCount: number;
}

interface ErrorBoundaryProps {
	children: ReactNode;
	fallback?: React.ComponentType<ErrorFallbackProps>;
	onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
	maxRetries?: number;
	resetOnPropsChange?: boolean;
	resetKeys?: Array<string | number>;
}

interface ErrorFallbackProps {
	error: Error;
	resetError: () => void;
	retryCount: number;
	maxRetries: number;
}

/**
 * Enhanced error boundary with retry logic and better error reporting
 */
export class ErrorBoundaryWrapper extends Component<
	ErrorBoundaryProps,
	ErrorBoundaryState
> {
	private resetTimeoutId: number | null = null;

	constructor(props: ErrorBoundaryProps) {
		super(props);
		this.state = {
			hasError: false,
			retryCount: 0,
		};
	}

	static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
		return {
			hasError: true,
			error,
		};
	}

	componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
		this.setState({
			error,
			errorInfo,
		});

		// Call custom error handler
		this.props.onError?.(error, errorInfo);

		// Log error for debugging
		if (process.env.NODE_ENV === "development") {
			console.error("ErrorBoundary caught an error:", error, errorInfo);
		}
	}

	componentDidUpdate(prevProps: ErrorBoundaryProps) {
		const { resetOnPropsChange, resetKeys } = this.props;
		const { hasError } = this.state;

		// Reset error state if resetKeys changed
		if (hasError && resetOnPropsChange && resetKeys) {
			const prevResetKeys = prevProps.resetKeys || [];
			const hasResetKeyChanged =
				resetKeys.length !== prevResetKeys.length ||
				resetKeys.some((key, i) => key !== prevResetKeys[i]);

			if (hasResetKeyChanged) {
				this.resetError();
			}
		}
	}

	componentWillUnmount() {
		if (this.resetTimeoutId) {
			clearTimeout(this.resetTimeoutId);
		}
	}

	resetError = () => {
		const { maxRetries = 3 } = this.props;
		const { retryCount } = this.state;

		if (retryCount < maxRetries) {
			this.setState({
				hasError: false,
				error: undefined,
				errorInfo: undefined,
				retryCount: retryCount + 1,
			});
		} else {
			// Provide feedback when max retries are reached
			if (process.env.NODE_ENV === "development") {
				console.warn("[ErrorBoundary] Maximum retry attempts reached", {
					retryCount,
					maxRetries,
					component: this.constructor.name,
				});
			}
		}
	};

	render() {
		const { hasError, error, retryCount } = this.state;
		const { children, fallback: CustomFallback, maxRetries = 3 } = this.props;

		if (hasError && error) {
			const FallbackComponent = CustomFallback || DefaultErrorFallback;
			return (
				<FallbackComponent
					error={error}
					maxRetries={maxRetries}
					resetError={this.resetError}
					retryCount={retryCount}
				/>
			);
		}

		return children;
	}
}

/**
 * Default error fallback component
 */
function DefaultErrorFallback({
	error,
	resetError,
	retryCount,
	maxRetries,
}: ErrorFallbackProps) {
	const canRetry = retryCount < maxRetries;
	const isDevelopment = process.env.NODE_ENV === "development";
	const t = useTranslations("errors");
	const tCommon = useTranslations("common");

	return (
		<div className="flex min-h-[200px] items-center justify-center rounded-lg border border-red-200 bg-red-50 p-6 dark:border-red-800 dark:bg-red-900/20">
			<div className="max-w-md text-center">
				<div className="mb-4 flex justify-center">
					<AlertTriangle className="h-12 w-12 text-red-500" />
				</div>

				<h3 className="mb-2 font-semibold text-lg text-red-700 dark:text-red-400">
					{t("somethingWentWrong")}
				</h3>

				<p className="mb-4 text-red-600 dark:text-red-300">
					{isDevelopment ? error.message : t("unexpectedErrorComponent")}
				</p>

				{canRetry && (
					<Button
						className="mb-3"
						onClick={resetError}
						size="sm"
						variant="outline"
					>
						<RefreshCw className="mr-2 h-4 w-4" />
						{t("tryAgain")} ({maxRetries - retryCount} {tCommon("attemptsLeft")}
						)
					</Button>
				)}

				{isDevelopment && (
					<details className="mt-4 text-left">
						<summary className="flex cursor-pointer items-center gap-2 text-red-600 text-sm dark:text-red-400">
							<Bug className="h-4 w-4" />
							{t("errorDetails")} (Development)
						</summary>
						<pre className="mt-2 max-h-40 overflow-auto rounded bg-red-100 p-3 text-red-800 text-xs dark:bg-red-900/40 dark:text-red-200">
							{error.stack}
						</pre>
					</details>
				)}
			</div>
		</div>
	);
}

/**
 * Hook for using error boundary in functional components
 */
export function useErrorBoundary() {
	const [error, setError] = React.useState<Error | null>(null);

	const resetError = React.useCallback(() => {
		setError(null);
	}, []);

	const captureError = React.useCallback((error: Error) => {
		setError(error);
	}, []);

	// Throw error in render phase to avoid React 18 Strict Mode issues
	if (error) {
		throw error;
	}

	return { captureError, resetError };
}
