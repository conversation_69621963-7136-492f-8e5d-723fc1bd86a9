import { cva, type VariantProps } from "class-variance-authority";
import type * as React from "react";

import { cn } from "@/lib/utils";

const inputVariants = cva(
	"flex w-full min-w-0 border-input bg-transparent text-base outline-none transition-[color,box-shadow] selection:bg-primary selection:text-primary-foreground file:inline-flex file:h-7 file:border-0 file:bg-transparent file:font-medium file:text-foreground file:text-sm placeholder:text-muted-foreground disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 aria-invalid:border-destructive aria-invalid:ring-destructive/20 md:text-sm dark:bg-input/30 dark:aria-invalid:ring-destructive/40",
	{
		variants: {
			variant: {
				default:
					"h-9 rounded-md border px-3 py-1 shadow-xs focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50",
				line: "h-10 rounded-none border-0 border-border border-b-2 px-0 py-2 focus-visible:border-primary/40 focus-visible:ring-0",
			},
		},
		defaultVariants: {
			variant: "default",
		},
	},
);

export interface InputProps
	extends React.ComponentProps<"input">,
		VariantProps<typeof inputVariants> {}

function Input({ className, type, variant, ...props }: InputProps) {
	return (
		<input
			className={cn(inputVariants({ variant }), className)}
			data-slot="input"
			type={type}
			{...props}
		/>
	);
}

export { Input };
