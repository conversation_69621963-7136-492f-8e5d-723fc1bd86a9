"use client";

import { <PERSON><PERSON><PERSON>ircle, RefreshCw } from "lucide-react";
import { useTranslations } from "next-intl";
import type React from "react";
import { Component, type ErrorInfo, type ReactNode } from "react";
import { Button } from "@/components/ui/button";

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  translations?: {
    chatError: string;
    chatErrorDescription: string;
    chatErrorDetails: string;
    tryAgain: string;
  };
}

interface State {
  hasError: boolean;
  error?: Error;
}

/**
 * Error Boundary for AI Chat Components
 * Catches and handles errors in the chat interface gracefully
 */
export class ChatErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(_error: Error, _errorInfo: ErrorInfo) {
    // Log error for monitoring only in development
    if (process.env.NODE_ENV === "development") {
    }

    // In production, you might want to send this to an error reporting service
    if (process.env.NODE_ENV === "production") {
      // Example: Sentry.captureException(error, { extra: errorInfo });
    }
  }

  handleReset = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default fallback UI
      return (
        <div className="flex h-full flex-col items-center justify-center p-8 text-center">
          <div className="max-w-md rounded-lg border border-red-200 bg-red-50 p-6 dark:border-red-800 dark:bg-red-950/20">
            <AlertCircle className="mx-auto mb-4 h-12 w-12 text-red-500" />
            <h3 className="mb-2 font-semibold text-lg text-red-800 dark:text-red-200">
              {this.props.translations?.chatError || "Chat Error"}
            </h3>
            <p className="mb-4 text-red-700 text-sm dark:text-red-300">
              {this.props.translations?.chatErrorDescription ||
                "Something went wrong with the chat interface. This is usually a temporary issue."}
            </p>
            {process.env.NODE_ENV === "development" && this.state.error && (
              <details className="mb-4 text-left text-red-600 text-xs dark:text-red-400">
                <summary className="cursor-pointer font-medium">
                  {this.props.translations?.chatErrorDetails || "Error Details"}
                </summary>
                <pre className="mt-2 overflow-auto rounded bg-red-100 p-2 text-xs dark:bg-red-900/20">
                  {this.state.error.message}
                  {this.state.error.stack && `\n\n${this.state.error.stack}`}
                </pre>
              </details>
            )}
            <Button
              className="bg-red-600 text-white hover:bg-red-700"
              onClick={this.handleReset}
              size="sm"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              {this.props.translations?.tryAgain || "Try Again"}
            </Button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Hook-based Error Boundary wrapper for functional components with translations
 */
export function ChatErrorBoundaryWithTranslations({ children }: { children: ReactNode }) {
  const t = useTranslations("admin");
  const tCommon = useTranslations("common");

  const translations = {
    chatError: t("chatError"),
    chatErrorDescription: t("chatErrorDescription"),
    chatErrorDetails: t("chatErrorDetails"),
    tryAgain: tCommon("retry"),
  };

  return <ChatErrorBoundary translations={translations}>{children}</ChatErrorBoundary>;
}

/**
 * Hook-based Error Boundary wrapper for functional components
 */
export function withChatErrorBoundary<P extends object>(Component: React.ComponentType<P>) {
  return function WrappedComponent(props: P) {
    return (
      <ChatErrorBoundaryWithTranslations>
        <Component {...props} />
      </ChatErrorBoundaryWithTranslations>
    );
  };
}
