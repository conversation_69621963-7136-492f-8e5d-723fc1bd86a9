"use client";

import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { AdminSuburbAutocomplete } from "@/components/admin/suburb-autocomplete";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import {
	Drawer,
	DrawerContent,
	DrawerDescription,
	DrawerHeader,
	DrawerTitle,
} from "@/components/ui/drawer";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { useIsMobile } from "@/hooks/use-mobile";
import { useMessage } from "@/hooks/useMessage";
import type { IHouseholdWithDetails } from "@/lib/db/households";
import { cn } from "@/lib/utils";
import { formatDateForDatabase } from "@/lib/utils/date-time";
import { zodResolver } from "@/lib/utils/zod-resolver-compat";
import {
	type ClientHouseholdRegistrationFormValues,
	createClientHouseholdRegistrationSchema,
} from "@/lib/validation/client/census-client";
import type { ICensusYear } from "@/types";

// Use the database interface directly for type consistency
type HouseholdWithDetails = IHouseholdWithDetails;

// Interface for API request data
interface UpdateHouseholdApiData {
	suburb: string;
	head_first_name: string;
	head_last_name: string;
	head_date_of_birth: string;
	head_mobile_phone: string;
	head_gender: "male" | "female" | "other";
}

interface EditHouseholdDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	household: HouseholdWithDetails;
	censusYears: ICensusYear[];
	onHouseholdUpdated: () => void;
}

export function EditHouseholdDialog({
	open,
	onOpenChange,
	household,
	onHouseholdUpdated,
}: EditHouseholdDialogProps) {
	const { showError } = useMessage();
	const isMobile = useIsMobile();
	const t = useTranslations("admin");
	const tCommon = useTranslations("common");
	const tForms = useTranslations("forms");
	const tValidation = useTranslations("validation");
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [dobPopoverOpen, setDobPopoverOpen] = useState(false);

	// Create client-side validation schema with translations
	const householdSchema = createClientHouseholdRegistrationSchema(tValidation);

	// Initialize form with household data (mapping API fields to form fields)
	const {
		register,
		handleSubmit,
		formState: { errors },
		reset,
		setValue,
		watch,
		control,
	} = useForm<ClientHouseholdRegistrationFormValues>({
		resolver: zodResolver(householdSchema),
		defaultValues: {
			suburb: household.suburb,
			headFirstName: household.headName?.split(" ")[0] || "",
			headLastName: household.headName?.split(" ").slice(1).join(" ") || "",
			headDateOfBirth: household.headDateOfBirth
				? new Date(household.headDateOfBirth)
				: undefined,
			mobilePhone: household.headContact || "",
			gender: household.headGender || "male", // Use the headGender from the API or default to 'male'
		},
	});

	// Watch the date of birth field for display
	const watchedDateOfBirth = watch("headDateOfBirth");

	// Update form values when household changes
	useEffect(() => {
		reset({
			suburb: household.suburb,
			headFirstName: household.headName?.split(" ")[0] || "",
			headLastName: household.headName?.split(" ").slice(1).join(" ") || "",
			headDateOfBirth: household.headDateOfBirth
				? new Date(household.headDateOfBirth)
				: undefined,
			mobilePhone: household.headContact || "",
			gender: household.headGender || "male", // Use the headGender from the API or default to 'male'
		});
	}, [household, reset]);

	// Handle dialog close
	const handleDialogClose = (open: boolean) => {
		if (!(isSubmitting && open)) {
			reset();
			setDobPopoverOpen(false); // Reset popover state

			// Force body to be interactive (admin portal pointer-events fix)
			if (!open) {
				document.body.style.pointerEvents = "auto";
				document.body.style.overflow = "";
				document.body.style.paddingRight = "";
			}

			onOpenChange(open);
		}
	};

	// Handle date selection (preserving local date to prevent timezone shifts)
	const handleDateSelect = (date: Date) => {
		// Format date properly to prevent off-by-one errors due to timezone conversion
		const formattedDate = formatDateForDatabase(date);
		setValue("headDateOfBirth", new Date(formattedDate));
	};

	// Ensure body is interactive when dialog is closed (admin portal pointer-events fix)
	useEffect(() => {
		if (!open) {
			// Force pointer-events to be enabled
			document.body.style.pointerEvents = "auto";
			// Remove any overflow hidden that might have been added
			document.body.style.overflow = "";
			// Remove any padding right that might have been added to compensate for scrollbar
			document.body.style.paddingRight = "";
		}

		// Cleanup function to ensure proper cleanup when component unmounts
		return () => {
			// Force pointer-events to be enabled
			document.body.style.pointerEvents = "auto";
			// Remove any overflow hidden that might have been added
			document.body.style.overflow = "";
			// Remove any padding right that might have been added to compensate for scrollbar
			document.body.style.paddingRight = "";
		};
	}, [open]);

	// Handle form submission
	const onSubmit = async (data: ClientHouseholdRegistrationFormValues) => {
		try {
			setIsSubmitting(true);

			// Transform data to match API expectations (map form fields to API fields)
			const apiData: UpdateHouseholdApiData = {
				suburb: data.suburb,
				head_first_name: data.headFirstName,
				head_last_name: data.headLastName,
				head_date_of_birth: data.headDateOfBirth
					? formatDateForDatabase(data.headDateOfBirth)
					: "",
				head_mobile_phone: data.mobilePhone,
				head_gender: data.gender,
			};

			// Submit form data to API
			const response = await fetch(`/api/admin/households/${household.id}`, {
				method: "PUT",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(apiData),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || tCommon("failedToUpdateHousehold"));
			}

			// Reset form and close dialog
			onHouseholdUpdated();
		} catch (error) {
			console.error("Error updating household:", error);
			showError("failedToUpdateHousehold");
		} finally {
			setIsSubmitting(false);
		}
	};

	// Shared form content component for both mobile and desktop
	const FormContent = () => (
		<div className="grid gap-4 py-4">
			{/* Household Information */}
			<div className="mb-2">
				<h3 className="font-medium text-sm">
					{tCommon("householdInformation")}
				</h3>
			</div>

			<AdminSuburbAutocomplete
				control={control}
				error={errors.suburb}
				label={tCommon("suburb")}
				name="suburb"
				placeholder={tForms("searchForSuburb")}
				required
			/>

			{/* Household Head Information */}
			<div className="mt-4 mb-2">
				<h3 className="font-medium text-sm">
					{tCommon("householdHeadInformation")}
				</h3>
			</div>

			<div className="grid grid-cols-2 gap-4">
				<div className="grid gap-2">
					<Label
						className={errors.headFirstName ? "text-destructive" : ""}
						htmlFor="headFirstName"
					>
						{tForms("firstName")} <span className="text-destructive">*</span>
					</Label>
					<Input
						id="headFirstName"
						{...register("headFirstName")}
						className={errors.headFirstName ? "border-destructive" : ""}
						placeholder={tForms("enterFirstName")}
					/>
					{errors.headFirstName && (
						<p className="text-destructive text-xs">
							{errors.headFirstName.message}
						</p>
					)}
				</div>

				<div className="grid gap-2">
					<Label
						className={errors.headLastName ? "text-destructive" : ""}
						htmlFor="headLastName"
					>
						{tForms("lastName")} <span className="text-destructive">*</span>
					</Label>
					<Input
						id="headLastName"
						{...register("headLastName")}
						className={errors.headLastName ? "border-destructive" : ""}
						placeholder={tForms("enterLastName")}
					/>
					{errors.headLastName && (
						<p className="text-destructive text-xs">
							{errors.headLastName.message}
						</p>
					)}
				</div>
			</div>

			{/* Date of Birth + Gender Row */}
			<div className="grid grid-cols-2 gap-4">
				<div className="grid gap-2">
					<Label className={errors.headDateOfBirth ? "text-destructive" : ""}>
						{tForms("householdHeadDateOfBirth")}{" "}
						<span className="text-destructive">*</span>
					</Label>
					<Popover onOpenChange={setDobPopoverOpen} open={dobPopoverOpen}>
						<PopoverTrigger asChild>
							<Button
								className={cn(
									"w-full justify-start text-left font-normal",
									!watchedDateOfBirth && "text-muted-foreground",
									errors.headDateOfBirth && "border-destructive",
								)}
								type="button"
								variant="outline"
							>
								<CalendarIcon className="mr-2 h-4 w-4 flex-shrink-0" />
								<span className="truncate">
									{watchedDateOfBirth ? (
										format(new Date(watchedDateOfBirth), "dd/MM/yyyy")
									) : (
										<span>{tForms("selectDateOfBirthPlaceholder")}</span>
									)}
								</span>
							</Button>
						</PopoverTrigger>
						<PopoverContent
							align="start"
							className="w-auto p-0"
							onWheel={(e) => {
								// Allow wheel events to propagate to calendar for proper scrolling
								e.stopPropagation();
							}}
							style={{ pointerEvents: "auto" }}
						>
							<Calendar
								date={watchedDateOfBirth ? new Date(watchedDateOfBirth) : null}
								onDateChange={handleDateSelect}
								onDone={() => setDobPopoverOpen(false)} // Hide the footer in Calendar to match admin portal pattern
								preventFutureDates={true} // Hide the Today button in Calendar
								showFooter={false} // This prevents auto-scrolling and allows mouse wheel scrolling
								showTodayButton={false} // Prevent future dates for date of birth
								standalone={false}
							/>
							{/* Custom footer with Today and Done buttons - matches admin portal pattern */}
							<div className="flex justify-between border-t p-3">
								<Button
									className="cursor-pointer"
									onClick={() => {
										const today = new Date();
										const formattedDate = formatDateForDatabase(today);
										setValue("headDateOfBirth", new Date(formattedDate));
									}}
									size="sm"
									variant="ghost"
								>
									{tCommon("today")}
								</Button>
								<Button
									className="cursor-pointer"
									onClick={() => setDobPopoverOpen(false)}
									size="sm"
									variant="ghost"
								>
									{tCommon("done")}
								</Button>
							</div>
						</PopoverContent>
					</Popover>
					{errors.headDateOfBirth && (
						<p className="text-destructive text-xs">
							{errors.headDateOfBirth.message}
						</p>
					)}
				</div>

				<div className="grid gap-2">
					<Label
						className={errors.gender ? "text-destructive" : ""}
						htmlFor="gender"
					>
						{tForms("gender")} <span className="text-destructive">*</span>
					</Label>
					<Select
						onValueChange={(value) =>
							setValue("gender", value as "male" | "female" | "other")
						}
						value={watch("gender")}
					>
						<SelectTrigger
							className={errors.gender ? "border-destructive" : ""}
							id="gender"
						>
							<SelectValue placeholder={tForms("selectGender")} />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="male">{tForms("male")}</SelectItem>
							<SelectItem value="female">{tForms("female")}</SelectItem>
							<SelectItem value="other">{tForms("other")}</SelectItem>
						</SelectContent>
					</Select>
					{errors.gender && (
						<p className="text-destructive text-xs">{errors.gender.message}</p>
					)}
				</div>
			</div>

			{/* Mobile Phone Row */}
			<div className="grid gap-2">
				<Label
					className={errors.mobilePhone ? "text-destructive" : ""}
					htmlFor="mobilePhone"
				>
					{tForms("mobilePhone")} <span className="text-destructive">*</span>
				</Label>
				<Input
					id="mobilePhone"
					{...register("mobilePhone")}
					className={errors.mobilePhone ? "border-destructive" : ""}
					placeholder={tForms("enter10digitMobileNumber")}
				/>
				{errors.mobilePhone && (
					<p className="text-destructive text-xs">
						{errors.mobilePhone.message}
					</p>
				)}
			</div>
		</div>
	);

	// Action buttons component for both mobile and desktop
	const ActionButtons = () => (
		<>
			<Button
				disabled={isSubmitting}
				onClick={() => handleDialogClose(false)}
				type="button"
				variant="outline"
			>
				{tCommon("cancel")}
			</Button>
			<Button disabled={isSubmitting} type="submit" variant="default">
				{isSubmitting ? tCommon("saving") : tCommon("save")}
			</Button>
		</>
	);

	// Mobile implementation using Drawer (native swipe-to-dismiss from bottom)
	// Features: Visual handle bar, smooth swipe gestures, and hidden scrollbar
	if (isMobile) {
		return (
			<Drawer onOpenChange={handleDialogClose} open={open}>
				<DrawerContent className="flex max-h-[80vh] min-h-[60vh] flex-col">
					<DrawerHeader className="pb-4 text-left">
						<DrawerTitle className="font-semibold text-lg">
							{tCommon("editHousehold")}
						</DrawerTitle>
						<DrawerDescription className="text-muted-foreground">
							{tCommon("updateHouseholdInformationForId", {
								id: household.id.toString(),
							})}
						</DrawerDescription>
					</DrawerHeader>

					<form
						className="flex flex-1 flex-col"
						onSubmit={handleSubmit(onSubmit)}
					>
						<div className="scrollbar-hide flex-1 overflow-y-auto px-4">
							<FormContent />
						</div>

						<div className="border-t px-4 pt-4 pb-4">
							<div className="flex justify-end gap-2">
								<ActionButtons />
							</div>
						</div>
					</form>
				</DrawerContent>
			</Drawer>
		);
	}

	// Desktop implementation using Dialog (unchanged)
	return (
		<Dialog onOpenChange={handleDialogClose} open={open}>
			<DialogContent className="sm:max-w-[500px]">
				<DialogHeader>
					<DialogTitle>{tCommon("editHousehold")}</DialogTitle>
					<DialogDescription>
						{tCommon("updateHouseholdInformationForId", {
							id: household.id.toString(),
						})}
					</DialogDescription>
				</DialogHeader>

				<form onSubmit={handleSubmit(onSubmit)}>
					<FormContent />

					<DialogFooter>
						<ActionButtons />
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
}
