"use client";

import { useTranslations } from "next-intl";
import React from "react";
import type { ChartData } from "../../../../lib/utils/chart-data-formatter";
import { ErrorBoundaryWrapper } from "../../../../src/components/shared/ErrorBoundaryWrapper";

// Lazy components hoisted to module scope to avoid recreation on every render
const LazyExportButton = React.lazy(() => import("./export-button"));
const LazyVirtualizedChart = React.lazy(() => import("./virtualized-chart"));

// Base chart component interface
export interface BaseChartProps {
	data: ChartData;
	isAnimationActive?: boolean;
	className?: string;
}

// Chart component registry type
export type ChartComponent = React.ComponentType<BaseChartProps>;

// Chart registry for modular chart system
export interface ChartRegistry {
	[key: string]: ChartComponent;
}

// Default chart registry - will be populated by individual chart components
export const chartRegistry: ChartRegistry = {};

// Register a chart component
export function registerChart(type: string, component: ChartComponent) {
	if (process.env.NODE_ENV !== "production" && chartRegistry[type]) {
		return;
	}
	chartRegistry[type] = component;
}

// Get a chart component by type
export function getChartComponent(type: string): ChartComponent | null {
	return chartRegistry[type] || null;
}

// Get all available chart types
export function getAvailableChartTypes(): string[] {
	return Object.keys(chartRegistry);
}

// Chart factory component
export interface ChartFactoryProps extends BaseChartProps {
	fallback?: React.ComponentType<BaseChartProps>;
}

export function ChartFactory({
	data,
	isAnimationActive,
	className,
	fallback,
}: ChartFactoryProps) {
	const ChartComponent = getChartComponent(data.type);

	if (!ChartComponent) {
		if (fallback) {
			const FallbackComponent = fallback;
			return (
				<FallbackComponent
					className={className}
					data={data}
					isAnimationActive={isAnimationActive}
				/>
			);
		}

		return (
			<div className="flex h-64 items-center justify-center rounded-lg bg-muted">
				<div className="text-center">
					<p className="mb-2 text-muted-foreground">
						Chart type &quot;{data.type}&quot; not supported
					</p>
					<p className="text-muted-foreground/80 text-xs">
						Available types: {getAvailableChartTypes().join(", ")}
					</p>
				</div>
			</div>
		);
	}

	return (
		<ChartComponent
			className={className}
			data={data}
			isAnimationActive={isAnimationActive}
		/>
	);
}

// Chart wrapper with error boundary and enhanced features
export interface ChartWrapperProps extends BaseChartProps {
	title?: string;
	description?: string;
	actions?: React.ReactNode;
	enableExport?: boolean;
	enableVirtualization?: boolean;
	showDataSummary?: boolean;
}

// Chart-specific error fallback component
function ChartErrorFallback({
	error,
	resetError,
}: {
	error: Error;
	resetError: () => void;
}) {
	return (
		<div className="flex h-64 items-center justify-center rounded-lg border border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20">
			<div className="text-center">
				<p className="mb-2 text-red-600 dark:text-red-400">
					Failed to render chart
				</p>
				<p className="mb-3 text-red-500 text-xs dark:text-red-500">
					{error.message}
				</p>
				<button
					className="rounded bg-red-100 px-3 py-1 text-red-700 text-xs transition-colors hover:bg-red-200 dark:bg-red-900/40 dark:text-red-300 dark:hover:bg-red-900/60"
					onClick={resetError}
				>
					Try Again
				</button>
			</div>
		</div>
	);
}

export function ChartWrapper({
	data,
	isAnimationActive,
	className,
	title,
	description,
	actions,
	enableExport = false,
	enableVirtualization = false,
	showDataSummary = false,
}: ChartWrapperProps) {
	const t = useTranslations("admin");
	const chartRef = React.useRef<HTMLDivElement>(null);

	return (
		<div
			className={`rounded-lg border border-border bg-card ${className || ""}`}
		>
			{(title || description || actions || enableExport) && (
				<div className="border-border border-b p-4">
					<div className="flex items-center justify-between">
						<div>
							{title && (
								<h3 className="font-semibold text-lg text-slate-900 dark:text-slate-100">
									{title}
								</h3>
							)}
							{description && (
								<p className="mt-1 text-slate-600 text-sm dark:text-slate-400">
									{description}
								</p>
							)}
						</div>
						<div className="flex items-center gap-2">
							{enableExport && (
								<React.Suspense fallback={<div>Loading...</div>}>
									<LazyExportButton chartElementRef={chartRef} data={data} />
								</React.Suspense>
							)}
							{actions}
						</div>
					</div>
				</div>
			)}

			<div className="p-6 px-8" ref={chartRef}>
				<ErrorBoundaryWrapper
					fallback={ChartErrorFallback}
					maxRetries={2}
					onError={(_error, _errorInfo) => {}}
				>
					{enableVirtualization ? (
						<React.Suspense fallback={<div>{t("loadingChart")}</div>}>
							<LazyVirtualizedChart
								className="w-full"
								data={data}
								isAnimationActive={isAnimationActive}
							/>
						</React.Suspense>
					) : (
						<ChartFactory
							className="w-full"
							data={data}
							isAnimationActive={isAnimationActive}
						/>
					)}
				</ErrorBoundaryWrapper>
			</div>

			{showDataSummary && (
				<div className="px-4 pb-4">
					<div className="rounded bg-muted p-3 text-muted-foreground text-xs">
						<div className="grid grid-cols-3 gap-4">
							<div>
								<span className="font-medium">Records:</span> {data.data.length}
							</div>
							<div>
								<span className="font-medium">Type:</span> {data.type}
							</div>
							<div>
								<span className="font-medium">Updated:</span>{" "}
								{new Date().toLocaleDateString()}
							</div>
						</div>
					</div>
				</div>
			)}
		</div>
	);
}

// Import comprehensive validation system
// Note: validateChartDataComprehensive is exported for use in other components

// Export types for use in chart components
export type { ChartData };
