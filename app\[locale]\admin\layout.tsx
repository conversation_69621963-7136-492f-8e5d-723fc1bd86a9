import type { Metadata } from "next";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { AdminLayoutClient } from "@/components/admin/admin-layout-client";
import { authOptions } from "@/lib/auth/auth-options";

// Force dynamic rendering to ensure fresh session data
export const dynamic = "force-dynamic";

// Define metadata for admin section
export const generateMetadata = async ({
	params,
}: {
	params: Promise<{ path?: string[] }>;
}): Promise<Metadata> => {
	// Await params before accessing properties (Next.js 15 requirement)
	const { path } = await params;
	const t = await getTranslations("metadata");

	// If the current path is /admin/login
	if (path?.[0] === "login") {
		return {
			title: t("adminLoginTitle"),
			description: t("adminLoginDescription"),
		};
	}

	// Default metadata for other admin pages
	return {
		title: t("adminPortalTitle"),
		description: t("adminPortalDescription"),
	};
};

export default async function AdminLayout({
	children,
	params,
}: {
	children: React.ReactNode;
	params: Promise<{ path?: string[] }>;
}) {
	// Await params to satisfy Next.js 15 requirements (even though we don't use them here)
	await params;
	// For admin pages (except login), use the same authentication method as pages
	// This ensures consistency between layout and page session data
	let userName = "Admin";

	try {
		// Use the same session retrieval method as requireAdmin() for consistency
		const session = await getServerSession(authOptions);

		// Only set userName if we have a valid admin session
		if (session?.user?.role === "admin" && session.user.name) {
			userName = session.user.name;
		}
	} catch (error) {
		// Fallback gracefully - the page-level requireAdmin() will handle redirects
	}

	return <AdminLayoutClient userName={userName}>{children}</AdminLayoutClient>;
}
