"use client";

import { Check, Copy } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { AccountManagementSection } from "@/components/census/account-management-section";
import { Skeleton } from "@/components/ui/skeleton";
import { StatusBadge } from "@/components/ui/status-badge";
import { useCensusAuth } from "@/hooks/useCensusAuth";
import { useMessage } from "@/hooks/useMessage";

interface AccountClientProps {
  code: string;
  initialSessionData: {
    id: string;
    name: string;
    code: string;
    censusYearId: string;
    householdId?: string;
  };
}

/**
 * AccountClient component
 *
 * Client-side component for the Account page that handles:
 * - Session management
 * - Loading states
 * - Rendering the account management UI
 */
export function AccountClient({ code, initialSessionData }: AccountClientProps) {
  const { session, status, updateSession } = useCensusAuth();
  const { showSuccess, showError } = useMessage();
  const tCensus = useTranslations("census");
  const tCommon = useTranslations("common");
  const tNotifications = useTranslations("notifications");
  const tNavigation = useTranslations("navigation");
  const tTables = useTranslations("tables");
  const [isLoading, setIsLoading] = useState(true);
  const [isCopied, setIsCopied] = useState(false);

  // Handle copying code to clipboard with industry-standard UI pattern
  const handleCopyCode = async () => {
    try {
      await navigator.clipboard.writeText(code);

      // Show success state immediately
      setIsCopied(true);
      showSuccess("uniqueCodeCopied");

      // Revert to copy icon after 2 seconds
      setTimeout(() => {
        setIsCopied(false);
      }, 2000);
    } catch (_error) {
      showError("failedToCopyCode");
    }
  };

  // Effect to handle session loading
  useEffect(() => {
    const checkSession = async () => {
      try {
        if (status === "loading") {
          return;
        }

        // If session is available, we're done loading
        if (status === "authenticated" && session) {
          setIsLoading(false);
          return;
        }

        // If no session but we have initial data, try to refresh the session
        if (status === "unauthenticated" && initialSessionData) {
          await updateSession();
          setIsLoading(false);
          return;
        }

        // If we get here, we're done loading regardless of the outcome
        setIsLoading(false);
      } catch (_error) {
        setIsLoading(false);
      }
    };

    checkSession();
  }, [status, session, initialSessionData, updateSession]);

  // Render loading skeleton while checking session
  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex min-h-[calc(100vh-16rem)] flex-col items-center justify-center px-4 py-8 md:px-0">
          <div className="mx-auto w-full max-w-2xl space-y-12">
            {/* Header skeleton */}
            <div className="flex items-center gap-4">
              <Skeleton className="h-12 w-12 rounded-full" />
              <Skeleton className="h-10 w-64" /> {/* Adjusted width for larger title */}
            </div>

            {/* Profile Information Card Skeleton */}
            <div className="rounded-xl border border-border/50 bg-card p-6 shadow-lg sm:p-8">
              <div className="mb-6 flex items-center gap-3">
                <Skeleton className="h-7 w-7 rounded-full" /> {/* Icon skeleton */}
                <Skeleton className="h-8 w-56" /> {/* Title skeleton */}
              </div>
              <Skeleton className="mb-8 h-4 w-4/5" /> {/* Subtitle/description skeleton */}
              <div className="space-y-6">
                {/* First row skeleton: Household and Household ID side by side */}
                <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
                  <div className="rounded-lg border border-muted/30 bg-muted/20 p-4">
                    <Skeleton className="mb-2 h-4 w-28" /> {/* Label skeleton */}
                    <Skeleton className="h-6 w-48" /> {/* Value skeleton */}
                  </div>

                  <div className="rounded-lg border border-muted/30 bg-muted/20 p-4">
                    <Skeleton className="mb-2 h-4 w-36" /> {/* Label skeleton */}
                    <Skeleton className="h-6 w-full" /> {/* Value skeleton */}
                  </div>
                </div>

                {/* Second row skeleton: Unique Code */}
                <div className="rounded-lg border border-muted/30 bg-muted/20 p-4">
                  <div className="flex flex-col justify-between gap-2 sm:flex-row sm:items-center">
                    <div className="w-full">
                      <Skeleton className="mb-2 h-4 w-32" /> {/* Label skeleton */}
                      <Skeleton className="h-6 w-full" /> {/* Value skeleton */}
                    </div>
                    <Skeleton className="mt-2 h-6 w-20 rounded-full sm:mt-0" />{" "}
                    {/* Badge skeleton */}
                  </div>
                </div>
              </div>
            </div>

            {/* Account Management Section Skeleton */}
            <div className="rounded-xl border border-border/50 bg-card p-6 shadow-lg sm:p-8">
              <div className="mb-6 flex items-center gap-3">
                <Skeleton className="h-7 w-7 rounded-full" /> {/* Icon skeleton */}
                <Skeleton className="h-8 w-48" /> {/* Title skeleton */}
              </div>
              <Skeleton className="mb-8 h-4 w-full" /> {/* Subtitle/description skeleton */}
              <div className="rounded-lg border border-muted/20 bg-muted/10 p-4">
                {" "}
                {/* Changed from destructive to muted */}
                <div className="mb-2 flex items-center gap-2">
                  <Skeleton className="h-5 w-5 rounded-full" />
                  <Skeleton className="h-5 w-32" />
                </div>
                <Skeleton className="mb-4 h-4 w-full" />
                <Skeleton className="h-10 w-36" /> {/* Button skeleton */}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Use session data if available, otherwise fall back to initial data
  const userData = session?.user || initialSessionData;

  return (
    <div className="container mx-auto py-8">
      <div className="flex min-h-[calc(100vh-16rem)] flex-col items-center justify-center px-4 py-8 md:px-0">
        <div className="mx-auto w-full max-w-2xl space-y-12">
          {/* Header Section */}
          <div className="flex items-center gap-4">
            <div className="rounded-full bg-primary/10 p-3">
              <svg
                className="h-7 w-7 text-primary"
                fill="none"
                height="28"
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                width="28"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
                <circle cx="12" cy="7" r="4" />
              </svg>
            </div>
            <h1 className="font-bold text-4xl tracking-tight">{tCensus("yourAccount")}</h1>
          </div>

          {/* Profile Information Section */}
          <div className="rounded-xl border border-border/50 bg-card p-6 shadow-lg sm:p-8">
            <div className="mb-6 flex items-center gap-3">
              <svg
                className="h-6 w-6 text-primary"
                fill="none"
                height="24"
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect height="18" rx="2" width="18" x="3" y="3" />
                <path d="M3 9h18" />
                <path d="M9 21V9" />
              </svg>
              <h2 className="font-semibold text-2xl">{tCommon("profileInformation")}</h2>
            </div>

            <p className="mb-8 text-muted-foreground">{tCensus("yourCensusAccountDetails")}</p>

            <div className="space-y-6">
              {/* First row: Household and Household ID side by side */}
              <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
                <div className="rounded-lg border border-muted/30 bg-muted/20 p-4">
                  <p className="font-medium text-muted-foreground text-sm">
                    {tNavigation("household")}
                  </p>
                  <p className="font-semibold text-foreground text-lg">{userData.name}</p>
                </div>

                {userData.householdId && (
                  <div className="rounded-lg border border-muted/30 bg-muted/20 p-4">
                    <p className="font-medium text-muted-foreground text-sm">
                      {tTables("householdId")}
                    </p>
                    <p className="mt-1 font-mono text-base text-foreground">
                      {userData.householdId}
                    </p>
                  </div>
                )}
              </div>

              {/* Second row: Unique Code */}
              <div className="rounded-lg border border-muted/30 bg-muted/20 p-4">
                <div className="flex flex-col justify-between gap-2 sm:flex-row sm:items-center">
                  <div>
                    <p className="font-medium text-muted-foreground text-sm">
                      {tCommon("uniqueCode")}
                    </p>
                    <p className="font-mono font-semibold text-foreground text-lg">{code}</p>
                  </div>
                  <StatusBadge
                    className="mt-2 transition-all duration-200 sm:mt-0"
                    interactive={true}
                    onClick={handleCopyCode}
                    title={isCopied ? tNotifications("uniqueCodeCopied") : tCensus("clickToCopy")}
                    variant="info"
                  >
                    {isCopied ? <Check className="text-green-600" size={14} /> : <Copy size={14} />}
                    {tCommon("copyId")}
                  </StatusBadge>
                </div>
              </div>
            </div>
          </div>

          {/* Account Management Section */}
          <div className="rounded-xl border border-border/50 bg-card p-6 shadow-lg sm:p-8">
            <div className="mb-6 flex items-center gap-3">
              <svg
                className="h-6 w-6 text-primary"
                fill="none"
                height="24"
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z" />
                <circle cx="12" cy="12" r="3" />
              </svg>
              <h2 className="font-semibold text-2xl">{tCommon("accountSettings")}</h2>
            </div>
            <p className="mb-8 text-muted-foreground">
              {tCommon("manageYourCensusAccountSettings")}
            </p>
            <AccountManagementSection />
          </div>
        </div>
      </div>
    </div>
  );
}
