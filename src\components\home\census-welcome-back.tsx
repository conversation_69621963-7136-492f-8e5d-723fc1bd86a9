"use client";

import { <PERSON><PERSON><PERSON>, Users } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { HomepageAnnouncement } from "@/components/home/<USER>";
import { Button } from "@/components/ui/button";
import { useCensusAuth } from "@/hooks/useCensusAuth";
import type { CensusData } from "@/lib/homepage/placeholder-processor";

interface CensusWelcomeBackProps {
  userName: string;
  userCode: string;
  censusData?: CensusData;
  locale?: string;
}

export function CensusWelcomeBack({
  userName,
  userCode,
  censusData,
  locale = "en-AU",
}: CensusWelcomeBackProps) {
  const t = useTranslations("census");
  const tNav = useTranslations("navigation");
  const tLegal = useTranslations("legal");
  const router = useRouter();

  // Use client-side session for real-time updates
  const { session, isAuthenticated } = useCensusAuth();
  const [displayName, setDisplayName] = useState(userName);
  const [displayCode, setDisplayCode] = useState(userCode);

  // Update display name and code when session changes (real-time updates)
  useEffect(() => {
    if (isAuthenticated && session?.user) {
      setDisplayName(session.user.name);
      setDisplayCode(session.user.code);
    }
  }, [session, isAuthenticated]);

  const handleContinue = () => {
    router.push(`/census/${displayCode}`);
  };

  return (
    <div className="mx-auto w-full max-w-sm">
      <div className="flex flex-col gap-5 p-4 md:p-0">
        {/* Welcome Header */}
        <div className="flex flex-col items-center space-y-1 text-center">
          <h1 className="font-bold text-2xl">{t("welcomeBack", { name: displayName })}</h1>

          {/* Inline announcement between title and subheading */}
          {censusData && <HomepageAnnouncement censusData={censusData} locale={locale} />}

          {/* Bible Quote for returning users - perseverance theme */}
          <div className="w-full max-w-sm">
            <div className="flex items-start gap-3">
              <BookOpen className="mt-1 h-5 w-5 flex-shrink-0 text-primary" />
              <div>
                <p className="mb-1 text-base italic">
                  &quot;Let us run with perseverance the race marked out for us.&quot;
                </p>
                <p className="text-right text-muted-foreground text-sm">— Hebrews 12:1</p>
              </div>
            </div>
          </div>

          <p className="mt-1 text-balance text-muted-foreground text-sm">
            {t("continueWhereYouLeftOff")}
          </p>
        </div>

        <div className="grid gap-5">
          <div className="space-y-3">
            {/* Action Button */}
            <Button className="w-full" onClick={handleContinue}>
              <Users className="mr-2 h-5 w-5" />
              {t("continue")}
            </Button>
          </div>

          {/* Terms and Conditions Divider */}
          <div className="relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-border after:border-t">
            <span className="relative z-10 bg-background px-2 text-muted-foreground text-xs">
              {tLegal("termsAndConditions")}
            </span>
          </div>

          {/* Legal Links */}
          <div className="text-center text-muted-foreground text-xs">
            {tLegal("byClickingContinue")}{" "}
            <Link className="text-primary hover:underline" href="/terms">
              {tLegal("termsOfService")}
            </Link>{" "}
            {tLegal("and")}{" "}
            <Link className="text-primary hover:underline" href="/privacy-policy">
              {tLegal("privacyPolicy")}
            </Link>
            .
          </div>

          {/* Admin Login Link */}
          <div className="text-center text-sm">
            <Link
              className="text-muted-foreground underline underline-offset-4 hover:text-primary"
              href="/admin/login"
            >
              {tNav("adminLogin")}
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
