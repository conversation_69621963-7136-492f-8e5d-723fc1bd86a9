"use client";

import React, {
	createContext,
	type ReactNode,
	useContext,
	useState,
} from "react";

export type Language = {
	code: string;
	label: string;
	nativeLabel: string;
};

export const languages: Language[] = [
	{ code: "en", label: "English", nativeLabel: "English" },
	{ code: "zh-CN", label: "中文", nativeLabel: "中文" },
];

interface LanguageContextType {
	selectedLanguage: Language;
	setLanguage: (language: Language) => void;
}

const LanguageContext = createContext<LanguageContextType | undefined>(
	undefined,
);

export function LanguageProvider({ children }: { children: ReactNode }) {
	const [selectedLanguage, setSelectedLanguage] = useState<Language>(
		languages[0],
	);

	const setLanguage = (language: Language) => {
		setSelectedLanguage(language);
		// Here you would implement the actual language switching logic
		// For example, using next-intl or another i18n library
		console.log(`Language changed to ${language.nativeLabel}`);
	};

	return (
		<LanguageContext.Provider value={{ selectedLanguage, setLanguage }}>
			{children}
		</LanguageContext.Provider>
	);
}

export function useLanguage() {
	const context = useContext(LanguageContext);
	if (context === undefined) {
		throw new Error("useLanguage must be used within a LanguageProvider");
	}
	return context;
}
