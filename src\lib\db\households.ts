/**
 * Household Database Operations with Prisma - Consolidated Professional Implementation
 *
 * This file consolidates households.ts and households-prisma.ts into a single source of truth.
 * Features:
 * - Consistent data structures (IHouseholdWithDetails for UI consumption)
 * - Professional Prisma ORM implementation (no raw SQL)
 * - Comprehensive search functionality
 * - Type-safe operations throughout
 * - Proper data transformation utilities
 */

import type {
	CensusYear,
	Household,
	HouseholdMember,
	Member,
	Prisma,
} from "@prisma/client";
import { prisma } from "./prisma";

// Type definitions for complex household data
export interface IHouseholdWithRelations extends Household {
	firstCensusYear?: CensusYear;
	lastCensusYear?: CensusYear;
	householdMembers?: (HouseholdMember & {
		member: Member;
		censusYear: CensusYear;
	})[];
	memberCount?: number;
	headMember?: Member | null;
	uniqueCode?: string | null;
}

export interface IHouseholdWithDetails extends Household {
	firstCensusYear: number;
	lastCensusYear: number;
	memberCount: number;
	headName: string | null;
	headContact: string | null;
	headDateOfBirth: string | null;
	headGender: "male" | "female" | "other" | null;
	uniqueCode: string | null;
}

/**
 * Get all households with optional filtering and pagination
 */
export async function getHouseholds(
	options: {
		limit?: number;
		offset?: number;
		searchTerm?: string;
		censusYearId?: number;
		includeMembers?: boolean;
		includeDetails?: boolean;
	} = {},
): Promise<IHouseholdWithRelations[]> {
	const {
		limit = 100,
		offset = 0,
		searchTerm = "",
		censusYearId,
		includeMembers = false,
		includeDetails = false,
	} = options;

	// Build include object based on options
	const include: Record<string, unknown> = {};

	if (includeDetails || includeMembers) {
		include.firstCensusYear = true;
		include.lastCensusYear = true;
	}

	if (includeMembers) {
		include.householdMembers = {
			include: {
				member: true,
				censusYear: true,
			},
			where: censusYearId ? { censusYearId } : { isCurrent: true },
		};
	}

	// Build where clause for search
	const where = searchTerm
		? {
				suburb: { contains: searchTerm, mode: "insensitive" as const },
			}
		: {};

	// Execute query with Prisma
	const households = await prisma.household.findMany({
		where,
		include: Object.keys(include).length > 0 ? include : undefined,
		take: limit,
		skip: offset,
		orderBy: [{ suburb: "asc" }, { id: "asc" }],
	});

	return households;
}

/**
 * Get household by ID with optional relationships
 */
export async function getHouseholdById(
	id: number,
	options: {
		includeMembers?: boolean;
		includeCensusYears?: boolean;
		censusYearId?: number;
	} = {},
): Promise<IHouseholdWithRelations | null> {
	const {
		includeMembers = false,
		includeCensusYears = false,
		censusYearId,
	} = options;

	// Build include object based on options
	const include: Record<string, unknown> = {};

	if (includeCensusYears) {
		include.firstCensusYear = true;
		include.lastCensusYear = true;
	}

	if (includeMembers) {
		include.householdMembers = {
			include: {
				member: true,
				censusYear: true,
			},
			where: censusYearId ? { censusYearId } : { isCurrent: true },
		};
	}

	// Execute query with Prisma
	const household = await prisma.household.findUnique({
		where: { id },
		include: Object.keys(include).length > 0 ? include : undefined,
	});

	return household;
}

/**
 * Create a new household
 */
export async function createHousehold(data: {
	suburb: string;
	firstCensusYearId: number;
	lastCensusYearId: number;
}): Promise<Household> {
	return await prisma.household.create({
		data: {
			suburb: data.suburb,
			firstCensusYearId: data.firstCensusYearId,
			lastCensusYearId: data.lastCensusYearId,
		},
	});
}

/**
 * Update an existing household
 */
export async function updateHousehold(
	id: number,
	data: Partial<{
		suburb: string;
		firstCensusYearId: number;
		lastCensusYearId: number;
	}>,
): Promise<Household | null> {
	try {
		return await prisma.household.update({
			where: { id },
			data,
		});
	} catch (error) {
		console.error("Error updating household:", error);
		return null;
	}
}

/**
 * Delete a household and all related records
 */
export async function deleteHousehold(id: number): Promise<boolean> {
	try {
		await prisma.household.delete({
			where: { id },
		});
		return true;
	} catch (error) {
		console.error("Error deleting household:", error);
		return false;
	}
}

/**
 * Get households by suburb
 */
export async function getHouseholdsBySuburb(
	suburb: string,
): Promise<IHouseholdWithRelations[]> {
	return await prisma.household.findMany({
		where: {
			suburb: { equals: suburb, mode: "insensitive" },
		},
		include: {
			firstCensusYear: true,
			lastCensusYear: true,
		},
		orderBy: { id: "asc" },
	});
}

/**
 * Get households by census year
 */
export async function getHouseholdsByCensusYear(
	censusYearId: number,
): Promise<IHouseholdWithRelations[]> {
	return await prisma.household.findMany({
		where: {
			householdMembers: {
				some: {
					censusYearId,
				},
			},
		},
		include: {
			firstCensusYear: true,
			lastCensusYear: true,
			householdMembers: {
				where: { censusYearId },
				include: {
					member: true,
					censusYear: true,
				},
			},
		},
		orderBy: { suburb: "asc" },
	});
}

/**
 * @deprecated Use getHouseholdsWithDetails instead
 * This function has been replaced with a Prisma-based implementation
 * that provides better type safety and eliminates BigInt serialization issues.
 */

/**
 * Transform Prisma household result to flat IHouseholdWithDetails structure
 */
function transformToHouseholdWithDetails(household: {
	id: number;
	suburb: string;
	firstCensusYearId: number;
	lastCensusYearId: number;
	createdAt: Date;
	updatedAt: Date;
	firstCensusYear?: { year: number };
	lastCensusYear?: { year: number };
	householdMembers?: Array<{
		relationship: string;
		member: {
			firstName: string;
			lastName: string;
			dateOfBirth: Date | null;
			mobilePhone: string;
			gender: "male" | "female" | "other";
		};
	}>;
	uniqueCodes?: Array<{ code: string }>;
}): IHouseholdWithDetails {
	// Find the head member
	const headMember = household.householdMembers?.find(
		(hm) => hm.relationship === "head",
	);
	const headInfo = headMember?.member;

	// Get the latest unique code
	const latestUniqueCode = household.uniqueCodes?.[0]?.code || null;

	return {
		id: household.id,
		suburb: household.suburb,
		firstCensusYearId: household.firstCensusYearId,
		lastCensusYearId: household.lastCensusYearId,
		createdAt: household.createdAt,
		updatedAt: household.updatedAt,
		firstCensusYear: household.firstCensusYear?.year || 0,
		lastCensusYear: household.lastCensusYear?.year || 0,
		memberCount: household.householdMembers?.length || 0,
		headName: headInfo ? `${headInfo.firstName} ${headInfo.lastName}` : null,
		headContact: headInfo?.mobilePhone || null,
		headDateOfBirth: headInfo?.dateOfBirth
			? headInfo.dateOfBirth.toISOString().split("T")[0]
			: null,
		headGender: headInfo?.gender || null,
		uniqueCode: latestUniqueCode,
	};
}

/**
 * Get households with detailed information (for admin dashboard) - Professional Prisma Implementation
 */
export async function getHouseholdsWithDetails(
	options: {
		limit?: number;
		offset?: number;
		searchTerm?: string;
		censusYearId?: number;
	} = {},
): Promise<IHouseholdWithDetails[]> {
	const { limit = 50, offset = 0, searchTerm = "", censusYearId } = options;

	// Build where conditions properly for Prisma
	const whereConditions: Prisma.HouseholdWhereInput = {};

	// Add search conditions if provided
	if (searchTerm) {
		whereConditions.OR = [
			// Search by suburb
			{ suburb: { contains: searchTerm, mode: "insensitive" as const } },
			// Search by head member name
			{
				householdMembers: {
					some: {
						relationship: "head" as const,
						isCurrent: true,
						member: {
							OR: [
								{
									firstName: {
										contains: searchTerm,
										mode: "insensitive" as const,
									},
								},
								{
									lastName: {
										contains: searchTerm,
										mode: "insensitive" as const,
									},
								},
							],
						},
					},
				},
			},
			// Search by head member contact
			{
				householdMembers: {
					some: {
						relationship: "head" as const,
						isCurrent: true,
						member: {
							mobilePhone: { contains: searchTerm },
						},
					},
				},
			},
			// Search by unique code
			{
				uniqueCodes: {
					some: {
						code: { contains: searchTerm, mode: "insensitive" as const },
					},
				},
			},
		];
	}

	// Add census year filter if provided
	if (censusYearId) {
		whereConditions.householdMembers = {
			some: { censusYearId },
		};
	}

	// Execute Prisma query with proper includes
	const households = await prisma.household.findMany({
		where:
			Object.keys(whereConditions).length > 0 ? whereConditions : undefined,
		include: {
			firstCensusYear: true,
			lastCensusYear: true,
			householdMembers: {
				where: censusYearId ? { censusYearId } : { isCurrent: true },
				include: {
					member: true,
				},
			},
			uniqueCodes: {
				orderBy: { createdAt: "desc" },
				take: 1,
			},
		},
		take: limit,
		skip: offset,
		orderBy: [{ suburb: "asc" }, { id: "asc" }],
	});

	// Transform to flat structure expected by the UI
	return households.map(transformToHouseholdWithDetails);
}

/**
 * Get total count of households with optional filtering
 */
export async function getHouseholdsCount(
	options: { searchTerm?: string; censusYearId?: number } = {},
): Promise<number> {
	const { searchTerm, censusYearId } = options;

	// Build where conditions for Prisma
	const whereConditions: Prisma.HouseholdWhereInput = {};

	// Add search term filter
	if (searchTerm) {
		whereConditions.OR = [
			{ suburb: { contains: searchTerm, mode: "insensitive" } },
			{
				householdMembers: {
					some: {
						member: {
							OR: [
								{ firstName: { contains: searchTerm, mode: "insensitive" } },
								{ lastName: { contains: searchTerm, mode: "insensitive" } },
								{ mobilePhone: { contains: searchTerm } },
							],
						},
					},
				},
			},
			{
				uniqueCodes: {
					some: {
						code: { contains: searchTerm, mode: "insensitive" },
					},
				},
			},
		];
	}

	// Add census year filter
	if (censusYearId) {
		whereConditions.householdMembers = {
			some: { censusYearId },
		};
	}

	return await prisma.household.count({
		where:
			Object.keys(whereConditions).length > 0 ? whereConditions : undefined,
	});
}

/**
 * Get household member count
 */
export async function getHouseholdMemberCount(
	householdId: number,
	censusYearId?: number,
): Promise<number> {
	const count = await prisma.householdMember.count({
		where: {
			householdId,
			...(censusYearId ? { censusYearId } : { isCurrent: true }),
		},
	});

	return count;
}

/**
 * Get household head information
 */
export async function getHouseholdHead(
	householdId: number,
	censusYearId?: number,
): Promise<Member | null> {
	const householdMember = await prisma.householdMember.findFirst({
		where: {
			householdId,
			relationship: "head",
			...(censusYearId ? { censusYearId } : { isCurrent: true }),
		},
		include: {
			member: true,
		},
	});

	return householdMember?.member || null;
}
