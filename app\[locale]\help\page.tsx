import type { Metada<PERSON> } from "next";
import { getTranslations } from "next-intl/server";
import { HelpPageClient } from "@/components/help/help-page-client";
import { getChurchInfo } from "@/lib/db/settings";

// Force dynamic rendering to prevent DYNAMIC_SERVER_USAGE errors
export const dynamic = "force-dynamic";

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("metadata");

  return {
    title: t("helpTitle"),
    description: t("helpDescription"),
  };
}

// Server component to fetch church information
export default async function HelpPage() {
  const t = await getTranslations("help");

  // Fetch church information from the database
  let churchInfo = {
    churchName: "",
    email: "",
    contactNumber: "",
    address: "",
  };

  try {
    // Add a log to help debug connection issues
    if (process.env.NODE_ENV === "development") {
    }

    // Try to get church info directly from the database (now with caching)
    churchInfo = await getChurchInfo();
  } catch (_error) {
    if (process.env.NODE_ENV === "development") {
    }
    // Fallback to translated default values if database fetch fails
    churchInfo = {
      churchName: t("fallbackChurchName"),
      email: t("fallbackEmail"),
      contactNumber: t("fallbackPhone"),
      address: t("fallbackAddress"),
    };
  }

  return <HelpPageClient churchInfo={churchInfo} />;
}
