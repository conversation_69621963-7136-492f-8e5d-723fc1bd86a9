/**
 * Minimal tour positioning utilities
 */

/**
 * Get dynamic header height
 */
function getHeaderHeight(): number {
  const header = document.querySelector("header.sticky");
  return header ? header.getBoundingClientRect().height : 64;
}

/**
 * Smooth scroll to element with header offset
 */
export function scrollToElement(element: HTMLElement, offset = 100): void {
  const elementRect = element.getBoundingClientRect();
  const absoluteElementTop = elementRect.top + (window.scrollY || window.pageYOffset);
  const headerHeight = getHeaderHeight();

  const scrollToY = Math.max(0, absoluteElementTop - headerHeight - offset);

  window.scrollTo({
    top: scrollToY,
    behavior: "smooth",
  });
}
