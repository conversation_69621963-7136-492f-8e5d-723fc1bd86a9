"use client";

import { useTranslations } from "next-intl";
import { useCallback, useEffect, useState } from "react";
import { StatusBadge } from "@/components/ui/status-badge";
import {
	Toolt<PERSON>,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

interface CensusStatus {
	isOpen: boolean;
	isManualOverride: boolean;
	scheduledState: boolean | null;
	nextChangeTime: string | null;
	error?: string;
}

interface CensusStatusIndicatorProps {
	className?: string;
}

/**
 * Census Status Indicator component for the admin header
 * Shows current census status as a text badge with tooltip
 * Uses the existing /api/census/status endpoint
 */
export function CensusStatusIndicator({
	className,
}: CensusStatusIndicatorProps) {
	const [status, setStatus] = useState<CensusStatus | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const t = useTranslations("common");
	const tAdmin = useTranslations("admin");
	const tErrors = useTranslations("errors");

	// Fetch census status from the admin API endpoint
	const fetchStatus = useCallback(async () => {
		try {
			setError(null);

			const response = await fetch("/api/admin/census-status", {
				method: "GET",
				headers: {
					"Content-Type": "application/json",
				},
				cache: "no-store", // Prevent caching to ensure fresh data
			});

			if (!response.ok) {
				throw new Error(
					tErrors("failedToFetchCensusStatusRespo", {
						status: response.status.toString(),
					}),
				);
			}

			const data = await response.json();

			if (data.error) {
				throw new Error(data.error);
			}

			setStatus(data);
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : t("unknownErrorOccurred");
			setError(errorMessage);
			if (process.env.NODE_ENV === "development") {
				console.error("Error fetching census status:", err);
			}
		} finally {
			setIsLoading(false);
		}
	}, [t, tErrors]);

	// Initial fetch and polling setup
	useEffect(() => {
		fetchStatus();

		// Set up polling every 15 seconds for faster updates
		const interval = setInterval(fetchStatus, 15_000);
		return () => clearInterval(interval);
	}, [fetchStatus]);

	// Listen for manual census status changes
	useEffect(() => {
		const handleStatusChange = () => {
			fetchStatus();
		};

		window.addEventListener("census-status-changed", handleStatusChange);
		return () =>
			window.removeEventListener("census-status-changed", handleStatusChange);
	}, [fetchStatus]);

	// Determine badge variant and tooltip text
	const getStatusDisplay = () => {
		if (isLoading) {
			return {
				variant: "default" as const,
				text: t("loading"),
				tooltip: t("loadingCensusStatus"),
				animate: true,
			};
		}

		if (error) {
			return {
				variant: "default" as const,
				text: t("error"),
				tooltip: t("failedToLoadStatus"),
				animate: false,
			};
		}

		if (status?.isOpen) {
			return {
				variant: "success" as const,
				text: t("open"),
				tooltip: tAdmin("censusIsOpen"),
				animate: false,
			};
		}

		return {
			variant: "danger" as const,
			text: t("closed"),
			tooltip: tAdmin("censusIsClosed"),
			animate: false,
		};
	};

	const statusDisplay = getStatusDisplay();

	return (
		<TooltipProvider>
			<Tooltip>
				<TooltipTrigger asChild>
					<div className={cn("mr-2 flex items-center", className)}>
						<StatusBadge
							className={cn(
								"cursor-pointer transition-opacity",
								statusDisplay.animate && "animate-pulse",
							)}
							variant={statusDisplay.variant}
						>
							{statusDisplay.text}
						</StatusBadge>
					</div>
				</TooltipTrigger>
				<TooltipContent align="center" side="bottom">
					<p>{statusDisplay.tooltip}</p>
				</TooltipContent>
			</Tooltip>
		</TooltipProvider>
	);
}
