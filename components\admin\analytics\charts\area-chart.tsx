"use client";

import {
	<PERSON>,
	<PERSON><PERSON><PERSON>,
	Cartes<PERSON>Grid,
	<PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
} from "recharts";
import { type ChartConfig, ChartContainer } from "@/components/ui/chart";
import { type BaseChartProps, registerChart } from "./chart-registry";
import {
	CHART_CONFIGS,
	CHART_DEFAULTS,
	MOBILE_CHART_CONFIGS,
} from "./constants";

function AreaChartComponent({
	data,
	isAnimationActive = true,
	className,
}: BaseChartProps) {
	const config = data.config || {};
	const { stackedKeys = [], showGrid = true, showLegend = true } = config;

	// Check for responsive configuration
	const responsiveConfig = (data as any)._responsive;
	const isMobile = responsiveConfig?.isMobile;
	const chartHeight = responsiveConfig?.height || CHART_DEFAULTS.HEIGHT;
	const chartMargins =
		responsiveConfig?.margins || CHART_DEFAULTS.MARGINS.DEFAULT;
	const fontSize =
		responsiveConfig?.fontSize || MOBILE_CHART_CONFIGS.FONT_SIZES.DESKTOP;

	// If no stacked keys provided, try to infer from data
	const keys =
		stackedKeys.length > 0
			? stackedKeys
			: data.data.length > 0
				? Object.keys(data.data[0]).filter(
						(key) =>
							key !== (data.xKey || "date") &&
							typeof data.data[0][key] === "number",
					)
				: [];

	// Create chart config for shadcn using theme approach
	const chartConfig: ChartConfig = keys.reduce((acc, key, index) => {
		const lightColors = [
			"oklch(0.646 0.222 41.116)", // chart-1
			"oklch(0.6 0.118 184.704)", // chart-2
			"oklch(0.398 0.07 227.392)", // chart-3
			"oklch(0.828 0.189 84.429)", // chart-4
			"oklch(0.769 0.188 70.08)", // chart-5
		];
		const darkColors = [
			"oklch(0.488 0.243 264.376)", // chart-1
			"oklch(0.696 0.17 162.48)", // chart-2
			"oklch(0.769 0.188 70.08)", // chart-3
			"oklch(0.627 0.265 303.9)", // chart-4
			"oklch(0.645 0.246 16.439)", // chart-5
		];

		acc[key] = {
			label: key,
			theme: {
				light: lightColors[index % 5],
				dark: darkColors[index % 5],
			},
		};
		return acc;
	}, {} as ChartConfig);

	// Custom tooltip
	const CustomTooltip = ({
		active,
		payload,
		label,
	}: {
		active?: boolean;
		payload?: Array<{ color: string; dataKey: string; value: number | string }>;
		label?: string;
	}) => {
		if (active && payload && payload.length) {
			// Validate that we have valid data entries
			const validEntries = payload.filter(
				(entry) => entry && entry.value !== undefined && entry.value !== null,
			);
			if (validEntries.length === 0) {
				return null;
			}

			return (
				<div className="rounded-lg border border-slate-200 bg-white p-3 shadow-lg dark:border-slate-600 dark:bg-slate-800">
					<p className="mb-2 font-medium text-slate-900 dark:text-slate-100">
						{label}
					</p>
					<div className="space-y-1">
						{validEntries.map(
							(
								entry: {
									color: string;
									dataKey: string;
									value: number | string;
								},
								index: number,
							) => (
								<div className="flex items-center gap-2 text-sm" key={index}>
									<div
										className="h-3 w-3 rounded-sm"
										style={{ backgroundColor: entry.color }}
									/>
									<span className="text-slate-600 dark:text-slate-400">
										{entry.dataKey}:
									</span>
									<span className="font-medium text-slate-900 dark:text-slate-100">
										{typeof entry.value === "number"
											? entry.value.toLocaleString()
											: entry.value}
									</span>
								</div>
							),
						)}
					</div>
				</div>
			);
		}
		return null;
	};

	// Custom legend
	const CustomLegend = ({
		payload,
	}: {
		payload?: Array<{ color: string; value: string }>;
	}) => {
		if (!(showLegend && payload)) {
			return null;
		}

		return (
			<div className="mt-4 flex flex-wrap justify-center gap-4 text-sm">
				{payload.map(
					(entry: { color: string; value: string }, index: number) => (
						<div className="flex items-center gap-2" key={index}>
							<div
								className="h-3 w-3 rounded-sm"
								style={{ backgroundColor: entry.color }}
							/>
							<span className="text-slate-700 dark:text-slate-300">
								{entry.value}
							</span>
						</div>
					),
				)}
			</div>
		);
	};

	return (
		<div className={`w-full ${className || ""}`}>
			<ChartContainer
				className="w-full"
				config={chartConfig}
				style={{ height: `${chartHeight}px` }}
			>
				<AreaChart data={data.data} margin={chartMargins}>
					{showGrid && (
						<CartesianGrid className="opacity-30" strokeDasharray="3 3" />
					)}
					<XAxis
						angle={
							data.data.length > CHART_CONFIGS.AREA.LABEL_ANGLE_THRESHOLD
								? -45
								: 0
						}
						className="text-xs"
						dataKey={data.xKey || "date"}
						height={
							data.data.length > CHART_CONFIGS.AREA.LABEL_ANGLE_THRESHOLD
								? isMobile
									? 50
									: 60
								: isMobile
									? 25
									: 30
						}
						interval={0}
						textAnchor={
							data.data.length > CHART_CONFIGS.AREA.LABEL_ANGLE_THRESHOLD
								? "end"
								: "middle"
						}
						tick={{ fontSize: fontSize.tick }}
					/>
					<YAxis
						className="text-xs"
						tick={{ fontSize: fontSize.tick }}
						width={isMobile ? 35 : 45}
					/>
					<Tooltip content={<CustomTooltip />} />
					{showLegend && <Legend content={<CustomLegend />} />}

					{/* Render areas for each metric */}
					{keys.map((key: string, index: number) => (
						<Area
							animationBegin={index * 100}
							animationDuration={CHART_DEFAULTS.ANIMATION_DURATION}
							dataKey={key}
							fill={`var(--color-${key})`}
							fillOpacity={CHART_CONFIGS.AREA.FILL_OPACITY}
							isAnimationActive={isAnimationActive}
							key={key}
							stackId="1"
							stroke={`var(--color-${key})`}
							strokeWidth={CHART_CONFIGS.AREA.STROKE_WIDTH}
							type="monotone"
						/>
					))}
				</AreaChart>
			</ChartContainer>
		</div>
	);
}

// Register the component
registerChart("area", AreaChartComponent);

export default AreaChartComponent;
