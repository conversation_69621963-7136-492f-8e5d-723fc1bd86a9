/**
 * Authentication error translation mappings
 * Maps error codes to translation keys in the 'auth' namespace
 * Used by useMessage hook and server-side translation routes
 */
export const authErrorKeys: Record<string, string> = {
	// NextAuth.js error codes
	CredentialsSignin: "invalidCredentials",
	OAuthSignin: "oauthSigninError",
	OAuthCallback: "oauthCallbackError",
	OAuthCreateAccount: "oauthCreateAccountError",
	EmailCreateAccount: "emailCreateAccountError",
	Callback: "callbackError",
	OAuthAccountNotLinked: "oauthAccountNotLinked",
	EmailSignin: "emailSigninError",
	SessionRequired: "sessionRequired",

	// Custom error codes
	TotpRequired: "totpRequired",
	TotpInvalid: "totpInvalid",

	// Redirect reasons
	unauthenticated: "unauthenticated",
	unauthorized: "unauthorized",
	session_expired: "sessionExpired",

	// Login/verification errors
	errorDuringLogin: "errorDuringLogin",
	verificationFailed: "verificationFailed",

	// Default error message
	default: "authenticationError",
};
