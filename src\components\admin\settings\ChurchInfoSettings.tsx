"use client";

import { useTranslations } from "next-intl";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { FormFieldWithTooltip } from "@/components/admin/settings/FormFieldWithTooltip";
import { SettingsCard } from "@/components/admin/settings/SettingsCard";
import { useFormSubmit } from "@/hooks/useFormSubmit";
import { useMessage } from "@/hooks/useMessage";
import { zodResolver } from "@/lib/utils/zod-resolver-compat";
import {
	type ClientChurchInfoFormValues,
	createClientChurchInfoSchema,
} from "@/lib/validation/client/settings-client";

export function ChurchInfoSettings() {
	const t = useTranslations("admin");
	const tForms = useTranslations("forms");
	const tValidation = useTranslations("validation");
	const { showSuccess, showError, showWarning } = useMessage();

	// Create client schema with translations
	const churchInfoSchema = createClientChurchInfoSchema(tValidation);

	const form = useForm<ClientChurchInfoFormValues>({
		resolver: zodResolver(churchInfoSchema),
		defaultValues: {
			churchName: "",
			email: "",
			contactNumber: "",
			address: "",
		},
	});

	// Fetch current church information
	useEffect(() => {
		const fetchChurchInfo = async () => {
			try {
				const response = await fetch("/api/settings/church-info");

				// Handle API errors
				if (!response.ok) {
					console.warn(`API call failed with status ${response.status}`);

					// If unauthorized, it means we're not logged in
					if (response.status === 401) {
						console.warn("Not logged in as admin");
						showWarning("needAdminLoginToViewSettings");
					} else {
						showError("failedToLoadChurchInfo");
					}

					// Set placeholder values
					form.reset({
						churchName: "",
						email: "",
						contactNumber: "",
						address: "",
					});
					return;
				}

				const data = await response.json();

				// Update form values
				form.reset({
					churchName: data.churchName || "",
					email: data.email || "",
					contactNumber: data.contactNumber || "",
					address: data.address || "",
				});
			} catch (error) {
				console.error("Error fetching church info:", error);
				// For development purposes, use default values if the API call fails
				form.reset({
					churchName: "WSCCC Church",
					email: "<EMAIL>",
					contactNumber: "0412345678",
					address: "123 Yeah Street, Sydney NSW 2000",
				});
				showWarning("usingDefaultValuesDatabaseUnavailable");
			}
		};

		fetchChurchInfo();
	}, [form, showWarning, showError, t]);

	// Use our custom hook for form submission
	const { handleSubmit: submitChurchInfo, isSubmitting } =
		useFormSubmit<ClientChurchInfoFormValues>({
			onSubmit: async (data) => {
				try {
					const response = await fetch("/api/settings/church-info", {
						method: "PUT",
						headers: {
							"Content-Type": "application/json",
						},
						body: JSON.stringify(data),
					});

					const responseData = await response.json();

					if (!response.ok) {
						// If unauthorized, return a specific message
						if (response.status === 401) {
							return {
								success: false,
								message: t("needAdminLoginToSaveSettings"),
							};
						}

						if (responseData.details) {
							// Handle validation errors
							showError("InvalidInput", "settings");
							return {
								success: false,
								message: tValidation("checkFormErrors"),
							};
						}

						showError("UpdateFailed", "settings");
						return {
							success: false,
							message: t("failedToUpdateChurchInfo"),
						};
					}

					// Check if this is a development message about database connection
					if (
						responseData.message &&
						responseData.message.includes("would be saved in production")
					) {
						return {
							success: true,
							message: responseData.message,
						};
					}

					// Success - use centralized alert system
					showSuccess("churchInfoUpdated");
					return {
						success: true,
						suppressAlert: true,
					};
				} catch (error) {
					console.error("Error updating church info:", error);

					if (error instanceof Error && error.message === "ValidationError") {
						showError("InvalidInput", "settings");
					} else {
						showError("UpdateFailed", "settings");
					}

					return {
						success: false,
						suppressAlert: true,
					};
				}
			},
		});

	return (
		<SettingsCard
			description={t("updateChurchContactInfo")}
			form={form}
			isSubmitting={isSubmitting}
			onFormSubmit={submitChurchInfo}
			title={t("churchInformation")}
		>
			<div className="space-y-6">
				<FormFieldWithTooltip
					error={form.formState.errors.churchName}
					id="churchName"
					label={t("churchName")}
					placeholder={t("enterChurchName")}
					register={form.register}
					required
				/>

				<FormFieldWithTooltip
					error={form.formState.errors.email}
					id="email"
					label={tForms("email")}
					placeholder={t("enterEmailAddress")}
					register={form.register}
					required
					type="email"
				/>

				<FormFieldWithTooltip
					error={form.formState.errors.contactNumber}
					id="contactNumber"
					label={t("contactNumber")}
					placeholder={tForms("enter10DigitMobile")}
					register={form.register}
					required
					type="tel"
				/>

				<FormFieldWithTooltip
					error={form.formState.errors.address}
					id="address"
					label={tForms("address")}
					placeholder={t("enterChurchAddress")}
					register={form.register}
					required
				/>
			</div>
		</SettingsCard>
	);
}
