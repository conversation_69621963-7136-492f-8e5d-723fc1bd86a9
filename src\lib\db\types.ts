/**
 * Common type definitions for database operations - Prisma Edition
 * Updated for PostgreSQL with Prisma ORM
 */

// Type for database query result with insertId (Prisma equivalent)
export interface QueryResultWithInsertId {
	insertId: number;
	affectedRows: number;
	changedRows: number;
}

// Type for database query result with metadata
export interface QueryResultWithFields<T> {
	data: T[];
	count: number;
}

// Type for database service information
export interface DatabaseServiceInfo {
	sessions: {
		total: number;
		expired: number;
	};
	auditLogs: {
		total: number;
		old: number;
	};
}

// Type for database export options
export interface DatabaseExportOptions {
	format: "sql" | "csv" | "json";
	tables: "all" | "members" | "households" | "settings";
	adminId?: number;
}

// Type for database export result
export interface DatabaseExportResult {
	data: string | Buffer;
	filename: string;
	contentType: string;
	recordCount: number;
}

// Type for database import data
export interface DatabaseImportData {
	[tableName: string]: Array<Record<string, unknown>>;
}

// Type for database import validation result
export interface DatabaseImportValidationResult {
	isValid: boolean;
	errors: string[];
	warnings: string[];
	tables: string[];
	recordCount: number;
}

// Type for database import result
export interface DatabaseImportResult {
	success: boolean;
	message: string;
	details?: unknown;
}

// Type for database service action parameters
export interface DatabaseServiceActionParameters {
	[key: string]: string | number | boolean | null;
}

// Type for database service action result
export interface DatabaseServiceActionResult {
	success: boolean;
	message: string;
	details?: unknown;
}

// Type for database connection (Prisma-compatible)
export interface DatabaseConnection {
	connected: boolean;
	error?: string;
}

// Type for database row
export type DatabaseRow = Record<string, unknown>;

// Type for database query parameters
export type QueryParams = (
	| string
	| number
	| boolean
	| null
	| undefined
	| Date
)[];

// Type for query options
export interface QueryOptions {
	timeout?: number; // Query timeout in milliseconds
	retries?: number; // Number of retries for the query
}

// Type for database query result
export type QueryResult<T = DatabaseRow[]> = T;

// Type for Prisma query result with insertId
export interface PrismaInsertResult {
	id: number;
	count: number;
}
