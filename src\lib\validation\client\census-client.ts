import { z } from "zod/v4";
import { getTodayInSydney, startOfDay } from "@/lib/utils/date-time";

/**
 * Client-side validation schemas for census forms
 * These schemas use translation functions for user-facing error messages
 * Use with useTranslations('validation') hook in client components
 */

/**
 * Create household member form validation schema with translations
 * @param t - Translation function from useTranslations('validation')
 */
export function createClientHouseholdMemberSchema(t: any) {
  return z.object({
    id: z.number().optional(), // Optional for new members, required for updates
    firstName: z.string().min(1, { error: t("firstNameRequired") }),
    lastName: z.string().min(1, { error: t("lastNameRequired") }),
    dateOfBirth: z.coerce
      .date({
        error: t("dateOfBirthRequired"),
      })
      .refine(
        (date) => {
          const today = getTodayInSydney();
          const dateToCheck = startOfDay(date);
          return dateToCheck <= today;
        },
        {
          error: t("futureDateNotAllowed"),
        },
      ),
    gender: z.enum(["male", "female", "other"], {
      error: t("selectGender"),
    }),
    mobilePhone: z
      .string()
      .min(10, { error: t("phoneMinLength") })
      .max(10, { error: t("phoneMaxLength") })
      .regex(/^\d+$/, { error: t("phoneNumbers") }),
    hobby: z.string().optional().or(z.literal("")),
    occupation: z.string().optional().or(z.literal("")),
    relationship: z.enum(["head", "spouse", "child", "parent", "relative", "other"], {
      error: t("selectRelationship"),
    }),
  });
}

/**
 * Create sacrament form validation schema with translations
 * @param t - Translation function from useTranslations('validation')
 */
export function createClientSacramentSchema(t: any) {
  return z.object({
    id: z.number().optional(),
    memberId: z.number().optional(),
    sacramentTypeId: z.number(),
    date: z
      .union([
        z.date().refine(
          (date) => {
            const today = getTodayInSydney();
            const dateToCheck = startOfDay(date);
            return dateToCheck <= today;
          },
          {
            error: t("sacramentDateFuture"),
          },
        ),
        z.string().refine(
          (val) => {
            if (!val || val === "") return true; // Allow empty strings
            const parsedDate = new Date(val);
            if (isNaN(parsedDate.getTime())) return false; // Invalid date format
            const today = getTodayInSydney();
            const dateToCheck = startOfDay(parsedDate);
            return dateToCheck <= today;
          },
          {
            error: t("sacramentDateFuture"),
          },
        ),
        z.null(),
      ])
      .optional(),
    place: z.string().optional().or(z.literal("")),
  });
}

/**
 * Create combined member sacrament form validation schema with translations
 * @param t - Translation function from useTranslations('validation')
 */
export function createClientCombinedMemberSacramentSchema(t: any) {
  const householdMemberSchema = createClientHouseholdMemberSchema(t);
  const sacramentSchema = createClientSacramentSchema(t);

  return householdMemberSchema.extend({
    sacraments: z.array(sacramentSchema).optional(),
  });
}

/**
 * Create census form status validation schema
 */
export function createClientCensusFormStatusSchema(t: (key: string) => string) {
  return z.object({
    householdId: z.number(),
    censusYearId: z.number(),
    status: z.enum(["not_started", "in_progress", "completed"]),
    householdComment: z
      .string()
      .max(1000, { error: t("householdCommentTooLong") })
      .optional()
      .or(z.literal("")),
  });
}

/**
 * Create household registration form validation schema with translations
 * @param t - Translation function from useTranslations('validation')
 */
export function createClientHouseholdRegistrationSchema(t: any) {
  return z.object({
    suburb: z.string().min(1, { error: t("suburbRequired") }),
    headFirstName: z.string().min(1, { error: t("firstNameRequired") }),
    headLastName: z.string().min(1, { error: t("lastNameRequired") }),
    headDateOfBirth: z.coerce
      .date({
        error: t("dateOfBirthRequired"),
      })
      .refine(
        (date) => {
          const today = getTodayInSydney();
          const dateToCheck = startOfDay(date);
          return dateToCheck <= today;
        },
        {
          error: t("futureDateNotAllowed"),
        },
      ),
    mobilePhone: z
      .string()
      .min(10, { error: t("phoneMinLength") })
      .max(10, { error: t("phoneMaxLength") })
      .regex(/^\d+$/, { error: t("phoneNumbers") }),
    gender: z.enum(["male", "female", "other"], {
      error: t("selectGender"),
    }),
  });
}

/**
 * Create census code form validation schema with translations
 * @param t - Translation function from useTranslations('validation')
 */
export function createClientCensusCodeSchema(t: any) {
  return z.object({
    code: z
      .string()
      .min(1, { error: t("invalidCensusCode") })
      .max(50, { error: t("invalidCensusCode") })
      .refine(
        (code) => {
          // Internal validation logic - basic constraints without exposing the exact format
          // Real validation happens server-side via database lookup for security

          // Check character constraints without revealing the pattern
          if (!/^[A-Za-z0-9-]+$/.test(code)) {
            return false;
          }
          return true;
        },
        {
          error: t("invalidCensusCode"),
        },
      ),
  });
}

/**
 * Create delete account verification form validation schema with translations
 * @param t - Translation function from useTranslations('validation')
 */
export function createClientDeleteAccountVerificationSchema(t: any) {
  return z.object({
    confirmationPhrase: z
      .string()
      .min(1, { error: t("confirmationPhraseRequired") })
      .refine((value) => value === "DELETE NOW", {
        error: t("invalidConfirmationPhrase"),
      }),
  });
}

// Type exports for client-side forms
export type ClientHouseholdMemberFormValues = z.infer<
  ReturnType<typeof createClientHouseholdMemberSchema>
>;
export type ClientSacramentFormValues = z.infer<ReturnType<typeof createClientSacramentSchema>>;
export type ClientCombinedMemberSacramentFormValues = z.infer<
  ReturnType<typeof createClientCombinedMemberSacramentSchema>
>;
export type ClientCensusFormStatusFormValues = z.infer<
  ReturnType<typeof createClientCensusFormStatusSchema>
>;
export type ClientHouseholdRegistrationFormValues = z.infer<
  ReturnType<typeof createClientHouseholdRegistrationSchema>
>;

export type ClientCensusCodeFormValues = z.infer<ReturnType<typeof createClientCensusCodeSchema>>;
export type ClientDeleteAccountVerificationFormValues = z.infer<
  ReturnType<typeof createClientDeleteAccountVerificationSchema>
>;
