import type { <PERSON>ada<PERSON> } from "next";
import { getTranslations } from "next-intl/server";
import { requireAdmin } from "@/lib/auth/auth-utils";
import { UniqueCodeClient } from "./unique-code-client";

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("metadata");

  return {
    title: t("uniqueCodeManagementTitle"),
    description: t("uniqueCodeManagementDescription"),
  };
}

export default async function UniqueCodePage() {
  // Server-side authentication check
  await requireAdmin();

  return <UniqueCodeClient />;
}
