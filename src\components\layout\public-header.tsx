"use client";

import { <PERSON><PERSON>ir<PERSON>, Home, Menu, X } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { LanguageSelector } from "@/components/language/language-selector";
import { ThemeToggle } from "@/components/theme/theme-toggle";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

/**
 * PublicHeader component - Provides navigation for public pages without authentication dependencies
 *
 * This component is designed to be used on public pages like the help page,
 * where users might be logged into both admin and census portals simultaneously.
 * It doesn't rely on any specific authentication system, avoiding conflicts.
 */
export function PublicHeader() {
	const pathname = usePathname();
	const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

	// Translation hooks
	const tNav = useTranslations("navigation");
	const tCommon = useTranslations("common");
	const tBrand = useTranslations("brand");

	/**
	 * Handle responsive behavior
	 * - Uses window resize event listener with proper cleanup
	 */
	useEffect(() => {
		const handleResize = () => {
			const isMobile = window.innerWidth < 768; // md breakpoint is 768px

			// Close mobile menu if screen is resized to desktop
			if (!isMobile && mobileMenuOpen) {
				setMobileMenuOpen(false);
			}
		};

		// Initial check
		handleResize();

		// Add resize listener
		window.addEventListener("resize", handleResize);

		// Cleanup
		return () => window.removeEventListener("resize", handleResize);
	}, [mobileMenuOpen]);

	/**
	 * Toggle mobile menu open/closed
	 */
	const toggleMobileMenu = () => {
		setMobileMenuOpen(!mobileMenuOpen);
	};

	/**
	 * Close mobile menu when clicking outside
	 */
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			// Only run this if the mobile menu is open
			if (!mobileMenuOpen) return;

			// Check if the click was outside the header
			const header = document.querySelector("header");
			if (header && !header.contains(event.target as Node)) {
				setMobileMenuOpen(false);
			}
		};

		// Add event listener
		document.addEventListener("mousedown", handleClickOutside);

		// Cleanup
		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [mobileMenuOpen]);

	/**
	 * Render the PublicHeader component
	 */
	return (
		<header className="sticky top-0 z-50 w-full border-b bg-background/80 backdrop-blur-sm supports-[backdrop-filter]:bg-background/60">
			<div className="flex h-16 items-center justify-between px-0">
				{/* Logo and Brand */}
				<div className="flex items-center gap-2 pl-4">
					<Link
						className="flex cursor-pointer items-center gap-2 font-medium"
						href="/"
					>
						<div className="flex h-6 w-6 items-center justify-center rounded-md bg-primary text-primary-foreground">
							<svg
								aria-hidden="true"
								fill="none"
								height="16"
								stroke="currentColor"
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth="2"
								viewBox="0 0 24 24"
								width="16"
								xmlns="http://www.w3.org/2000/svg"
							>
								<path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z" />
								<path d="M3 9V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v4" />
								<path d="M13 13h4" />
								<path d="M13 17h4" />
								<path d="M7 13h2v4H7z" />
							</svg>
						</div>
						<span className="font-medium sm:inline-block">
							{tBrand("name")}
						</span>
					</Link>
				</div>

				{/* Desktop Navigation */}
				<nav
					aria-label="Main navigation"
					className="hidden items-center gap-6 md:flex"
				>
					<Link
						className={cn(
							"flex cursor-pointer items-center justify-center rounded-md p-2 font-medium text-sm transition-colors hover:bg-accent",
							pathname === "/" ? "text-primary" : "text-muted-foreground",
						)}
						href="/"
					>
						<Home aria-hidden="true" className="mr-2 h-4 w-4" />
						{tNav("home")}
					</Link>
					<Link
						className={cn(
							"flex cursor-pointer items-center justify-center rounded-md p-2 font-medium text-sm transition-colors hover:bg-accent",
							pathname === "/help" ? "text-primary" : "text-muted-foreground",
						)}
						href="/help"
					>
						<HelpCircle aria-hidden="true" className="mr-2 h-4 w-4" />
						{tNav("help")}
					</Link>
				</nav>

				{/* Right Side Actions */}
				<div className="flex items-center gap-2 pr-4">
					{/* Theme Toggle */}
					<ThemeToggle />

					{/* Language Selector */}
					<LanguageSelector />

					{/* Mobile Menu Button */}
					<Button
						aria-expanded={mobileMenuOpen}
						aria-label={tCommon("toggleMenu")}
						className="cursor-pointer md:hidden"
						onClick={toggleMobileMenu}
						size="icon"
						variant="ghost"
					>
						{mobileMenuOpen ? (
							<X aria-hidden="true" className="h-5 w-5" />
						) : (
							<Menu aria-hidden="true" className="h-5 w-5" />
						)}
						<span className="sr-only">{tCommon("toggleMenu")}</span>
					</Button>
				</div>
			</div>

			{/* Mobile Navigation - Overlay Style */}
			{mobileMenuOpen && (
				<>
					{/* Backdrop */}
					<div className="fade-in-0 fixed inset-0 top-16 z-40 animate-in bg-background/80 backdrop-blur-sm duration-100" />

					{/* Menu */}
					<div className="slide-in-from-top-5 fixed inset-x-0 top-16 z-50 animate-in border-t bg-background/95 shadow-lg backdrop-blur-sm duration-200 md:hidden">
						<nav
							aria-label="Mobile navigation"
							className="flex flex-col space-y-4 p-4"
						>
							<Link
								className={cn(
									"flex cursor-pointer items-center gap-2 rounded-md p-2 font-medium text-sm transition-colors hover:bg-accent",
									pathname === "/"
										? "bg-muted/50 text-primary"
										: "text-foreground",
								)}
								href="/"
								onClick={() => setMobileMenuOpen(false)}
							>
								<Home aria-hidden="true" className="h-4 w-4" />
								{tNav("home")}
							</Link>
							<Link
								className={cn(
									"flex cursor-pointer items-center gap-2 rounded-md p-2 font-medium text-sm transition-colors hover:bg-accent",
									pathname === "/help"
										? "bg-muted/50 text-primary"
										: "text-foreground",
								)}
								href="/help"
								onClick={() => setMobileMenuOpen(false)}
							>
								<HelpCircle aria-hidden="true" className="h-4 w-4" />
								{tNav("help")}
							</Link>
						</nav>
					</div>
				</>
			)}
		</header>
	);
}
