/**
 * Admin Users Database Operations with Prisma
 * Replaces MySQL-based implementation with Prisma ORM
 */

import type { Admin } from "@prisma/client";
import { hashPassword } from "@/lib/auth";
import { prisma } from "./prisma";

// Legacy interface for backward compatibility
export interface AdminLegacy {
	id: number;
	username: string;
	password: string;
	email: string | null;
	fullName: string | null;
	twoFactorSecret: string | null;
	twoFactorEnabled: boolean;
	twoFactorBackupCodes: string | null;
	lastLogin: string | null;
	createdAt: string;
	updatedAt: string;
}

// Get admin by ID
export async function getAdminById(id: number): Promise<Admin | null> {
	return await prisma.admin.findUnique({
		where: { id },
	});
}

// Get admin by username
export async function getAdminByUsername(
	username: string,
): Promise<Admin | null> {
	return await prisma.admin.findUnique({
		where: { username },
	});
}

// Create a new admin
export async function createAdmin(
	username: string,
	password: string,
	email?: string,
	fullName?: string,
): Promise<Admin> {
	const hashedPassword = await hashPassword(password);
	return await prisma.admin.create({
		data: {
			username,
			password: hashedPassword,
			email: email || null,
			fullName: fullName || null,
		},
	});
}

// Update admin password
export async function updateAdminPassword(
	adminId: number,
	newPassword: string,
): Promise<void> {
	const hashedPassword = await hashPassword(newPassword);
	await prisma.admin.update({
		where: { id: adminId },
		data: { password: hashedPassword },
	});
}

// Update admin last login
export async function updateAdminLastLogin(adminId: number): Promise<void> {
	await prisma.admin.update({
		where: { id: adminId },
		data: { lastLogin: new Date() },
	});
}

// Set up 2FA for an admin
export async function setupTwoFactorAuth(
	adminId: number,
	secret: string,
	enabled = false,
): Promise<void> {
	await prisma.admin.update({
		where: { id: adminId },
		data: {
			twoFactorSecret: secret,
			twoFactorEnabled: enabled,
		},
	});
}

// Enable or disable 2FA for an admin
export async function updateTwoFactorAuthStatus(
	adminId: number,
	enabled: boolean,
): Promise<void> {
	await prisma.admin.update({
		where: { id: adminId },
		data: { twoFactorEnabled: enabled },
	});
}

// Set backup codes for 2FA
export async function setTwoFactorBackupCodes(
	adminId: number,
	backupCodes: string[],
): Promise<void> {
	const codesJson = JSON.stringify(backupCodes);
	await prisma.admin.update({
		where: { id: adminId },
		data: { twoFactorBackupCodes: codesJson },
	});
}

// Get backup codes for 2FA
export async function getTwoFactorBackupCodes(
	adminId: number,
): Promise<string[] | null> {
	const admin = await prisma.admin.findUnique({
		where: { id: adminId },
		select: { twoFactorBackupCodes: true },
	});

	if (!(admin && admin.twoFactorBackupCodes)) {
		return null;
	}

	return JSON.parse(admin.twoFactorBackupCodes);
}
