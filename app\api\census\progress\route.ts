import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { STEP_PERCENTAGES } from "@/hooks/use-census-progress";
import { censusAuthOptions } from "@/lib/census-auth/census-auth-options";
import { prisma } from "@/lib/db/prisma";

/**
 * GET /api/census/progress
 *
 * Returns the completion progress for the current census participant
 * Tracks completion of:
 * - Household registration (always true after login)
 * - Hobbies (household head has hobby field filled)
 * - Occupation (household head has occupation field filled)
 * - Sacraments (household head has sacrament records)
 * - Family members (household has more than just the head)
 * - Community feedback (community feedback text is entered)
 */
export async function GET() {
	try {
		// Security: Validate census session
		const session = await getServerSession(censusAuthOptions);

		if (!session?.user) {
			if (process.env.NODE_ENV === "development") {
			}
			return NextResponse.json(
				{
					error: "Unauthorized",
					message: "Census authentication required",
				},
				{
					status: 401,
					headers: {
						"Cache-Control": "no-store, no-cache, must-revalidate",
						Pragma: "no-cache",
					},
				},
			);
		}

		// Security: Validate user role
		if (session.user.role !== "household") {
			if (process.env.NODE_ENV === "development") {
			}
			return NextResponse.json(
				{
					error: "Forbidden",
					message: "Household access required",
				},
				{ status: 403 },
			);
		}

		// Security: Validate household registration
		if (!session.user.householdId) {
			if (process.env.NODE_ENV === "development") {
			}
			return NextResponse.json(
				{
					error: "Bad Request",
					message: "Household registration required",
				},
				{ status: 400 },
			);
		}

		const householdId = Number.parseInt(session.user.householdId, 10);

		// Get household head information through HouseholdMember relationship
		const householdMemberHead = await prisma.householdMember.findFirst({
			where: {
				householdId,
				relationship: "head",
				isCurrent: true,
			},
			include: {
				member: {
					include: {
						sacraments: true,
					},
				},
			},
		});

		if (!householdMemberHead?.member) {
			return NextResponse.json(
				{ error: "Household head not found" },
				{ status: 404 },
			);
		}

		const householdHead = householdMemberHead.member;

		// Get all current household members count
		const totalMembers = await prisma.householdMember.count({
			where: {
				householdId,
				isCurrent: true,
			},
		});

		// Get census form data for progress tracking
		const censusForm = await prisma.censusForm.findFirst({
			where: {
				householdId,
				censusYearId: Number.parseInt(session.user.censusYearId, 10),
			},
		});

		// Calculate completion states (6 steps total - household registration auto-complete + 5 manual)
		const completionState = {
			householdRegistration: true, // Always true after login (auto-complete)
			hobbies: !!(householdHead.hobby && householdHead.hobby.trim().length > 0),
			occupation: !!(
				householdHead.occupation && householdHead.occupation.trim().length > 0
			),
			sacraments: householdHead.sacraments.length > 0,
			familyMembers:
				totalMembers > 1 || (censusForm?.addMemberAttempted ?? false), // Added members OR clicked Add Member button
			communityFeedback: !!(
				censusForm?.householdComment &&
				censusForm.householdComment.trim().length > 0
			),
		};

		// Calculate progress percentage using step percentages constant
		let progress: number = STEP_PERCENTAGES.householdRegistration; // Start with household registration (auto-complete)
		if (completionState.hobbies) {
			progress += STEP_PERCENTAGES.hobbies;
		}
		if (completionState.occupation) {
			progress += STEP_PERCENTAGES.occupation;
		}
		if (completionState.sacraments) {
			progress += STEP_PERCENTAGES.sacraments;
		}
		if (completionState.familyMembers) {
			progress += STEP_PERCENTAGES.familyMembers;
		}
		if (completionState.communityFeedback) {
			progress += STEP_PERCENTAGES.communityFeedback;
		}

		// Ensure progress doesn't exceed 100%
		progress = Math.min(progress, 100);

		return NextResponse.json(
			{
				success: true,
				data: {
					progress,
					completionState,
					householdId,
					totalMembers,
				},
			},
			{
				headers: {
					"Cache-Control": "no-store, no-cache, must-revalidate",
					Pragma: "no-cache",
				},
			},
		);
	} catch (_error) {
		return NextResponse.json(
			{
				error: "Internal server error",
				message: "Unable to fetch progress data",
			},
			{
				status: 500,
				headers: {
					"Cache-Control": "no-store, no-cache, must-revalidate",
					Pragma: "no-cache",
				},
			},
		);
	}
}
