import { type NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { censusAuthOptions } from "@/lib/census-auth/census-auth-options";
import { prisma } from "@/lib/db/prisma";

/**
 * POST /api/census/add-member-attempted
 * Mark that user has clicked the "Add Member" button for progress tracking
 */
export async function POST(_request: NextRequest) {
  try {
    // Get session
    const session = await getServerSession(censusAuthOptions);

    if (!session?.user?.householdId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const householdId = Number.parseInt(session.user.householdId, 10);
    const censusYearId = Number.parseInt(session.user.censusYearId, 10);

    // Update or create census form record
    await prisma.censusForm.upsert({
      where: {
        householdId_censusYearId: {
          householdId,
          censusYearId,
        },
      },
      update: {
        addMemberAttempted: true,
        lastUpdated: new Date(),
      },
      create: {
        householdId,
        censusYearId,
        addMemberAttempted: true,
        status: "in_progress",
        lastUpdated: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      message: "Add member attempted flag updated",
    });
  } catch (_error) {
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
