"use client";

import { signIn, signOut, useSession } from "next-auth/react";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { useMessage } from "@/hooks/useMessage";
import { clearCensusLocalStorage } from "@/lib/utils/census-storage-cleanup";
import { useAuthSystem } from "@/providers/combined-auth-provider";
import { useCensusRateLimit } from "./use-census-rate-limit";

export function useCensusAuth() {
  // Use the census auth session with the correct basePath
  const {
    data: session,
    status,
    update,
  } = useSession({
    required: false,
  });

  // Separate state for form submission loading
  const [isSubmitting, setIsSubmitting] = useState(false);
  // Add state to track if session has been refreshed after login
  const [sessionRefreshed, setSessionRefreshed] = useState(false);

  // const router = useRouter(); // Not currently used but kept for future navigation needs
  const { showError, showSuccess } = useMessage();
  const { setActiveSystem } = useAuthSystem();
  const rateLimitHook = useCensusRateLimit();
  const tAuth = useTranslations("auth");

  // NOTE: Removed setActiveSystem call - now handled by pathname-based logic in CombinedAuthProvider
  // This prevents race conditions and competing state updates

  // Force a session refresh when the status changes to authenticated
  // This ensures we have the latest session data after login
  useEffect(() => {
    // Public pages that should not refresh the session
    const publicPages = ["/help", "/privacy-policy", "/terms-of-service"];
    const pathname = window.location.pathname;
    const isPublicPage = publicPages.includes(pathname);

    // Skip session refresh on public pages to avoid conflicts
    if (isPublicPage) {
      setSessionRefreshed(true);
      return;
    }

    const refreshSession = async () => {
      if (status === "authenticated" && !sessionRefreshed) {
        try {
          // Force NextAuth to update the session
          // This will get the latest session data from the server
          await update();
        } catch (error) {
          console.error("Failed to refresh session:", error);
        } finally {
          // Always set sessionRefreshed to true to prevent infinite refresh attempts
          setSessionRefreshed(true);
        }
      }
    };

    refreshSession();
  }, [status, sessionRefreshed, update]);

  const isAuthenticated =
    status === "authenticated" && session?.user?.role === "household" && session?.user?.id !== "";
  // This state is only for NextAuth session initialisation
  const isLoading = status === "loading" || (status === "authenticated" && !sessionRefreshed);

  // Handle session errors (when account was deleted)
  useEffect(() => {
    // If status is 'unauthenticated' but we were previously authenticated,
    // it might be due to an account deletion error
    if (status === "unauthenticated" && typeof window !== "undefined") {
      const urlParams = new URLSearchParams(window.location.search);
      const error = urlParams.get("error");

      if (error === "ACCOUNT_DELETED") {
        // Clear all census-related localStorage data when account deletion is detected
        clearCensusLocalStorage();
        window.location.href =
          "/api/census/auth/toast-redirect?reason=account_deleted&redirectTo=/";
      }
    }
  }, [status]);

  /**
   * Sign in with a census code
   */
  const signInWithCode = async (code: string) => {
    try {
      // Set submitting state to true when starting the sign-in process
      setIsSubmitting(true);
      // Reset session refreshed state before login
      setSessionRefreshed(false);

      if (process.env.NODE_ENV === "development") {
        console.log("Attempting to sign in with census code");
      }

      // Make sure we're using the census auth endpoint
      const result = await signIn("credentials", {
        code,
        redirect: false,
        // Ensure we're using the census auth endpoint
        signinUrl: "/api/census/auth/signin",
        callbackUrl: `/census/${code}`,
      });

      if (result?.error) {
        // Handle specific errors
        if (result.error === "CensusClosed") {
          showError("CensusClosed", "census");
        } else if (result.error === "TooManyAttempts") {
          showError("TooManyAttempts", "census");
          // Refresh rate limit status to get updated lockout time
          await rateLimitHook.checkStatus();
        } else {
          showError("InvalidCode", "census");
        }
        return false;
      }

      if (result?.url) {
        // Force a session update to ensure we have the latest data
        if (process.env.NODE_ENV === "development") {
          console.log("Census login successful, updating session data");
        }
        await update();
        setSessionRefreshed(true);

        // Show success message
        showSuccess("censusCodeValidated");

        // Use a small delay to ensure the session update has time to complete
        // Then use a full page reload instead of client-side navigation
        setTimeout(() => {
          if (process.env.NODE_ENV === "development") {
            console.log("Redirecting to census form with full page reload");
          }
          window.location.href = result.url || "/";
        }, 500);

        return true;
      }

      return false;
    } catch (error) {
      if (process.env.NODE_ENV === "development") {
        console.error("Census authentication error:", error);
      }
      showError("AuthenticationError");
      return false;
    } finally {
      // Always reset the submitting state when the operation completes
      setIsSubmitting(false);
    }
  };

  /**
   * Sign out from census session
   */
  const signOutFromCensus = async () => {
    await signOut({
      callbackUrl: "/",
    });
  };

  /**
   * Force a refresh of the session from the server
   * This triggers the JWT callback to check for updates in the database
   *
   * @returns The updated session
   */
  const updateSession = async () => {
    try {
      // Call the NextAuth.js update function without parameters
      // This will refresh the session from the server
      const result = await update();
      return result;
    } catch (error) {
      throw error;
    }
  };

  return {
    session,
    status,
    isAuthenticated,
    isLoading,
    isSubmitting,
    signInWithCode,
    signOutFromCensus,
    updateSession, // Use our enhanced update function
    rateLimit: rateLimitHook, // Expose rate limiting functionality
  };
}
