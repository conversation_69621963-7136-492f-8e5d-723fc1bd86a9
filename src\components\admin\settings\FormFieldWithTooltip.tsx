"use client";

import { Info } from "lucide-react";
import type { HTMLInputTypeAttribute } from "react";
import type {
	FieldError,
	FieldErrorsImpl,
	FieldValues,
	Merge,
	Path,
	UseFormRegister,
} from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

interface FormFieldWithTooltipProps<
	TFormValues extends FieldValues = FieldValues,
> {
	id: Path<TFormValues>;
	label: string;
	tooltip?: string;
	children?: React.ReactNode;
	helperText?: string;
	required?: boolean;
	// For direct integration with react-hook-form
	type?: HTMLInputTypeAttribute;
	placeholder?: string;
	disabled?: boolean;
	register?: UseFormRegister<TFormValues>;
	error?:
		| FieldError
		| Merge<FieldError, FieldErrorsImpl<Record<string, unknown>>>;
	className?: string;
}

export function FormFieldWithTooltip<
	TFormValues extends FieldValues = FieldValues,
>({
	id,
	label,
	tooltip,
	children,
	helperText,
	required = false,
	// Form field props
	type = "text",
	placeholder,
	disabled = false,
	register,
	error,
	className,
}: FormFieldWithTooltipProps<TFormValues>) {
	// Determine if we should show error message from the error object
	const errorMessage = error?.message;
	const showError = !!errorMessage;

	// If helperText is not provided but error exists, use error message
	const displayHelperText = showError ? errorMessage : helperText;

	return (
		<div className={cn("space-y-2", className)}>
			<div className="flex items-center justify-between">
				<Label className="font-medium text-sm" htmlFor={id}>
					{label}
					{required && <span className="ml-1 text-destructive">*</span>}
				</Label>
				{tooltip && (
					<TooltipProvider>
						<Tooltip>
							<TooltipTrigger asChild>
								<Info className="h-4 w-4 cursor-help text-muted-foreground" />
							</TooltipTrigger>
							<TooltipContent>
								<p className="w-80 text-sm">{tooltip}</p>
							</TooltipContent>
						</Tooltip>
					</TooltipProvider>
				)}
			</div>

			{/* If register is provided, render an Input with register */}
			{register ? (
				<Input
					className={cn(showError && "border-destructive")}
					disabled={disabled}
					id={id}
					placeholder={placeholder}
					type={type}
					{...(register ? register(id) : {})}
				/>
			) : (
				// Otherwise, render the children
				<div>{children}</div>
			)}

			{displayHelperText && (
				<p
					className={cn(
						"text-xs",
						showError ? "text-destructive" : "text-muted-foreground",
					)}
				>
					{String(
						typeof displayHelperText === "string"
							? displayHelperText
							: errorMessage || "Validation error",
					)}
				</p>
			)}
		</div>
	);
}
