import Link from "next/link";
import { useTranslations } from "next-intl";
import { LanguageSelector } from "@/components/language/language-selector";
import { ThemeToggle } from "@/components/theme/theme-toggle";

export function Footer() {
	const currentYear = new Date().getFullYear();
	const tCommon = useTranslations("common");
	const tNav = useTranslations("navigation");
	const tLegal = useTranslations("legal");

	return (
		<footer className="mt-auto w-full border-t bg-background">
			<div className="container mx-auto px-4 py-4">
				{/* Bottom section with copyright and language selector */}
				<div className="flex flex-col items-center justify-between text-muted-foreground text-sm md:flex-row">
					<div className="mb-4 md:mb-0">
						{tCommon("copyright", { year: currentYear.toString() })}
					</div>

					{/* Links and language selector */}
					<div className="flex flex-wrap items-center justify-center">
						<div className="flex items-center gap-4">
							<Link
								className="text-muted-foreground text-sm hover:text-primary hover:underline"
								href="/help"
							>
								{tNav("help")}
							</Link>
							<Link
								className="text-muted-foreground text-sm hover:text-primary hover:underline"
								href="/privacy-policy"
							>
								{tLegal("privacyPolicy")}
							</Link>
							<Link
								className="text-muted-foreground text-sm hover:text-primary hover:underline"
								href="/terms"
							>
								{tLegal("termsOfService")}
							</Link>
						</div>
						<div className="ml-4 flex items-center">
							<LanguageSelector />
							<div className="ml-2">
								<ThemeToggle />
							</div>
						</div>
					</div>
				</div>
			</div>
		</footer>
	);
}
