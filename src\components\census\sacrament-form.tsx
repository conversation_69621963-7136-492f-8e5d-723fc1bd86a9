"use client";

import { <PERSON>ertCircle, Calendar, CheckCircle2, <PERSON>Text, MapPin, Trash2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { memo } from "react";
import { type Control, Controller } from "react-hook-form";
//import { Separator } from '@/components/ui/separator';
import { Button } from "@/components/ui/button";
import { DatePicker } from "@/components/ui/date-picker";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import type {
  ClientCombinedMemberSacramentFormValues,
  ClientSacramentFormValues,
} from "@/lib/validation/client/census-client";

interface SacramentType {
  id: number;
  code: string;
  name: string;
  description?: string;
}

interface SacramentFormProps {
  index: number;
  sacramentData: ClientSacramentFormValues;
  onFieldChange: (
    index: number,
    fieldName: keyof ClientSacramentFormValues,
    value: number | string | Date | null,
  ) => void;
  onRemove: (index: number) => void;
  sacramentTypes: SacramentType[];
  allUsedSacramentTypeIds: number[];
  isTypeDisabledInOthers: (typeId: number, currentSacramentIndex: number) => boolean;
  errors?: Partial<Record<keyof ClientSacramentFormValues, { message?: string }>>;
  control: Control<ClientCombinedMemberSacramentFormValues>;
}

export const SacramentForm = memo(function SacramentForm({
  index,
  sacramentData,
  onFieldChange,
  onRemove,
  sacramentTypes,
  // allUsedSacramentTypeIds is not used directly in this component
  // but is part of the interface for consistency
  isTypeDisabledInOthers,
  errors,
  control,
}: SacramentFormProps) {
  const t = useTranslations("census");
  const tForms = useTranslations("forms");
  const tSacraments = useTranslations("sacraments");

  const currentSacramentTypeDetails = sacramentTypes.find(
    (st) => st.id === sacramentData.sacramentTypeId,
  );
  const currentSacramentTypeName = currentSacramentTypeDetails
    ? tSacraments(currentSacramentTypeDetails.code as any)
    : tForms("selectType");
  const isTypeSelected = !!sacramentData.sacramentTypeId;

  let displayableSacramentTypes: SacramentType[];
  if (isTypeSelected) {
    if (currentSacramentTypeDetails) {
      displayableSacramentTypes = [currentSacramentTypeDetails];
    } else {
      displayableSacramentTypes = [];
    }
  } else {
    displayableSacramentTypes = sacramentTypes.filter(
      (st) => !isTypeDisabledInOthers(st.id, index),
    );
  }

  return (
    <div>
      <div className="mb-3 flex items-center justify-between">
        <Label className="font-medium text-sm" htmlFor={`sacramentTypeId-${index}`}>
          {tForms("sacramentType")}
        </Label>
        <Button
          aria-label={t("removeSacrament")}
          className="h-7 w-7 rounded-full p-0 text-muted-foreground transition-colors hover:bg-destructive/10 hover:text-destructive"
          onClick={() => onRemove(index)}
          size="sm"
          variant="ghost"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>

      {sacramentTypes.length > 0 &&
        !isTypeSelected &&
        sacramentTypes.every((type) => isTypeDisabledInOthers(type.id, index)) && (
          <div className="mb-3 flex items-start gap-2 rounded-md border border-amber-200 bg-amber-50 p-2 dark:border-amber-800 dark:bg-amber-950">
            <AlertCircle className="mt-0.5 h-4 w-4 text-amber-600 dark:text-amber-400" />
            <p className="text-amber-800 text-sm dark:text-amber-300">
              {t("allSacramentTypesInUse")}
            </p>
          </div>
        )}

      <div className="mb-4 flex flex-wrap gap-2">
        {displayableSacramentTypes.map((type) => {
          const isSelectedInThisForm = sacramentData.sacramentTypeId === type.id;
          return (
            <Button
              className={cn(
                "h-auto rounded-md px-3 py-1 transition-all",
                "hover:bg-muted/20",
                "border border-muted-foreground/20",
              )}
              key={type.id}
              onClick={() => {
                onFieldChange(index, "sacramentTypeId", type.id);
              }}
              size="sm"
              type="button"
              variant={isSelectedInThisForm ? "secondary" : "outline"}
            >
              <span className="flex items-center gap-1.5">
                {tSacraments(type.code as any)}
                {isSelectedInThisForm && <CheckCircle2 className="ml-0.5 h-3.5 w-3.5" />}
              </span>
            </Button>
          );
        })}
      </div>
      {errors?.sacramentTypeId && (
        <p className="mt-2 text-destructive text-xs">{errors.sacramentTypeId.message}</p>
      )}

      {isTypeSelected && (
        <div className="mt-4 space-y-4">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
              <Label
                className="mb-2 flex items-center gap-1.5 font-medium text-sm"
                htmlFor={`date-${index}`}
              >
                <Calendar className="h-3.5 w-3.5 text-primary" />
                {t("date")}
              </Label>
              <Controller
                control={control}
                name={`sacraments.${index}.date` as const}
                render={({ field }) => (
                  <DatePicker
                    className={errors?.date ? "border-destructive" : ""}
                    date={
                      field.value instanceof Date
                        ? field.value
                        : field.value
                          ? new Date(field.value)
                          : null
                    }
                    placeholderText={tForms("selectDatePlaceholder")}
                    preventFutureDates={true}
                    setDate={(newDate) => field.onChange(newDate)}
                  />
                )}
              />
              {errors?.date && (
                <p className="mt-1 text-destructive text-xs">{errors.date.message}</p>
              )}
            </div>

            <div>
              <Label
                className="mb-2 flex items-center gap-1.5 font-medium text-sm"
                htmlFor={`place-${index}`}
              >
                <MapPin className="h-3.5 w-3.5 text-primary" />
                {t("place")}
              </Label>
              <Controller
                control={control}
                name={`sacraments.${index}.place` as const}
                render={({ field }) => (
                  <Input
                    id={`place-${index}`}
                    {...field}
                    className={cn(
                      "h-10", // Match the height of DatePicker
                      errors?.place ? "border-destructive" : "",
                    )}
                    placeholder={tForms("enterPlaceOfSacramentPlaceholder")}
                  />
                )}
              />
              {errors?.place && (
                <p className="mt-1 text-destructive text-xs">{errors.place.message}</p>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
});
