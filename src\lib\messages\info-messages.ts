/**
 * Info message translation mappings
 * Maps info codes to translation keys in the 'common' namespace
 * Used by useMessage hook and server-side translation routes
 */

export const infoMessageKeys: Record<string, string> = {
  // Process information
  operationInProgress: "operationInProgress",
  dataLoading: "dataLoading",
  dataLoaded: "dataLoadedSuccessfully",

  // Status information
  systemReady: "systemReady",
  connectionEstablished: "connectionEstablished",

  // User guidance
  firstTimeSetup: "firstTimeSetupInfo",
  featureAvailable: "featureAvailableInfo",

  // Authentication & Security
  twoFactorRequired: "twoFactorRequired",

  // Sacrament Management
  sacramentLimitReached: "sacramentLimitReached",
  allSacramentsRecorded: "allSacramentsRecorded",
  cannotAddMoreSacraments: "cannotAddMoreSacraments",
  allSacramentTypesAdded: "allSacramentTypesAdded",

  // Default fallback
  default: "infoMessage",
};
