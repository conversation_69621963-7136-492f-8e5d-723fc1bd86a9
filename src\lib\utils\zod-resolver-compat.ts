/**
 * Zod v4 compatibility utilities for react-hook-form zodResolver
 *
 * This file provides type-safe compatibility between Zod v4 schemas and zodResolver.
 * This is a PERMANENT compatibility layer, not temporary migration code.
 *
 * Purpose: Bridge the gap between Zod v4 schemas and react-hook-form's zodResolver
 * Status: Production-ready permanent utility
 * Created: December 2024 during Zod v4 migration
 */

import { zodResolver as originalZodResolver } from "@hookform/resolvers/zod";
import type { FieldValues, Resolver } from "react-hook-form";
import type { z } from "zod/v4";

/**
 * Type-safe zodResolver wrapper for Zod v4 schemas
 * This function provides compatibility between Zod v4 schemas and react-hook-form
 */
export function zodResolver<TFieldValues extends FieldValues = FieldValues, TContext = object>(
  // Accept any Zod schema type to handle function-generated schemas
  schema: z.ZodTypeAny,
): Resolver<TFieldValues, TContext> {
  // Cast the schema to be compatible with the original zodResolver
  // This is safe because Zod v4 schemas are structurally compatible with v3
  return originalZodResolver(schema as any) as Resolver<TFieldValues, TContext>;
}

/**
 * Type for Zod v4 compatible field errors
 * This extends the standard FieldError to handle Zod v4 error structures
 */
export type ZodV4FieldError = {
  message?: string;
  type?: string;
  ref?: HTMLElement;
};

/**
 * Helper function to safely extract error message from Zod v4 field errors
 */
export function getFieldErrorMessage(error: unknown): string {
  if (typeof error === "string") {
    return error;
  }
  if (error && typeof error === "object" && "message" in error) {
    return String(error.message);
  }
  return "Validation error";
}
