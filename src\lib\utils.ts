import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Environment-aware logging utility
 * Only logs in development environment to keep production console clean
 * SECURITY: Never log sensitive data like passwords, tokens, or user IDs
 */
export const devLog = {
  log: (...args: unknown[]) => {
    if (process.env.NODE_ENV === "development") {
      console.log(...args);
    }
  },
  warn: (...args: unknown[]) => {
    if (process.env.NODE_ENV === "development") {
      console.warn(...args);
    }
  },
  error: (...args: unknown[]) => {
    // Errors should be logged to proper logging service in production
    // For now, suppress all console output in production for security
    if (process.env.NODE_ENV === "development") {
      console.error(...args);
    }
  },
  info: (...args: unknown[]) => {
    if (process.env.NODE_ENV === "development") {
      console.info(...args);
    }
  },
  debug: (...args: unknown[]) => {
    if (process.env.NODE_ENV === "development") {
      console.debug(...args);
    }
  },
};
