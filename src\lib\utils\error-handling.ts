/**
 * Utility functions for safe error handling
 * Updated for Zod v4 compatibility while maintaining v3 support
 */

/**
 * Safely extracts the message from an error object
 * @param error The error object (can be of any type)
 * @param defaultMessage Default message to return if the error doesn't have a message property
 * @returns The error message or the default message
 */
export function getErrorMessage(
  error: unknown,
  defaultMessage = "An unknown error occurred",
): string {
  if (error instanceof Error) {
    return error.message;
  }

  if (
    typeof error === "object" &&
    error !== null &&
    "message" in error &&
    typeof error.message === "string"
  ) {
    return error.message;
  }

  if (typeof error === "string") {
    return error;
  }

  return defaultMessage;
}

/**
 * Checks if an error is a ZodError (compatible with both v3 and v4)
 * @param error The error object (can be of any type)
 * @returns True if the error is a ZodError, false otherwise
 */
export function isZodError(error: unknown): boolean {
  return (
    typeof error === "object" && error !== null && "name" in error && error.name === "ZodError"
  );
}

/**
 * Safely extracts the errors array from a ZodError (compatible with both v3 and v4)
 * @param error The error object (can be of any type)
 * @returns The errors array or an empty array if the error doesn't have an errors property
 */
export function getZodErrorDetails(error: unknown): unknown {
  if (typeof error === "object" && error !== null && "issues" in error) {
    return error.issues; // v4 uses 'issues' instead of 'errors'
  }

  // Fallback for v3 compatibility
  if (typeof error === "object" && error !== null && "errors" in error) {
    return error.errors;
  }

  return [];
}

/**
 * Checks if an error is a PostgreSQL duplicate entry error
 * @param error The error object (can be of any type)
 * @returns True if the error is a duplicate entry error, false otherwise
 */
export function isPostgreSQLDuplicateError(error: unknown): boolean {
  return (
    typeof error === "object" &&
    error !== null &&
    "code" in error &&
    (error.code === "23505" || error.code === "P2002") // PostgreSQL unique violation or Prisma unique constraint
  );
}

/**
 * Formats a ZodError into a user-friendly string (Zod v4 compatible)
 * @param error The ZodError object
 * @returns A formatted error string or null if not a ZodError
 */
export function formatZodError(error: unknown): string | null {
  if (!isZodError(error)) {
    return null;
  }

  const issues = getZodErrorDetails(error) as Array<{
    message: string;
    path?: Array<string | number>;
  }>;
  if (!Array.isArray(issues) || issues.length === 0) {
    return "Validation error occurred";
  }

  // Format issues into readable messages
  return issues
    .map((issue) => {
      const path =
        Array.isArray(issue.path) && issue.path.length > 0 ? ` at ${issue.path.join(".")}` : "";
      return `${issue.message}${path}`;
    })
    .join("; ");
}

/**
 * Extracts field-specific errors from a ZodError (Zod v4 compatible)
 * @param error The ZodError object
 * @returns An object mapping field paths to error messages
 */
export function getZodFieldErrors(error: unknown): Record<string, string[]> {
  if (!isZodError(error)) {
    return {};
  }

  const issues = getZodErrorDetails(error) as Array<{
    message: string;
    path?: Array<string | number>;
  }>;
  if (!Array.isArray(issues)) {
    return {};
  }

  const fieldErrors: Record<string, string[]> = {};

  issues.forEach((issue) => {
    if (Array.isArray(issue.path) && issue.path.length > 0) {
      const fieldPath = issue.path.join(".");
      if (!fieldErrors[fieldPath]) {
        fieldErrors[fieldPath] = [];
      }
      fieldErrors[fieldPath].push(issue.message);
    }
  });

  return fieldErrors;
}
