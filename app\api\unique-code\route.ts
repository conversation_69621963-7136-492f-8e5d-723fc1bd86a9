import { type NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { authOptions } from "@/lib/auth/auth-options";
import { getActiveCensusYear } from "@/lib/db/census-years";
import { prisma } from "@/lib/db/prisma";
import {
  deleteUniqueCode,
  generateUniqueCodes,
  getUniqueCodeById,
  getUniqueCodesCount,
} from "@/lib/db/unique-codes";
import { getZodErrorDetails, isZodError } from "@/lib/utils/error-handling";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";
import {
  createDeleteUniqueCodesSchema,
  createGenerateUniqueCodesSchema,
  searchUniqueCodesSchema,
} from "@/lib/validation/unique-code";

/**
 * GET endpoint to fetch unique codes with optional filtering
 */
export async function GET(request: NextRequest) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const tAdmin = await getTranslations({ locale, namespace: "admin" });
  const tErrors = await getTranslations({ locale, namespace: "errors" });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require admin authentication
    if (!session || session.user.role !== "admin") {
      return NextResponse.json({ error: tAdmin("unauthorized") }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const searchTerm = searchParams.get("searchTerm") || "";
    const isAssigned = searchParams.get("isAssigned")
      ? searchParams.get("isAssigned") === "true"
      : undefined;
    const censusYearId = searchParams.get("censusYearId")
      ? Number.parseInt(searchParams.get("censusYearId")!, 10)
      : undefined;
    const page = searchParams.get("page") ? Number.parseInt(searchParams.get("page")!, 10) : 1;
    const pageSize = searchParams.get("pageSize")
      ? Number.parseInt(searchParams.get("pageSize")!, 10)
      : 20;

    // Validate query parameters
    try {
      searchUniqueCodesSchema.parse({
        searchTerm,
        isAssigned,
        censusYearId,
        page,
        pageSize,
      });
    } catch (validationError) {
      if (isZodError(validationError)) {
        return NextResponse.json(
          {
            error: tErrors("validationFailed"),
            details: getZodErrorDetails(validationError),
          },
          { status: 400 },
        );
      }
    }

    // Get total count using optimised count function
    const total = await getUniqueCodesCount(searchTerm, isAssigned, censusYearId);

    // Build Prisma query for data retrieval
    const where: Record<string, unknown> = {};

    // Add search term filter
    if (searchTerm) {
      where.code = { contains: searchTerm, mode: "insensitive" };
    }

    // Add assignment status filter
    if (isAssigned !== undefined) {
      where.isAssigned = isAssigned;
    }

    // Add census year filter
    if (censusYearId) {
      where.censusYearId = censusYearId;
    }

    // Execute the query with Prisma - SECURITY: Only select non-sensitive fields
    const uniqueCodes = await prisma.uniqueCode.findMany({
      where,
      select: {
        id: true,
        code: true,
        isAssigned: true,
        assignedAt: true,
        householdId: true,
        censusYearId: true,
        createdAt: true,
        updatedAt: true,
        // SECURITY: Explicitly exclude validation fields (validationStart, validationEnd, validationHash)
        censusYear: {
          select: {
            year: true,
            isActive: true,
          },
        },
      },
      orderBy: { id: "desc" },
      take: pageSize,
      skip: (page - 1) * pageSize,
    });

    // Transform data to match expected format
    const transformedCodes = uniqueCodes.map((uc) => ({
      id: uc.id,
      code: uc.code,
      isAssigned: uc.isAssigned,
      assignedAt: uc.assignedAt,
      householdId: uc.householdId,
      censusYearId: uc.censusYearId,
      createdAt: uc.createdAt,
      updatedAt: uc.updatedAt,
      census_year: uc.censusYear?.year,
      is_active_year: uc.censusYear?.isActive,
    }));

    // Return the results with pagination info
    return NextResponse.json({
      data: transformedCodes,
      pagination: {
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize),
      },
    });
  } catch (_error) {
    return NextResponse.json({ error: tAdmin("failedToFetchUniqueCodes") }, { status: 500 });
  }
}

/**
 * POST endpoint to generate new unique codes
 */
export async function POST(request: NextRequest) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const tAdmin = await getTranslations({ locale, namespace: "admin" });
  const tErrors = await getTranslations({ locale, namespace: "errors" });
  const _tNotifications = await getTranslations({
    locale,
    namespace: "notifications",
  });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require admin authentication
    if (!session || session.user.role !== "admin") {
      return NextResponse.json({ error: tAdmin("unauthorized") }, { status: 401 });
    }

    // Parse and validate request body
    const data = await request.json();

    // Ensure data types are correct
    const formData = {
      count: Number(data.count),
      censusYearId: Number(data.censusYearId),
    };

    // Always use the active census year regardless of what was provided
    const activeCensusYear = await getActiveCensusYear();
    if (!activeCensusYear) {
      return NextResponse.json({ error: tErrors("noActiveCensusYearFound") }, { status: 400 });
    }

    // Override any provided census year ID with the active one
    formData.censusYearId = activeCensusYear.id;

    // Environment-aware logging - only in development
    if (process.env.NODE_ENV === "development") {
    }

    try {
      // Create validation schema with translations
      const generateUniqueCodesSchema = await createGenerateUniqueCodesSchema(locale);
      generateUniqueCodesSchema.parse(formData);

      // Environment-aware logging - only in development
      if (process.env.NODE_ENV === "development") {
      }
    } catch (validationError) {
      if (isZodError(validationError)) {
        return NextResponse.json(
          {
            error: tErrors("validationFailed"),
            details: getZodErrorDetails(validationError),
          },
          { status: 400 },
        );
      }
      throw validationError;
    }

    // Get admin ID from session
    const adminId = Number.parseInt(session.user.id, 10);

    // Get client IP address
    const ipAddress =
      request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "unknown";

    // Generate unique codes and get their IDs
    let newCodeIds: number[] = [];
    try {
      // This function now returns the IDs of the newly generated codes
      newCodeIds = await generateUniqueCodes(formData.count, formData.censusYearId);
    } catch (generateError) {
      const errorMessage =
        generateError instanceof Error ? generateError.message : "Unknown error occurred";
      return NextResponse.json(
        {
          error: `${tErrors("failedToGenerateUniqueCodes")}: ${errorMessage}`,
        },
        { status: 500 },
      );
    }

    // Log the action using Prisma
    try {
      await prisma.auditLog
        ?.create({
          data: {
            userType: "admin",
            userId: adminId,
            action: "generate-unique-codes",
            entityType: "unique_codes",
            entityId: null,
            newValues: JSON.stringify({
              count: formData.count,
              censusYearId: formData.censusYearId,
            }),
            ipAddress,
          },
        })
        .catch(() => null);
    } catch (_logError) {
      // If logging fails, just log to console but don't fail the request
      if (process.env.NODE_ENV === "development") {
      }
    }

    // Fetch only the newly generated codes using their IDs with Prisma
    const newCodes = await prisma.uniqueCode.findMany({
      where: {
        id: { in: newCodeIds },
      },
      include: {
        censusYear: {
          select: {
            year: true,
            isActive: true,
          },
        },
      },
      orderBy: { id: "asc" },
    });

    // Transform to match expected format
    const codesWithYearInfo = newCodes.map((code) => ({
      id: code.id,
      code: code.code,
      isAssigned: code.isAssigned,
      assignedAt: code.assignedAt,
      householdId: code.householdId,
      censusYearId: code.censusYearId,
      createdAt: code.createdAt,
      updatedAt: code.updatedAt,
      census_year: code.censusYear?.year || null,
      is_active_year: code.censusYear?.isActive,
    }));

    // Success message handled client-side for consistency with other admin operations
    return NextResponse.json({
      success: true,
      codes: codesWithYearInfo,
    });
  } catch (error) {
    // Handle validation errors
    if (isZodError(error)) {
      return NextResponse.json(
        {
          error: tAdmin("validationFailed"),
          details: getZodErrorDetails(error),
        },
        { status: 400 },
      );
    }

    return NextResponse.json({ error: tAdmin("failedToGenerateUniqueCodes") }, { status: 500 });
  }
}

/**
 * DELETE endpoint to delete unused unique codes
 */
export async function DELETE(request: NextRequest) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const tAdmin = await getTranslations({ locale, namespace: "admin" });
  const tErrors = await getTranslations({ locale, namespace: "errors" });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require admin authentication
    if (!session || session.user.role !== "admin") {
      return NextResponse.json({ error: tAdmin("unauthorized") }, { status: 401 });
    }

    // Parse and validate request body with translations
    const data = await request.json();
    const deleteUniqueCodesSchema = await createDeleteUniqueCodesSchema(locale);
    const validatedData = deleteUniqueCodesSchema.parse(data);

    // Get admin ID from session
    const adminId = Number.parseInt(session.user.id, 10);

    // Get client IP address
    const ipAddress =
      request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "unknown";

    // Check if any of the codes are already assigned
    const assignedCodes = [];
    for (const codeId of validatedData.codeIds) {
      const code = await getUniqueCodeById(codeId);
      if (code?.isAssigned) {
        assignedCodes.push(code.code);
      }
    }

    if (assignedCodes.length > 0) {
      return NextResponse.json(
        {
          error: tErrors("cannotDeleteAssignedCodes"),
          assignedCodes,
        },
        { status: 400 },
      );
    }

    // Delete the codes
    for (const codeId of validatedData.codeIds) {
      await deleteUniqueCode(codeId);
    }

    // Log the action using Prisma
    try {
      await prisma.auditLog
        ?.create({
          data: {
            userType: "admin",
            userId: adminId,
            action: "delete-unique-codes",
            entityType: "unique_codes",
            entityId: null,
            newValues: JSON.stringify({ codeIds: validatedData.codeIds }),
            ipAddress,
          },
        })
        .catch(() => null);
    } catch (_logError) {}

    // Success message handled client-side for consistency with other admin operations
    return NextResponse.json({
      success: true,
    });
  } catch (error) {
    // Handle validation errors
    if (isZodError(error)) {
      return NextResponse.json(
        {
          error: tErrors("validationFailed"),
          details: getZodErrorDetails(error),
        },
        { status: 400 },
      );
    }

    return NextResponse.json({ error: tErrors("failedToDeleteUniqueCodes") }, { status: 500 });
  }
}
