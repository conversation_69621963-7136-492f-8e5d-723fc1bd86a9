/**
 * Member Database Operations with Prisma - Consolidated Professional Implementation
 *
 * This file consolidates members.ts and members-prisma.ts into a single source of truth.
 * Features:
 * - Consistent data structures and search functionality
 * - Professional Prisma ORM implementation
 * - Comprehensive member management operations
 * - Type-safe operations throughout
 * - Proper data transformation utilities
 */

import type {
	CensusYear,
	Household,
	HouseholdMember,
	Member,
	Sacrament,
	SacramentType,
} from "@prisma/client";
import { Prisma } from "@prisma/client";
import { getTranslations } from "next-intl/server";
import { calculateAge, getAgeGroup } from "../utils/date-calculations";
import { getLocaleFromCookies } from "../utils/server-messages";
import { prisma } from "./prisma";

/**
 * Custom error class for household head deletion attempts
 */
export class HouseholdHeadDeletionError extends Error {
	constructor(message: string) {
		super(message);
		this.name = "HouseholdHeadDeletionError";
	}
}

// Type definitions for complex member data
export interface IMemberWithRelations extends Member {
	householdMembers?: (HouseholdMember & {
		household: Household;
		censusYear: CensusYear;
	})[];
	sacraments?: (Sacrament & {
		sacramentType: SacramentType;
	})[];
	age?: number | null;
}

// Legacy compatibility - map old function names to new implementations
export async function getMembers(): Promise<Member[]> {
	return await prisma.member.findMany({
		orderBy: [{ lastName: "asc" }, { firstName: "asc" }],
	});
}

export async function getMemberById(id: number): Promise<Member | null> {
	return await prisma.member.findUnique({
		where: { id },
	});
}

export async function createMember(data: {
	firstName: string;
	lastName: string;
	dateOfBirth?: Date | null;
	gender: "male" | "female" | "other";
	mobilePhone: string;
	hobby?: string | null;
	occupation?: string | null;
}): Promise<Member> {
	return await prisma.member.create({
		data,
	});
}

export async function updateMember(
	id: number,
	data: Partial<{
		firstName: string;
		lastName: string;
		dateOfBirth: Date | null;
		gender: "male" | "female" | "other";
		mobilePhone: string;
		hobby: string | null;
		occupation: string | null;
	}>,
): Promise<Member | null> {
	try {
		return await prisma.member.update({
			where: { id },
			data,
		});
	} catch (error) {
		console.error("Error updating member:", error);
		return null;
	}
}

export async function deleteMember(id: number): Promise<boolean> {
	try {
		await prisma.member.delete({
			where: { id },
		});
		return true;
	} catch (error) {
		console.error("Error deleting member:", error);
		return false;
	}
}

export async function getMembersByHouseholdId(
	householdId: number,
	censusYearId?: number,
): Promise<IMemberWithRelations[]> {
	const members = await prisma.member.findMany({
		where: {
			householdMembers: {
				some: {
					householdId,
					...(censusYearId ? { censusYearId } : {}),
				},
			},
		},
		include: {
			householdMembers: {
				where: {
					householdId,
					...(censusYearId ? { censusYearId } : {}),
				},
				include: {
					household: true,
					censusYear: true,
				},
			},
		},
	});

	return members.map((member) => ({
		...member,
		age: calculateAge(member.dateOfBirth),
	}));
}

export async function searchMembers(
	searchTerm: string,
): Promise<IMemberWithRelations[]> {
	const members = await prisma.member.findMany({
		where: {
			OR: [
				{ firstName: { contains: searchTerm, mode: "insensitive" } },
				{ lastName: { contains: searchTerm, mode: "insensitive" } },
				{ mobilePhone: { contains: searchTerm } },
				{ hobby: { contains: searchTerm, mode: "insensitive" } },
				// Search by household suburb
				{
					householdMembers: {
						some: {
							household: {
								suburb: { contains: searchTerm, mode: "insensitive" },
							},
						},
					},
				},
			],
		},
		include: {
			householdMembers: {
				include: {
					household: true,
					censusYear: true,
				},
			},
		},
		orderBy: [{ lastName: "asc" }, { firstName: "asc" }],
	});

	return members.map((member) => ({
		...member,
		age: calculateAge(member.dateOfBirth),
	}));
}

/**
 * Get all members with optional filtering - Prisma implementation
 */
export async function getAllMembers(
	options: {
		limit?: number;
		offset?: number;
		searchTerm?: string;
		censusYearId?: number;
		includeRelationships?: boolean;
		includeSacraments?: boolean;
	} = {},
): Promise<IMemberWithRelations[]> {
	const {
		limit = 100,
		offset = 0,
		searchTerm = "",
		censusYearId,
		includeRelationships = false,
		includeSacraments = false,
	} = options;

	// Build include object based on options
	const include: Record<string, unknown> = {};

	if (includeRelationships) {
		include.householdMembers = {
			include: {
				household: true,
				censusYear: true,
			},
			where: censusYearId ? { censusYearId } : undefined,
		};
	}

	if (includeSacraments) {
		include.sacraments = {
			include: {
				sacramentType: true,
			},
		};
	}

	// Build where clause for search
	const where = searchTerm
		? {
				OR: [
					{ firstName: { contains: searchTerm, mode: "insensitive" } },
					{ lastName: { contains: searchTerm, mode: "insensitive" } },
					{ mobilePhone: { contains: searchTerm } },
				],
			}
		: {};

	// Execute query with Prisma
	const members = await prisma.member.findMany({
		where: where as Prisma.MemberWhereInput,
		include: Object.keys(include).length > 0 ? include : undefined,
		take: limit,
		skip: offset,
		orderBy: [{ lastName: "asc" }, { firstName: "asc" }],
	});

	// Calculate age for each member
	return members.map((member) => ({
		...member,
		age: calculateAge(member.dateOfBirth),
	}));
}

/**
 * Get members with household and sacrament details for admin portal - Simplified Prisma version
 */
export async function getMembersWithDetails(params: {
	searchTerm?: string;
	gender?: string;
	relationship?: string;
	censusYearId?: number;
	sacramentStatus?: string;
	minAge?: number;
	maxAge?: number;
	sortBy?: string;
	sortOrder?: string;
	page?: number;
	pageSize?: number;
}): Promise<{ members: unknown[]; total: number }> {
	const {
		searchTerm = "",
		gender,
		relationship,
		censusYearId,
		page = 1,
		pageSize = 20,
	} = params;

	// Calculate offset for pagination
	const offset = (page - 1) * pageSize;

	// Build where clause
	const where: Record<string, unknown> = {};

	// Search term filter - comprehensive search across multiple fields
	if (searchTerm) {
		where.OR = [
			{
				firstName: { contains: searchTerm, mode: Prisma.QueryMode.insensitive },
			},
			{
				lastName: { contains: searchTerm, mode: Prisma.QueryMode.insensitive },
			},
			{ mobilePhone: { contains: searchTerm } },
			{ hobby: { contains: searchTerm, mode: Prisma.QueryMode.insensitive } },
			// Search by household suburb
			{
				householdMembers: {
					some: {
						household: {
							suburb: {
								contains: searchTerm,
								mode: Prisma.QueryMode.insensitive,
							},
						},
					},
				},
			},
		];
	}

	// Gender filter
	if (gender) {
		where.gender = gender;
	}

	// Relationship and census year filters through household members
	if (relationship || censusYearId) {
		where.householdMembers = {
			some: {
				...(relationship ? { relationship } : {}),
				...(censusYearId ? { censusYearId } : {}),
			},
		};
	}

	// Get total count
	const total = await prisma.member.count({ where });

	// Get members with relationships
	const members = await prisma.member.findMany({
		where,
		include: {
			householdMembers: {
				include: {
					household: true,
					censusYear: true,
				},
			},
			sacraments: {
				include: {
					sacramentType: true,
				},
			},
		},
		take: pageSize,
		skip: offset,
		orderBy: [{ lastName: "asc" }, { firstName: "asc" }],
	});

	// Transform to expected format
	const transformedMembers = members.map((member) => ({
		memberId: member.id,
		firstName: member.firstName || null,
		lastName: member.lastName || null,
		dateOfBirth: member.dateOfBirth
			? member.dateOfBirth.toISOString().split("T")[0]
			: null,
		age: calculateAge(member.dateOfBirth),
		gender: member.gender || null,
		mobilePhone: member.mobilePhone || null,
		hobby: member.hobby || null,
		createdAt: member.createdAt,
		updatedAt: member.updatedAt,
		relationship: member.householdMembers[0]?.relationship || null,
		householdId: member.householdMembers[0]?.householdId || null,
		suburb: member.householdMembers[0]?.household?.suburb || null,
		census_year: member.householdMembers[0]?.censusYear?.year || null,
		censusYearId: member.householdMembers[0]?.censusYearId || null,
		sacramentCount: member.sacraments?.length || 0,
		sacramentsReceived:
			member.sacraments?.map((s) => s.sacramentType.name).join(", ") || "",
	}));

	return { members: transformedMembers, total };
}

/**
 * Get full member details including household and sacrament information - Prisma version
 */
export async function getMemberWithFullDetails(
	memberId: number,
): Promise<unknown | null> {
	const member = await prisma.member.findUnique({
		where: { id: memberId },
		include: {
			householdMembers: {
				include: {
					household: {
						include: {
							// uniqueCodes relation not available in current schema
						},
					},
					censusYear: true,
				},
			},
			sacraments: {
				include: {
					sacramentType: true,
				},
				orderBy: {
					sacramentType: {
						id: "asc",
					},
				},
			},
		},
	});

	if (!member) return null;

	const householdMember = member.householdMembers[0];
	const household = householdMember?.household;

	// Get household head information
	const householdHead = household
		? await prisma.member.findFirst({
				where: {
					householdMembers: {
						some: {
							householdId: household.id,
							relationship: "head",
						},
					},
				},
			})
		: null;

	return {
		memberId: member.id,
		firstName: member.firstName || null,
		lastName: member.lastName || null,
		dateOfBirth: member.dateOfBirth
			? member.dateOfBirth.toISOString().split("T")[0]
			: null,
		age: calculateAge(member.dateOfBirth),
		gender: member.gender || null,
		mobilePhone: member.mobilePhone || null,
		hobby: member.hobby || null,
		occupation: member.occupation || null,
		createdAt: member.createdAt,
		updatedAt: member.updatedAt,
		relationship: householdMember?.relationship || null,
		householdId: household?.id || null,
		suburb: household?.suburb || null,
		census_year: householdMember?.censusYear?.year || null,
		censusYearId: householdMember?.censusYearId || null,
		householdHeadName: householdHead
			? `${householdHead.firstName || ""} ${householdHead.lastName || ""}`.trim()
			: null,
		householdHeadContact: householdHead?.mobilePhone || null,
		uniqueCode: null, // uniqueCodes relation not available in current schema
		sacramentDetails: member.sacraments.map((s) => ({
			id: s.id,
			sacramentTypeId: s.sacramentTypeId,
			sacramentName: s.sacramentType.name,
			sacramentCode: s.sacramentType.code,
			date: s.date ? s.date.toISOString().split("T")[0] : null,
			place: s.place,
			notes: s.notes,
		})),
	};
}

/**
 * Update member information with validation - Prisma version
 */
export async function updateMemberWithValidation(
	memberId: number,
	data: {
		firstName: string;
		lastName: string;
		dateOfBirth: string | Date;
		gender: "male" | "female" | "other";
		mobilePhone: string;
		hobby?: string;
		occupation?: string;
		relationship: "head" | "spouse" | "child" | "parent" | "relative" | "other";
	},
): Promise<void> {
	// Convert date to proper format if it's a string
	const dateOfBirth =
		typeof data.dateOfBirth === "string"
			? new Date(data.dateOfBirth)
			: data.dateOfBirth;

	// Update member basic information
	await prisma.member.update({
		where: { id: memberId },
		data: {
			firstName: data.firstName,
			lastName: data.lastName,
			dateOfBirth,
			gender: data.gender,
			mobilePhone: data.mobilePhone,
			hobby: data.hobby || null,
			occupation: data.occupation || null,
		},
	});

	// Update relationship in household_members table
	await prisma.householdMember.updateMany({
		where: {
			memberId,
			isCurrent: true,
		},
		data: {
			relationship: data.relationship,
		},
	});
}

/**
 * Get age demographics for members - Professional Prisma Implementation
 */
export async function getMemberDemographics(censusYearId?: number): Promise<{
	totalMembers: number;
	ageGroups: Record<string, number>;
	genderDistribution: Record<string, number>;
}> {
	// Get all members with their date of birth
	const members = await prisma.member.findMany({
		where: censusYearId
			? {
					householdMembers: {
						some: {
							censusYearId,
						},
					},
				}
			: undefined,
		select: {
			id: true,
			dateOfBirth: true,
			gender: true,
		},
	});

	// Get census year for age calculation
	let censusYear: number | undefined;
	if (censusYearId) {
		const yearRecord = await prisma.censusYear.findUnique({
			where: { id: censusYearId },
			select: { year: true },
		});
		censusYear = yearRecord?.year;
	}

	// Calculate age groups
	const ageGroups = {
		"Under 18": 0,
		"18-30": 0,
		"31-50": 0,
		"51-70": 0,
		"Over 70": 0,
		Unknown: 0,
	};

	// Calculate gender distribution
	const genderDistribution = {
		male: 0,
		female: 0,
		other: 0,
	};

	// Process each member
	members.forEach((member) => {
		// Age group calculation
		const ageGroup = getAgeGroup(member.dateOfBirth, censusYear);
		ageGroups[ageGroup as keyof typeof ageGroups]++;

		// Gender calculation
		if (member.gender in genderDistribution) {
			genderDistribution[member.gender as keyof typeof genderDistribution]++;
		}
	});

	return {
		totalMembers: members.length,
		ageGroups,
		genderDistribution,
	};
}

/**
 * Get member deletion info for validation and UX - Prisma version
 */
export async function getMemberDeletionInfo(memberId: number): Promise<{
	isHouseholdHead: boolean;
	householdId: number | null;
	memberCount: number;
	canDelete: boolean;
	deleteType: "regular" | "head_with_others" | "head_last_member";
	warningMessage?: string;
}> {
	// Get locale for translations
	const locale = await getLocaleFromCookies();
	const t = await getTranslations({ locale, namespace: "errors" });
	// Check if this is a household head
	const householdMember = await prisma.householdMember.findFirst({
		where: {
			memberId,
			relationship: "head",
			isCurrent: true,
		},
		include: {
			household: {
				include: {
					householdMembers: {
						where: { isCurrent: true },
					},
				},
			},
		},
	});

	const isHouseholdHead = !!householdMember;
	const householdId = householdMember?.householdId || null;
	const memberCount = householdMember?.household?.householdMembers?.length || 0;

	if (!isHouseholdHead) {
		return {
			isHouseholdHead: false,
			householdId: null,
			memberCount: 0,
			canDelete: true,
			deleteType: "regular",
		};
	}

	if (memberCount > 1) {
		return {
			isHouseholdHead: true,
			householdId,
			memberCount,
			canDelete: false,
			deleteType: "head_with_others",
			// Note: warningMessage removed - client-side components handle translation
		};
	}

	return {
		isHouseholdHead: true,
		householdId,
		memberCount,
		canDelete: true,
		deleteType: "head_last_member",
		warningMessage: t("deleteEntireHouseholdAndUnassignCode" as any),
	};
}

/**
 * Delete member with proper validation and cleanup - Prisma version
 */
export async function deleteMemberWithChecks(memberId: number): Promise<void> {
	// Get locale for translations
	const locale = await getLocaleFromCookies();
	const t = await getTranslations({ locale, namespace: "errors" });
	// First check if the member exists
	const existingMember = await prisma.member.findUnique({
		where: { id: memberId },
	});

	// If member doesn't exist, consider it already deleted (success case)
	if (!existingMember) {
		console.log(`Member ${memberId} already deleted or doesn't exist`);
		return;
	}

	// Check if this is a household head
	const householdMember = await prisma.householdMember.findFirst({
		where: {
			memberId,
			relationship: "head",
			isCurrent: true,
		},
		include: {
			household: {
				include: {
					householdMembers: {
						where: { isCurrent: true },
					},
				},
			},
		},
	});

	const isHouseholdHead = !!householdMember;
	const householdId = householdMember?.householdId;
	const memberCount = householdMember?.household?.householdMembers?.length || 0;

	// Prevent deletion of household head when other members exist
	if (isHouseholdHead && memberCount > 1) {
		throw new HouseholdHeadDeletionError(
			t("cannotDeleteHouseholdHeadWhenO" as any),
		);
	}

	// Use Prisma transaction for atomic operations
	await prisma.$transaction(async (tx) => {
		// Double-check member still exists within transaction
		const memberInTransaction = await tx.member.findUnique({
			where: { id: memberId },
		});

		if (!memberInTransaction) {
			console.log(
				`Member ${memberId} was deleted by another process during transaction`,
			);
			return;
		}

		// Delete sacrament records first (due to foreign key constraints)
		await tx.sacrament.deleteMany({
			where: { memberId },
		});

		// Delete household member relationships
		await tx.householdMember.deleteMany({
			where: { memberId },
		});

		// Delete the member record
		try {
			await tx.member.delete({
				where: { id: memberId },
			});
		} catch (error: unknown) {
			// Handle P2025 error (record not found) gracefully
			if (
				error &&
				typeof error === "object" &&
				"code" in error &&
				error.code === "P2025"
			) {
				console.log(
					`Member ${memberId} was already deleted during transaction`,
				);
				return;
			}
			throw error;
		}

		// If this was the last member (household head) of a household, clean up the household
		if (isHouseholdHead && memberCount === 1 && householdId) {
			// Update any associated unique codes to unassigned
			await tx.uniqueCode.updateMany({
				where: { householdId },
				data: {
					isAssigned: false,
					assignedAt: null,
					householdId: null,
					updatedAt: new Date(),
				},
			});

			// Delete the household record (with error handling)
			try {
				await tx.household.delete({
					where: { id: householdId },
				});
			} catch (error: unknown) {
				// Handle P2025 error for household deletion
				if (
					error &&
					typeof error === "object" &&
					"code" in error &&
					error.code === "P2025"
				) {
					console.log(`Household ${householdId} was already deleted`);
				} else {
					throw error;
				}
			}
		}
	});
}
