// Member type definition
export interface IMember {
	id: number;
	firstName: string;
	lastName: string;
	dateOfBirth: Date;
	gender: "male" | "female" | "other";
	mobilePhone: string;
	hobby?: string | null;
	occupation?: string | null;
	createdAt: Date;
	updatedAt: Date;
}

// Household type definition
export interface IHousehold {
	id: number;
	suburb: string;
	firstCensusYearId: number;
	lastCensusYearId: number;
	createdAt: Date;
	updatedAt: Date;
}

// Household Member relationship type definition
export interface IHouseholdMember {
	id: number;
	householdId: number;
	memberId: number;
	relationship: "head" | "spouse" | "child" | "parent" | "relative" | "other";
	censusYearId: number;
	isCurrent: boolean;
	createdAt: Date;
	updatedAt: Date;
}

// Sacrament type definition
export interface ISacrament {
	id: number;
	memberId: number;
	sacramentTypeId: number;
	date: Date | null;
	place?: string | null;
	notes?: string | null;
	censusYearId: number;
	createdAt: Date;
	updatedAt: Date;
}

// Sacrament Type definition
export interface ISacramentType {
	id: number;
	code: string;
	name: string;
	description?: string;
}

// Census Year definition
export interface ICensusYear {
	id: number;
	year: number;
	isActive: boolean;
	startDate: Date;
	endDate: Date;
	createdAt: Date;
	updatedAt: Date;
}

// Unique Code definition
export interface IUniqueCode {
	id: number;
	code: string;
	isAssigned: boolean;
	assignedAt?: Date;
	householdId?: number;
	censusYearId: number;
	createdAt: Date;
	updatedAt: Date;
}

// Admin User definition
export interface IAdmin {
	id: number;
	username: string;
	password: string;
	email?: string;
	fullName?: string;
	twoFactorSecret?: string;
	twoFactorEnabled: boolean;
	twoFactorBackupCodes?: string;
	lastLogin?: Date;
	createdAt: Date;
	updatedAt: Date;
}

// System Settings definition
export interface ISystemSetting {
	id: number;
	settingKey: string;
	settingValue: string;
	description?: string;
	updatedAt: Date;
}

// Member with household and sacrament details for admin portal
export interface IMemberWithDetails {
	memberId: number;
	firstName: string | null;
	lastName: string | null;
	dateOfBirth: string | null;
	age: number | string;
	gender: "male" | "female" | "other" | null;
	mobilePhone: string | null;
	hobby?: string | null;
	relationship:
		| "head"
		| "spouse"
		| "child"
		| "parent"
		| "relative"
		| "other"
		| null;
	householdId: number | null;
	suburb: string | null;
	census_year: number | null;
	censusYearId: number | null;
	sacramentCount: number | null;
	sacramentsReceived: string[];
	createdAt: Date;
	updatedAt: Date;
}

// Full member details for view dialogue
export interface IMemberFullDetails extends IMemberWithDetails {
	householdHeadName?: string | null;
	householdHeadContact?: string | null;
	uniqueCode?: string | null;
	sacramentDetails: Array<{
		id: number;
		sacramentTypeId: number;
		sacramentName: string;
		sacramentCode: string;
		date: string | null;
		place: string | null;
		notes: string | null;
	}>;
}

// Suburb definition for autocomplete
export interface ISuburb {
	id: number;
	salCode: string;
	suburbName: string;
	stateCode: string;
	stateName: string;
	displayName: string;
	searchText: string;
	createdAt: Date;
	updatedAt: Date;
}

// Form Field definition for dynamic forms
export interface IFormField {
	name: string;
	label: string;
	type: "text" | "number" | "date" | "select" | "checkbox" | "radio";
	required?: boolean;
	placeholder?: string;
	options?: { label: string; value: string }[];
	validation?: {
		min?: number;
		max?: number;
		pattern?: string;
		message?: string;
	};
}
