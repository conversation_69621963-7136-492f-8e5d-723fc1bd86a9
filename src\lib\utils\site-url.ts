/**
 * Utility for getting the site URL for census participants
 */

// Cache the site URL to avoid repeated API calls
let cachedSiteUrl: string | null = null;
let cacheExpiry = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

/**
 * Get the site URL for census participants
 * Uses a cached value if available, otherwise fetches from the API
 * Falls back to window.location.origin if API call fails
 */
export async function getSiteUrl(): Promise<string> {
  // Check if we have a valid cached value
  const now = Date.now();
  if (cachedSiteUrl && cacheExpiry > now) {
    return cachedSiteUrl;
  }

  try {
    // Fetch the site URL from the API
    const response = await fetch("/api/settings/site-url");

    if (!response.ok) {
      throw new Error("Failed to fetch site URL");
    }

    const data = await response.json();

    // Cache the result
    cachedSiteUrl = data.siteUrl;
    cacheExpiry = now + CACHE_DURATION;

    return data.siteUrl;
  } catch (error) {
    console.error("Error fetching site URL:", error);

    // Fall back to window.location.origin
    if (typeof window !== "undefined") {
      const fallbackUrl =
        window.location.hostname === "localhost" ? "http://localhost:3000" : window.location.origin;

      return fallbackUrl;
    }

    // Default fallback if window is not available (SSR)
    return "http://localhost:3000";
  }
}

/**
 * Clear the site URL cache
 * Call this after updating the site URL to ensure the latest value is used
 */
export function clearSiteUrlCache(): void {
  cachedSiteUrl = null;
  cacheExpiry = 0;
}
