"use client";

import { Trash2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useCallback, useEffect, useState } from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { StatusBadge } from "@/components/ui/status-badge";
import { useIsMobile } from "@/hooks/use-mobile";
import { useMessage } from "@/hooks/useMessage";
import { formatDate } from "@/lib/utils/date-time";
import { getRelationshipBadgeVariant } from "@/lib/utils/relationship-badge";
import { DeleteHouseholdDialog } from "./delete-household-dialog";
import { MemberSacramentsDisplay } from "./member-sacraments-display";

interface ViewHouseholdDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  householdId: number;
  onHouseholdDeleted: () => void;
}

interface HouseholdDetails {
  household: {
    id: number;
    suburb: string;
    firstCensusYearId: number;
    lastCensusYearId: number;
    createdAt: string;
    updatedAt: string;
  };
  members: Array<{
    id: number;
    householdId: number;
    memberId: number;
    relationship: string;
    censusYearId: number;
    isCurrent: boolean; // Note: Not used for UI display - kept for API compatibility
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    gender: string;
    mobilePhone: string;
    hobby?: string | null;
  }>;
  censusYears: Array<{
    id: number;
    year: number;
    isActive: boolean;
  }>;
  formStatus: Array<{
    id: number;
    householdId: number;
    censusYearId: number;
    status: "not_started" | "in_progress" | "completed";
    completionDate: string | null;
  }>;
  uniqueCode?: string | null;
}

export function ViewHouseholdDialog({
  open,
  onOpenChange,
  householdId,
  onHouseholdDeleted,
}: ViewHouseholdDialogProps) {
  const { showError } = useMessage();
  const isMobile = useIsMobile();
  const t = useTranslations("admin");
  const tCommon = useTranslations("common");
  const tForms = useTranslations("forms");
  const [loading, setLoading] = useState(true);
  const [householdDetails, setHouseholdDetails] = useState<HouseholdDetails | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Define fetchHouseholdDetails first
  const fetchHouseholdDetails = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/households/${householdId}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || tCommon("failedToFetchHouseholdDetails"));
      }

      const data = await response.json();
      setHouseholdDetails(data);
    } catch (error) {
      console.error("Error fetching household details:", error);
      showError("failedToFetchHouseholdDetails");
      onOpenChange(false);
    } finally {
      setLoading(false);
    }
  }, [householdId, showError, onOpenChange, tCommon]);

  // Fetch household details when dialog opens
  useEffect(() => {
    if (open && householdId) {
      fetchHouseholdDetails();
    }
  }, [open, householdId, fetchHouseholdDetails]);

  // Handle household deletion
  const handleDeleteHousehold = () => {
    setIsDeleteDialogOpen(true);
  };

  const handleHouseholdDeleted = () => {
    setIsDeleteDialogOpen(false);
    onOpenChange(false);
    onHouseholdDeleted();
  };

  // Shared content component for both mobile and desktop
  const DialogContentComponent = () => {
    if (loading) {
      return (
        <div className="space-y-6 py-4">
          {/* Skeleton for household information */}
          <div className="space-y-2">
            <Skeleton className="h-5 w-40" /> {/* Household Information title */}
            <div className="grid grid-cols-4 gap-4">
              <div className="rounded-md bg-muted/20 p-4">
                <Skeleton className="mb-1 h-3 w-16" /> {/* Suburb label */}
                <Skeleton className="mt-1 h-4 w-24" /> {/* Suburb value */}
              </div>
              <div className="rounded-md bg-muted/20 p-4">
                <Skeleton className="mb-1 h-3 w-24" /> {/* Census Year label */}
                <Skeleton className="mt-1 h-4 w-16" /> {/* Census Year value */}
              </div>
              <div className="rounded-md bg-muted/20 p-4">
                <Skeleton className="mb-1 h-3 w-20" /> {/* Created At label */}
                <Skeleton className="mt-1 h-4 w-28" /> {/* Created At value */}
              </div>
              <div className="rounded-md bg-muted/20 p-4">
                <Skeleton className="mb-1 h-3 w-24" /> {/* Last Updated label */}
                <Skeleton className="mt-1 h-4 w-28" /> {/* Last Updated value */}
              </div>
            </div>
          </div>
          <Skeleton className="h-px w-full" /> {/* Separator skeleton */}
          {/* Skeleton for household members */}
          <div className="space-y-2">
            <Skeleton className="h-5 w-48" /> {/* Household Members title */}
            <Skeleton className="h-40 w-full" /> {/* Members list */}
          </div>
        </div>
      );
    }

    if (householdDetails) {
      return (
        <div className="space-y-6">
          {/* Basic household information */}
          <div>
            <h3 className="mb-2 font-medium text-sm">{tCommon("householdInformation")}</h3>
            <div className="grid grid-cols-4 gap-4">
              <div className="rounded-md bg-muted/20 p-4">
                <h4 className="font-medium text-muted-foreground text-xs">{tCommon("suburb")}</h4>
                <p className="mt-1 font-medium text-sm">{householdDetails.household.suburb}</p>
              </div>
              <div className="rounded-md bg-muted/20 p-4">
                <h4 className="font-medium text-muted-foreground text-xs">
                  {tCommon("censusYear")}
                </h4>
                <p className="mt-1 font-medium text-sm">
                  {householdDetails.censusYears.find(
                    (y) => y.id === householdDetails.household.firstCensusYearId,
                  )?.year || "Unknown"}
                </p>
              </div>
              <div className="rounded-md bg-muted/20 p-4">
                <h4 className="font-medium text-muted-foreground text-xs">
                  {tCommon("createdAt")}
                </h4>
                <p className="mt-1 font-medium text-sm">
                  {formatDate(new Date(householdDetails.household.createdAt))}
                </p>
              </div>
              <div className="rounded-md bg-muted/20 p-4">
                <h4 className="font-medium text-muted-foreground text-xs">
                  {tCommon("lastUpdated")}
                </h4>
                <p className="mt-1 font-medium text-sm">
                  {formatDate(new Date(householdDetails.household.updatedAt))}
                </p>
              </div>
            </div>
          </div>

          <Separator />

          {/* Household members */}
          <div>
            <h3 className="mb-2 font-medium text-sm">
              {t("householdMembers")} ({householdDetails.members.length})
            </h3>
            {householdDetails.members.length === 0 ? (
              <div className="rounded-md bg-muted/20 p-4 text-center">
                <p className="text-muted-foreground text-sm">
                  {tCommon("noMembersInThisHousehold")}
                </p>
              </div>
            ) : (
              <div className="space-y-2">
                {householdDetails.members.map((member) => (
                  <div className="overflow-hidden rounded-md border border-border" key={member.id}>
                    <Accordion className="w-full" collapsible type="single">
                      <AccordionItem className="border-0" value={`member-${member.id}`}>
                        <AccordionTrigger className="cursor-pointer bg-muted/10 px-4 py-3 hover:bg-muted/20 hover:no-underline">
                          <div className="flex w-full items-center justify-between pr-4">
                            <div className="flex items-center">
                              <span className="font-medium">
                                {member.firstName} {member.lastName}
                              </span>
                              <StatusBadge
                                className="ml-2 capitalize"
                                variant={getRelationshipBadgeVariant(member.relationship)}
                              >
                                {member.relationship.charAt(0).toUpperCase() +
                                  member.relationship.slice(1)}
                              </StatusBadge>
                            </div>
                          </div>
                        </AccordionTrigger>
                        <AccordionContent className="border-border border-t">
                          <div className="grid grid-cols-4 gap-4 bg-muted/5 p-4">
                            <div className="rounded-md bg-muted/10 p-3">
                              <h4 className="font-medium text-muted-foreground text-xs">
                                {tCommon("dateOfBirth")}
                              </h4>
                              <p className="mt-1 font-medium text-sm">
                                {formatDate(new Date(member.dateOfBirth))}
                              </p>
                            </div>
                            <div className="rounded-md bg-muted/10 p-3">
                              <h4 className="font-medium text-muted-foreground text-xs">
                                {tCommon("gender")}
                              </h4>
                              <p className="mt-1 font-medium text-sm capitalize">{member.gender}</p>
                            </div>
                            <div className="rounded-md bg-muted/10 p-3">
                              <h4 className="font-medium text-muted-foreground text-xs">
                                {tCommon("mobilePhone")}
                              </h4>
                              <p className="mt-1 font-medium text-sm">{member.mobilePhone}</p>
                            </div>
                            <div className="rounded-md bg-muted/10 p-3">
                              <h4 className="font-medium text-muted-foreground text-xs">
                                {tCommon("censusYear")}
                              </h4>
                              <p className="mt-1 font-medium text-sm">
                                {householdDetails.censusYears.find(
                                  (y) => y.id === member.censusYearId,
                                )?.year || "Unknown"}
                              </p>
                            </div>
                            {member.hobby && (
                              <div className="col-span-4 mt-2 rounded-md bg-muted/10 p-3">
                                <h4 className="font-medium text-muted-foreground text-xs">
                                  {tForms("hobby")}
                                </h4>
                                <p className="mt-1 font-medium text-sm">{member.hobby}</p>
                              </div>
                            )}
                          </div>

                          {/* Sacrament Details Section */}
                          <div className="mt-4 rounded-md bg-muted/5 p-4">
                            <MemberSacramentsDisplay
                              memberId={member.memberId}
                              memberName={`${member.firstName} ${member.lastName}`}
                            />
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      );
    }

    return (
      <div className="py-4 text-center text-muted-foreground">
        {tCommon("failedToLoadHouseholdDetails")}
      </div>
    );
  };

  // Action buttons component for both mobile and desktop
  const ActionButtons = () => (
    <Button
      className="cursor-pointer"
      disabled={loading}
      onClick={handleDeleteHousehold}
      variant="destructive"
    >
      <Trash2 className="mr-2 h-4 w-4" />
      {tCommon("deleteHousehold")}
    </Button>
  );

  // Mobile implementation using Drawer (native swipe-to-dismiss from bottom)
  // Features: Visual handle bar, smooth swipe gestures, and hidden scrollbar
  if (isMobile) {
    return (
      <>
        <Drawer onOpenChange={onOpenChange} open={open}>
          <DrawerContent className="flex max-h-[80vh] min-h-[60vh] flex-col">
            <DrawerHeader className="pb-4 text-left">
              <DrawerTitle className="font-semibold text-lg">
                {tCommon("householdDetails")}
              </DrawerTitle>
              <DrawerDescription className="text-muted-foreground">
                {householdDetails ? (
                  <>
                    {householdDetails.uniqueCode ? (
                      <>
                        {tCommon("uniqueCode")}:{" "}
                        <span className="font-medium">{householdDetails.uniqueCode}</span> •{" "}
                      </>
                    ) : null}
                    ID: <span className="font-medium">{householdDetails.household.id}</span>
                  </>
                ) : (
                  tCommon("loadingHouseholdInformation")
                )}
              </DrawerDescription>
            </DrawerHeader>

            <div className="scrollbar-hide flex-1 space-y-6 overflow-y-auto px-4">
              <DialogContentComponent />
            </div>

            <div className="border-t px-4 pt-4 pb-4">
              <div className="flex justify-end">
                <ActionButtons />
              </div>
            </div>
          </DrawerContent>
        </Drawer>

        {/* Enhanced Delete Dialog */}
        {householdDetails && (
          <DeleteHouseholdDialog
            householdId={householdId}
            householdName={(() => {
              const householdHead = householdDetails.members.find(
                (member) => member.relationship === "head",
              );
              return householdHead
                ? `${householdHead.firstName} ${householdHead.lastName}'s Household`
                : "Household";
            })()}
            onHouseholdDeleted={handleHouseholdDeleted}
            onOpenChange={setIsDeleteDialogOpen}
            open={isDeleteDialogOpen}
          />
        )}
      </>
    );
  }

  // Desktop implementation using Dialog (unchanged)
  return (
    <>
      <Dialog onOpenChange={onOpenChange} open={open}>
        <DialogContent className="max-h-[80vh] overflow-y-auto sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle>{tCommon("householdDetails")}</DialogTitle>
            <DialogDescription>
              {householdDetails ? (
                <>
                  {householdDetails.uniqueCode ? (
                    <>
                      {tCommon("uniqueCode")}:{" "}
                      <span className="font-medium">{householdDetails.uniqueCode}</span> •{" "}
                    </>
                  ) : null}
                  ID: <span className="font-medium">{householdDetails.household.id}</span>
                </>
              ) : (
                tCommon("loadingHouseholdInformation")
              )}
            </DialogDescription>
          </DialogHeader>

          <DialogContentComponent />

          <DialogFooter className="gap-2 sm:gap-0">
            <ActionButtons />
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Enhanced Delete Dialog */}
      {householdDetails && (
        <DeleteHouseholdDialog
          householdId={householdId}
          householdName={(() => {
            const householdHead = householdDetails.members.find(
              (member) => member.relationship === "head",
            );
            return householdHead
              ? `${householdHead.firstName} ${householdHead.lastName}'s Household`
              : "Household";
          })()}
          onHouseholdDeleted={handleHouseholdDeleted}
          onOpenChange={setIsDeleteDialogOpen}
          open={isDeleteDialogOpen}
        />
      )}
    </>
  );
}
