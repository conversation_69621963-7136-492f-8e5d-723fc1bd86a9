"use client";

import React, {
	createContext,
	type ReactNode,
	useContext,
	useState,
} from "react";
import type { ActiveTour, TourContextValue } from "@/types/tour";

const TourContext = createContext<TourContextValue | undefined>(undefined);

/**
 * Minimal Tour Provider
 */
export function TourProvider({ children }: { children: ReactNode }) {
	const [activeTour, setActiveTour] = useState<ActiveTour | null>(null);
	const [isVisible, setIsVisible] = useState(false);

	return (
		<TourContext.Provider
			value={{ activeTour, setActiveTour, isVisible, setIsVisible }}
		>
			{children}
		</TourContext.Provider>
	);
}

export function useTourContext(): TourContextValue {
	const context = useContext(TourContext);
	if (!context)
		throw new Error("useTourContext must be used within a TourProvider");
	return context;
}
