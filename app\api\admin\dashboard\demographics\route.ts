import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { authOptions } from "@/lib/auth/auth-options";
import { prisma } from "@/lib/db/prisma";
import { getAgeGroup } from "@/lib/utils/date-calculations";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";

export async function GET(_request: Request) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require admin authentication
    if (!session || session.user.role !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get current census year
    const currentCensusYear = await prisma.censusYear.findFirst({
      where: { isActive: true },
    });

    const currentYearId = currentCensusYear?.id;

    // Get age distribution with gender breakdown using Prisma
    const members = await prisma.member.findMany({
      where: {
        householdMembers: {
          some: {
            censusYearId: currentYearId,
            isCurrent: true,
          },
        },
      },
      include: {
        householdMembers: {
          where: {
            censusYearId: currentYearId,
            isCurrent: true,
          },
        },
      },
    });

    // Process members to get age distribution
    const ageGroupMap = new Map<
      string,
      {
        total_count: number;
        male_count: number;
        female_count: number;
        other_count: number;
        age_group: string;
      }
    >();

    // Define age groups
    const ageGroupNames = ["Under 18", "18-35", "36-55", "56-75", "75+"];
    ageGroupNames.forEach((group) => {
      ageGroupMap.set(group, {
        total_count: 0,
        male_count: 0,
        female_count: 0,
        other_count: 0,
        age_group: group,
      });
    });

    // Calculate age distribution
    members.forEach((member) => {
      if (member.dateOfBirth) {
        const ageGroup = getAgeGroup(member.dateOfBirth);
        const group = ageGroupMap.get(ageGroup);

        if (group) {
          group.total_count++;

          if (member.gender === "male") {
            group.male_count++;
          } else if (member.gender === "female") {
            group.female_count++;
          } else {
            group.other_count++;
          }
        }
      }
    });

    // Convert map to array and sort by age group
    const ageDistribution = Array.from(ageGroupMap.values()).sort((a, b) => {
      const order: Record<string, number> = {
        "Under 18": 1,
        "18-35": 2,
        "36-55": 3,
        "56-75": 4,
        "75+": 5,
      };
      return order[a.age_group] - order[b.age_group];
    });

    // Calculate gender distribution from the members we already fetched
    const genderDistribution = [
      {
        male: members.filter((m) => m.gender === "male").length,
        female: members.filter((m) => m.gender === "female").length,
        other: members.filter((m) => m.gender === "other").length,
      },
    ];

    // Get sacrament distribution using Prisma
    const sacraments = await prisma.sacrament.findMany({
      where: {
        member: {
          householdMembers: {
            some: {
              censusYearId: currentYearId,
              isCurrent: true,
            },
          },
        },
      },
      include: {
        sacramentType: true,
      },
    });

    // Calculate sacrament distribution
    const sacramentDistribution = [
      {
        baptism: sacraments.filter((s) => s.sacramentType.name === "Baptism").length,
        confirmation: sacraments.filter((s) => s.sacramentType.name === "Confirmation").length,
        communion: sacraments.filter((s) => s.sacramentType.name === "First Communion").length,
        marriage: sacraments.filter((s) => s.sacramentType.name === "Marriage").length,
      },
    ];

    // Calculate total member count from the members we already fetched
    const total = members.length;

    // Format age groups with gender breakdown and percentages
    const ageGroups = ageDistribution.map((group) => ({
      label: group.age_group,
      count: group.total_count,
      percentage: total > 0 ? (group.total_count / total) * 100 : 0,
      genderBreakdown: {
        male: group.male_count,
        female: group.female_count,
        other: group.other_count,
        malePercentage: group.total_count > 0 ? (group.male_count / group.total_count) * 100 : 0,
        femalePercentage:
          group.total_count > 0 ? (group.female_count / group.total_count) * 100 : 0,
        otherPercentage: group.total_count > 0 ? (group.other_count / group.total_count) * 100 : 0,
      },
    }));

    const demographics = {
      ageGroups,
      genderDistribution: {
        male: genderDistribution[0]?.male || 0,
        female: genderDistribution[0]?.female || 0,
        other: genderDistribution[0]?.other || 0,
      },
      sacramentDistribution: {
        baptism: sacramentDistribution[0]?.baptism || 0,
        confirmation: sacramentDistribution[0]?.confirmation || 0,
        communion: sacramentDistribution[0]?.communion || 0,
        marriage: sacramentDistribution[0]?.marriage || 0,
      },
      totalMembers: total,
    };

    return NextResponse.json(demographics);
  } catch (_error) {
    // Get locale for error translations
    const locale = await getLocaleFromCookies();
    const t = await getTranslations({ locale, namespace: "admin" });

    return NextResponse.json({ error: t("demographicsFetchFailed") }, { status: 500 });
  }
}
