/**
 * Simple API Validation Helpers
 *
 * Clean, reliable patterns for handling validation in API routes
 * with proper Zod v4 compatibility and simple i18n support.
 */

import { NextResponse } from "next/server";
import { getTranslations } from "next-intl/server";
import type { ZodType } from "zod/v4";
import { getZodErrorDetails, isZodError } from "@/lib/utils/error-handling";

/**
 * Validate request data with proper error handling
 * Returns either the validated data or an error response
 */
export async function validateRequestData<T>(
  schema: ZodType<T>,
  data: unknown,
  locale: "en" | "zh-CN",
): Promise<{ success: true; data: T } | { success: false; response: NextResponse }> {
  try {
    const validatedData = schema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (isZodError(error)) {
      const t = await getTranslations({ locale, namespace: "admin" });
      return {
        success: false,
        response: NextResponse.json(
          {
            error: t("validationError"),
            details: getZodErrorDetails(error),
          },
          { status: 400 },
        ),
      };
    }

    // Re-throw non-validation errors
    throw error;
  }
}

/**
 * Safe parse with proper error formatting
 * Returns standardised validation result
 */
export function safeParseWithErrorHandling<T>(
  schema: ZodType<T>,
  data: unknown,
): { success: true; data: T } | { success: false; error: any } {
  const result = schema.safeParse(data);

  if (result.success) {
    return { success: true, data: result.data };
  }

  return {
    success: false,
    error: getZodErrorDetails(result.error),
  };
}

// Note: getLocaleFromRequest has been removed as it's no longer used after the locale detection migration.
// All locale detection is now handled by getLocaleFromCookies() in server-messages.ts for consistency.
