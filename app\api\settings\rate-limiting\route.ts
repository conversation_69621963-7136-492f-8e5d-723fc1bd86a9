import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { authOptions } from "@/lib/auth/auth-options";
import {
	getRateLimitSettings,
	updateRateLimitSettings,
} from "@/lib/db/settings";
import {
	getErrorMessage,
	getZodErrorDetails,
	isZodError,
} from "@/lib/utils/error-handling";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";
import { createRateLimitSchema } from "@/lib/validation/settings";

export async function GET(_request: Request) {
	// Get locale from centralized utility at the beginning
	const locale = await getLocaleFromCookies();
	const tAdmin = await getTranslations({ locale, namespace: "admin" });
	const tErrors = await getTranslations({ locale, namespace: "errors" });

	try {
		// Check authentication
		const session = await getServerSession(authOptions);

		// Require authentication for accessing settings
		if (!session || session.user.role !== "admin") {
			return NextResponse.json(
				{ error: tAdmin("unauthorized") },
				{ status: 401 },
			);
		}

		try {
			// Try to get rate limit settings from database
			const rateLimitSettings = await getRateLimitSettings();
			return NextResponse.json(rateLimitSettings);
		} catch (_dbError) {
			// Return default values for development
			return NextResponse.json({
				maxAttempts: 5,
				lockoutMinutes: 15,
				escalationMinutes: 15,
			});
		}
	} catch (error) {
		return NextResponse.json(
			{
				error: tErrors("failedToFetchRateLimitSettings"),
				details: getErrorMessage(error),
			},
			{ status: 500 },
		);
	}
}

export async function PUT(request: Request) {
	// Get locale from centralized utility at the beginning
	const locale = await getLocaleFromCookies();
	const tAdmin = await getTranslations({ locale, namespace: "admin" });
	const tErrors = await getTranslations({ locale, namespace: "errors" });
	const tNotifications = await getTranslations({
		locale,
		namespace: "notifications",
	});

	try {
		// Check authentication
		const session = await getServerSession(authOptions);

		// Require authentication for updating settings
		if (!session || session.user.role !== "admin") {
			return NextResponse.json(
				{ error: tAdmin("unauthorized") },
				{ status: 401 },
			);
		}

		// Parse and validate request body
		let data;
		try {
			data = await request.json();
		} catch {
			return NextResponse.json(
				{
					error: tErrors("invalidJsonInRequestBody"),
					details: "Request body must be valid JSON",
				},
				{ status: 400 },
			);
		}

		const rateLimitSchema = await createRateLimitSchema(locale);
		const validatedData = rateLimitSchema.parse(data);

		try {
			// Update rate limit settings in database
			await updateRateLimitSettings({
				maxAttempts: validatedData.maxAttempts,
				lockoutMinutes: validatedData.lockoutMinutes,
				escalationMinutes: validatedData.escalationMinutes,
			});

			return NextResponse.json({
				success: true,
				message: tNotifications("settingsUpdatedSuccessfully"),
			});
		} catch (_dbError) {
			if (process.env.NODE_ENV === "development") {
			}

			// For development, return a generic message without exposing values

			if (process.env.NODE_ENV === "development") {
				return NextResponse.json({
					success: true,
					message: tErrors("rateLimitSettingsWouldBeSavedInProduction"),
				});
			}

			return NextResponse.json(
				{
					error: tErrors("failedToUpdateRateLimitSettings"),
					details: tErrors("databaseConnectionError"),
				},
				{ status: 500 },
			);
		}
	} catch (error) {
		// Handle validation errors (reuse centralized locale variables)
		if (isZodError(error)) {
			const zodDetails = getZodErrorDetails(error);
			return NextResponse.json(
				{
					error: tErrors("validationFailed"),
					details: zodDetails,
				},
				{ status: 400 },
			);
		}

		return NextResponse.json(
			{
				error: tErrors("failedToUpdateRateLimitSettings"),
				details: getErrorMessage(error),
			},
			{ status: 500 },
		);
	}
}
