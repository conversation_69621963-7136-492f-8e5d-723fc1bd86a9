/**
 * QR Code Cache Configuration
 *
 * This file contains configuration settings for QR code caching,
 * including TTL values and cache key prefixes.
 */

// Cache key prefixes
export const QR_CACHE_PREFIX = "qr:";
export const UNIQUE_CODE_QR_CACHE_PREFIX = `${QR_CACHE_PREFIX}unique:`;
export const TWO_FA_QR_CACHE_PREFIX = `${QR_CACHE_PREFIX}2fa:`;

// TTL values in milliseconds
export const UNIQUE_CODE_QR_CACHE_TTL = 24 * 60 * 60 * 1000; // 24 hours
export const TWO_FA_QR_CACHE_TTL = 10 * 60 * 1000; // 10 minutes
