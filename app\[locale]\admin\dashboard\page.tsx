import type { Metada<PERSON> } from "next";
import { getTranslations } from "next-intl/server";
import { requireAdmin } from "@/lib/auth/auth-utils";
import { DashboardClient } from "./dashboard-client";

export async function generateMetadata(): Promise<Metadata> {
	const t = await getTranslations("metadata");

	return {
		title: t("adminDashboardTitle"),
		description: t("adminDashboardDescription"),
	};
}

export default async function AdminDashboardPage() {
	// Server-side authentication check
	const session = await requireAdmin();

	return <DashboardClient userName={session.user.name} />;
}
