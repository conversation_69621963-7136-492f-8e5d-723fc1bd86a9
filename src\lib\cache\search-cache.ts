import { LRUCache } from "lru-cache";

// Define the search result types
export interface PaginationState {
	page: number;
	pageSize: number;
	total: number;
	totalPages: number;
}

export interface SearchResult<T> {
	data: T[];
	pagination: PaginationState;
}

// Define the cache options with proper types
const options = {
	// Maximum number of items to store in the cache
	max: 100,

	// How long to live in milliseconds (1 minute for real-time updates)
	// Reduced from 10 minutes to maintain system independence while providing reasonable update frequency
	ttl: 1000 * 60 * 1,

	// Function to calculate the size of each item for maxSize enforcement
	sizeCalculation: (value: unknown, key: string) => {
		// Rough estimation of size based on JSON stringification
		return JSON.stringify(value).length + key.length;
	},

	// Maximum size of cache in bytes (5MB)
	maxSize: 5 * 1024 * 1024,

	// When true, entries will be removed after ttl milliseconds
	// When false, ttl setting is ignored and entries never expire
	ttlAutopurge: true,
};

// Create a cache instance for search results
const searchCache = new LRUCache<string, any>(options);

/**
 * Type definition for search parameters
 */
export type SearchParams = Record<
	string,
	string | number | boolean | null | undefined
>;

/**
 * Generate a cache key from search parameters
 *
 * @param params Search parameters
 * @returns Cache key string
 */
export function generateSearchCacheKey(params: SearchParams): string {
	// Sort keys to ensure consistent cache keys regardless of parameter order
	const sortedKeys = Object.keys(params).sort();

	// Build key string from sorted parameters, filtering out undefined/null values
	return sortedKeys
		.filter((key) => params[key] != null)
		.map((key) => `${key}=${String(params[key])}`)
		.join("&");
}

/**
 * Get cached search results
 *
 * @param params Search parameters
 * @returns Cached results or undefined if not in cache
 */
export function getCachedSearchResults<T>(params: SearchParams): T | undefined {
	const cacheKey = generateSearchCacheKey(params);
	return searchCache.get(cacheKey) as T | undefined;
}

/**
 * Cache search results
 *
 * @param params Search parameters
 * @param results Search results to cache
 */
export function cacheSearchResults<T>(params: SearchParams, results: T): void {
	const cacheKey = generateSearchCacheKey(params);
	searchCache.set(cacheKey, results);
}

/**
 * Clear the entire search cache
 */
export function clearSearchCache(): void {
	searchCache.clear();
}

/**
 * Invalidate cache entries that match a specific pattern
 *
 * @param keyPattern String or RegExp to match against cache keys
 */
export function invalidateCacheEntries(keyPattern: string | RegExp): void {
	// Get all keys from the cache
	const keys = Array.from(searchCache.keys());

	// Filter keys that match the pattern
	const matchingKeys =
		typeof keyPattern === "string"
			? keys.filter((key) => key.includes(keyPattern))
			: keys.filter((key) => keyPattern.test(key));

	// Delete matching entries
	matchingKeys.forEach((key) => searchCache.delete(key));
}

/**
 * Get or set cache entry with a function that generates the value if not found
 *
 * @param params Search parameters
 * @param fetchFn Function to call if cache miss
 * @returns Search results (either from cache or freshly fetched)
 */
export async function getOrSetCachedResults<T>(
	params: SearchParams,
	fetchFn: () => Promise<T>,
): Promise<T> {
	const cacheKey = generateSearchCacheKey(params);

	// Check if we have a cached result
	const cachedResult = searchCache.get(cacheKey) as T | undefined;

	if (cachedResult) {
		return cachedResult;
	}

	// If not in cache, fetch fresh data
	const freshResult = await fetchFn();

	// Cache the result
	searchCache.set(cacheKey, freshResult);

	return freshResult;
}
