# WSCCC Census System API Guide

## Overview

The WSCCC Census System provides a comprehensive REST API built with Next.js API routes. The API is organised into distinct sections for admin operations, census participant operations, and system utilities.

## API Architecture

### Route Organisation
```
/api/
├── admin/                    # Admin-only endpoints
│   ├── analytics/           # AI chatbot and reporting
│   ├── households/          # Household management
│   ├── members/             # Member management
│   └── settings/            # System configuration
├── census/                  # Census participant endpoints
│   ├── auth/                # Census authentication
│   ├── forms/               # Form submissions
│   └── sacraments/          # Sacrament management
├── auth/                    # Admin authentication
├── database/                # Database utilities
└── unique-code/             # Code management
```

### Authentication Patterns

#### Admin Authentication
```typescript
// Admin route protection
export async function requireAdmin() {
  const session = await getServerSession(authOptions);
  
  if (!session || session.user.role !== 'admin') {
    redirect('/api/auth/toast-redirect?reason=unauthorized');
  }
  
  return session;
}
```

#### Census Authentication
```typescript
// Enhanced census route protection with account deletion detection
export async function requireCensusAuth() {
  const session = await getServerSession(censusAuthOptions);

  if (!session) {
    // Professional account deletion detection
    const cookieStore = await cookies();
    const sessionCookie = cookieStore.get('census-session-token');

    if (sessionCookie?.value && sessionCookie.value.length > 0) {
      // Session cookie exists but no session - likely account deletion
      redirect('/api/census/auth/toast-redirect?reason=account_deleted&redirectTo=/');
    }

    // Normal unauthenticated case
    redirect('/api/census/auth/toast-redirect?reason=unauthenticated&redirectTo=/');
  }
  
  return session;
}
```

## Admin API Endpoints

### Analytics API

#### POST /api/admin/analytics/chatbot
AI-powered analytics chatbot for natural language database queries.

**Authentication**: Admin required

**Request Body**:
```typescript
{
  message: string;
  conversationHistory: Array<{
    role: 'user' | 'assistant';
    content: string;
  }>;
}
```

**Response**:
```typescript
{
  success: boolean;
  data?: {
    response: string;
    queryExecuted?: boolean;
    results?: any[];
  };
  error?: string;
}
```

**Features**:
- Natural language to SQL translation
- Read-only database operations
- Context-aware conversations
- Comprehensive error handling

### Household Management

#### GET /api/admin/households
Retrieve households with pagination and filtering.

**Authentication**: Admin required

**Query Parameters**:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `search`: Search term for filtering
- `censusYear`: Filter by census year

**Response**:
```typescript
{
  households: Household[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
}
```

#### POST /api/admin/households
Create a new household.

**Authentication**: Admin required

**Request Body**:
```typescript
{
  suburb: string;
  head_first_name: string;
  head_last_name: string;
  head_mobile_phone: string;
  head_gender: 'male' | 'female' | 'other';
}
```

#### PUT /api/admin/households/[id]
Update household information.

#### DELETE /api/admin/households/[id]
Delete a household and all associated data.

### Member Management

#### GET /api/admin/members
Retrieve members with advanced filtering.

**Query Parameters**:
- `page`, `limit`: Pagination
- `search`: Name search
- `householdId`: Filter by household
- `gender`: Filter by gender
- `ageRange`: Filter by age range

#### POST /api/admin/members
Create a new member.

**Request Body**:
```typescript
{
  firstName: string;
  lastName: string;
  dateOfBirth?: Date;
  gender: 'male' | 'female' | 'other';
  mobilePhone: string;
  hobby?: string;
  household_id: number;
  relationship: HouseholdRelation;
}
```

### Settings Management

#### GET /api/admin/settings
Retrieve system settings.

#### PUT /api/admin/settings
Update system settings.

**Request Body**:
```typescript
{
  churchName?: string;
  email?: string;
  contactNumber?: string;
  address?: string;
  currentCensusYear?: number;
  censusOpen?: boolean;
}
```

## Census API Endpoints

### Authentication

#### POST /api/census/auth/signin
Authenticate with unique code.

**Request Body**:
```typescript
{
  code: string;
}
```

**Response**:
```typescript
{
  success: boolean;
  redirect?: string;
  error?: string;
}
```

**Rate Limiting**: Hybrid progressive lockout system
- 5 attempts → 1-minute lockout
- Escalating timeouts: 1min → 5min → 15min → 30min → 60min (max)

#### GET /api/census/auth/rate-limit-status
Check current rate limiting status.

**Response**:
```typescript
{
  success: boolean;
  data: {
    isLocked: boolean;
    remainingTime: number;
    attemptsRemaining: number;
    escalationLevel: number;
  };
}
```

### Form Submission

#### POST /api/census/forms
Submit census form data.

**Authentication**: Census session required

**Request Body**:
```typescript
{
  householdData: {
    suburb: string;
  };
  members: Array<{
    firstName: string;
    lastName: string;
    dateOfBirth?: Date;
    gender: 'male' | 'female' | 'other';
    mobilePhone: string;
    relationship: HouseholdRelation;
    hobby?: string;
  }>;
}
```

#### GET /api/census/forms/current
Retrieve current census form data.

### Sacrament Management

#### GET /api/census/sacraments
Retrieve sacraments for current household.

#### POST /api/census/sacraments
Add a new sacrament record.

**Request Body**:
```typescript
{
  memberId: number;
  sacramentType: SacramentType;
  date?: Date;
  location?: string;
}
```

#### PUT /api/census/sacraments/[id]
Update sacrament record.

#### DELETE /api/census/sacraments/[id]
Delete sacrament record.

### Account Management

#### DELETE /api/census/delete-account
Delete census participant account and all associated data.

**Authentication**: Census participant session required

**Request Body**:
```typescript
{
  confirmationPhrase: "DELETE NOW" // Required exact match
}
```

**Response**:
```typescript
{
  success: true,
  message: "Account deleted successfully"
}
```

**Side Effects**:
- Deletes household and all associated members
- Updates unique code to unassigned status
- Creates audit log entry
- Sets `census_toast` cookie for homepage display
- Clears census-related localStorage

**Security Features**:
- Requires exact confirmation phrase
- Comprehensive data cleanup
- Audit logging with IP tracking
- Immediate session invalidation

## Database API Endpoints

### Export System

#### POST /api/database/export
Export database in various formats.

**Authentication**: Admin required

**Request Body**:
```typescript
{
  format: 'sql' | 'csv' | 'json';
  tables: 'all' | 'members' | 'households' | 'settings';
}
```

**Response**: File download with appropriate MIME type

**Features**:
- CSV with proper escaping and injection protection
- JSON with structured relationships
- SQL with complete schema and data
- Compression for large exports

### Database Service

#### GET /api/database/service
Retrieve database service information.

**Response**:
```typescript
{
  connectionPool: {
    totalConnections: number;
    activeConnections: number;
    idleConnections: number;
  };
  serverInfo: {
    version: string;
    uptime: string;
  };
}
```

#### POST /api/database/service
Perform database maintenance operations.

**Request Body**:
```typescript
{
  action: 'clean-sessions' | 'clean-audit-logs';
  parameters?: Record<string, any>;
}
```

## Unique Code Management

### Code Generation

#### POST /api/unique-code
Generate unique codes for census access.

**Authentication**: Admin required

**Request Body**:
```typescript
{
  count: number;
  censusYearId: number;
}
```

**Response**:
```typescript
{
  success: boolean;
  data: {
    generated: number;
    codes: Array<{
      id: number;
      code: string;
    }>;
  };
}
```

**Code Format**: `cc-yyyy-xxxxxxxxxxxxxxx` (23 characters)
- `cc`: Prefix
- `yyyy`: Census year
- `xxxxxxxxxxxxxxx`: 15-character cryptographic random string

#### GET /api/unique-code
Retrieve unique codes with filtering.

**Query Parameters**:
- `page`, `limit`: Pagination
- `censusYear`: Filter by census year
- `assigned`: Filter by assignment status
- `search`: Search by code

#### DELETE /api/unique-code
Delete unique codes.

**Request Body**:
```typescript
{
  ids: number[];
}
```

## Error Handling

### Standardised Error Responses

All API endpoints return consistent error responses:

```typescript
{
  success: false;
  error: string;
  details?: any;
  code?: string;
}
```

### HTTP Status Codes
- **200**: Success
- **400**: Bad Request (validation errors)
- **401**: Unauthorized (authentication required)
- **403**: Forbidden (insufficient permissions)
- **404**: Not Found
- **429**: Too Many Requests (rate limited)
- **500**: Internal Server Error

### Validation Errors

Zod v4 validation errors are formatted consistently:

```typescript
{
  success: false;
  error: "Validation failed";
  details: {
    fieldName: ["Error message 1", "Error message 2"];
  };
}
```

## Hybrid Rate Limiting

### Implementation
- **Hybrid Architecture**: Client UX + Server security
- **Database Authority**: PostgreSQL stores authoritative state
- **Strategic Validation**: Server checks only when needed
- **Client Countdown**: Smooth real-time display without polling
- **Multi-Tab Sync**: localStorage synchronization
- **Graceful Degradation**: Works even when client features fail

### Configuration
```typescript
const RATE_LIMIT_CONFIG = {
  MAX_ATTEMPTS: 5,
  BASE_LOCKOUT_MINUTES: 1,        // Reduced from 15
  ESCALATION_INCREMENT_MINUTES: 5, // Progressive escalation
  MAX_LOCKOUT_HOURS: 1,           // Max 60 minutes
};
```

### How It Works

**Core Philosophy**: "Client provides smooth UX, Server ensures security"

#### The Flow
1. **Authentication Attempt**: Client calls `checkStatus()` before auth
2. **Server Validation**: Server checks database and returns current state
3. **Client Decision**: Client blocks auth if server says locked
4. **Failed Auth**: Server records failure and calculates lockout
5. **Client Countdown**: Client displays smooth countdown using server lockout time
6. **Strategic Checks**: Client validates with server when countdown expires

#### Benefits
- **95% Network Reduction**: From 60+ requests/minute to 1-3 strategic checks
- **Smooth UX**: Real-time countdown without server polling
- **Bulletproof Security**: Server validates every authentication attempt
- **Multi-Tab Sync**: Consistent state across browser tabs
- **Graceful Degradation**: Works even when localStorage/timers fail

#### Security Guarantees
- Client cannot unlock early (server validates every auth attempt)
- Client cannot reset attempts (database is authoritative)
- Client cannot fake countdown (server checks lockout expiry)
- Works across devices (same IP gets same rate limit)
```

## Centralized Alert System

### Server-Side Message Utilities

The API uses a centralized alert system for consistent message handling across all endpoints.

#### Core Functions

```typescript
import { setServerMessage, createSuccessResponse, createErrorResponse } from '@/lib/utils/server-messages';

// Set message via cookie for client-side display
await setServerMessage('success', 'memberAdded', 'admin');

// Create standardized API responses with automatic translation
return createSuccessResponse(request, 'memberAdded', { id: 123 });
return createErrorResponse(request, 'updateFailed', 500);
```

#### Message Types and Mappings

| Message Type | Mapping File | Translation Namespace | Usage |
|--------------|--------------|----------------------|-------|
| **Success** | `success-messages.ts` | `notifications` | Operation confirmations |
| **Errors** | `auth-errors.ts`, `settings-errors.ts` | `auth`, `errors` | Error handling |
| **Warnings** | `warning-messages.ts` | `warnings` | User warnings |
| **Info** | `info-messages.ts` | `common` | Information messages |

#### Auth Context Separation

```typescript
// Automatic context detection from request URL
const authContext = url.pathname.includes('/api/admin') ? 'admin' : 'census';

// Separate cookie systems
cookieName = authContext === 'admin' ? 'auth_toast' : 'census_toast';
```

#### Translation Integration

```typescript
// Server-side translation with locale detection
const locale = await getLocaleFromCookies();
const t = await getTranslations({ locale, namespace: 'notifications' });
const message = t(translationKey);
```

### Best Practices

- ✅ Use `createSuccessResponse()` and `createErrorResponse()` for standardized responses
- ✅ Always specify auth context (`admin` or `census`) for proper message routing
- ✅ Use semantic message keys from mapping files, not hardcoded strings
- ✅ Test message display in both English and Chinese locales
- ❌ Don't return raw error messages without translation
- ❌ Don't mix admin and census message contexts

## Security Features

### Input Validation
- **Zod v4 schemas**: Comprehensive validation for all inputs with `{ error: }` syntax
- **Type safety**: TypeScript integration
- **Internationalisation**: Translated error messages in English and Chinese
- **Sanitization**: Input cleaning and normalization
- **SQL injection prevention**: Parameterized queries only

### Authentication
- **JWT tokens**: Secure session management
- **HTTP-only cookies**: XSS protection
- **CSRF protection**: Token-based validation
- **Session expiration**: 8-hour timeout

### Audit Logging
All API operations are logged for security and compliance:

```typescript
interface AuditLog {
  userId?: number;
  action: string;
  details?: string;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
}
```

## Related Documentation

- [Centralized Alert System Complete Guide](./CENTRALIZED-ALERT-SYSTEM-COMPLETE.md) - Comprehensive alert system documentation
- [Translation Guide](./TRANSLATION.md) - Internationalization implementation
- [Security Guide](./SECURITY.md) - Security implementations

This comprehensive API guide ensures secure, efficient, and maintainable backend operations for the WSCCC Census System.
