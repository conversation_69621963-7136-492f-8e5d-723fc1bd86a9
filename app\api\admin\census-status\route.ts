import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { authOptions } from "@/lib/auth/auth-options";
import { isCensusOpen } from "@/lib/census/census-availability";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";

/**
 * GET /api/admin/census-status
 *
 * Admin-only API endpoint to check census status
 * This ensures system independence by providing admin-specific access to census status
 * without admin components calling census APIs
 */

// Force dynamic rendering to prevent caching of census status
export const dynamic = "force-dynamic";

export async function GET(_request: Request) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require admin authentication
    if (!session || session.user.role !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get census status using shared utility function
    const censusStatus = await isCensusOpen();

    // Return the status
    return NextResponse.json({
      isOpen: censusStatus.isOpen,
      isManualOverride: censusStatus.isManualOverride,
      scheduledState: censusStatus.scheduledState,
      nextChangeTime: censusStatus.nextChangeTime,
    });
  } catch (_error) {
    // Get locale for error translations
    const locale = await getLocaleFromCookies();
    const t = await getTranslations({ locale, namespace: "admin" });

    // Return a default response
    return NextResponse.json(
      {
        isOpen: false,
        error: t("censusStatusCheckFailed"),
      },
      { status: 500 },
    );
  }
}
