import { getTranslations } from "next-intl/server";
import { z } from "zod/v4";
import { getTodayInSydney, startOfDay } from "@/lib/utils/date-time";

/**
 * Validation schema for household registration form
 * Used when a census participant first logs in with a unique code
 * These schemas now support translations using next-intl's errorMap pattern
 */

/**
 * Create household registration schema with translations
 */
export async function createHouseholdRegistrationSchema(locale: "en" | "zh-CN" = "en") {
  const t = await getTranslations({ locale, namespace: "validation" });

  return z.object({
    suburb: z.string().min(1, { error: t("suburbRequired") }),
    headFirstName: z.string().min(1, { error: t("firstNameRequired") }),
    headLastName: z.string().min(1, { error: t("lastNameRequired") }),
    headDateOfBirth: z.coerce.date().refine(
      (date) => {
        const today = getTodayInSydney();
        const dateToCheck = startOfDay(date);
        return dateToCheck <= today;
      },
      { error: t("dateOfBirthCannotBeInTheFuture") },
    ),
    mobilePhone: z
      .string()
      .min(10, { error: t("mobilePhoneMustBeAtLeast10Digi") })
      .max(10, { error: t("mobilePhoneCannotExceed10Digit") })
      .regex(/^\d+$/, { error: t("mobilePhoneCanOnlyContainNumbe") }),
    gender: z.enum(["male", "female", "other"], { error: t("genderRequired") }),
  });
}

// Type exports for server-side validation
export type ServerHouseholdRegistrationFormValues = z.infer<
  Awaited<ReturnType<typeof createHouseholdRegistrationSchema>>
>;
