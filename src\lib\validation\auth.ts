import { getTranslations } from "next-intl/server";
import { z } from "zod/v4";

/**
 * Server-side validation schemas for admin authentication
 * These schemas now support translations using next-intl's error parameter
 * For client-side validation with translations, use the client validation utilities
 */

/**
 * Create login schema with translations
 */
export async function createLoginSchema(locale: "en" | "zh-CN" = "en") {
	const tValidation = await getTranslations({
		locale,
		namespace: "validation",
	});

	return z.object({
		username: z.string().min(1, { error: tValidation("usernameRequired") }),
		password: z.string().min(1, { error: tValidation("passwordRequired") }),
	});
}

/**
 * Create TOTP schema with translations
 */
export async function createTotpSchema(locale: "en" | "zh-CN" = "en") {
	const tCommon = await getTranslations({ locale, namespace: "common" });

	return z.object({
		token: z
			.string()
			.min(6, { error: tCommon("codeMustBeAtLeast6Characters") })
			.max(6, { error: tCommon("codeMustBeAtMost6Characters") })
			.refine((val) => /^[A-Z0-9]{6}$/i.test(val), {
				error: tCommon("codeMustBe6CharactersLettersOr"),
			}),
	});
}

/**
 * Create password change schema with translations
 */
export async function createPasswordChangeSchema(
	locale: "en" | "zh-CN" = "en",
) {
	const tCommon = await getTranslations({ locale, namespace: "common" });
	const tValidation = await getTranslations({
		locale,
		namespace: "validation",
	});

	return z
		.object({
			currentPassword: z
				.string()
				.min(1, { error: tValidation("currentPasswordRequired") }),
			newPassword: z
				.string()
				.min(8, { error: tValidation("newPasswordMinLength") }),
			confirmPassword: z
				.string()
				.min(8, { error: tCommon("confirmPasswordMustBeAtLeast8C") }),
		})
		.refine((data) => data.newPassword === data.confirmPassword, {
			error: tValidation("passwordsDoNotMatch"),
			path: ["confirmPassword"],
		});
}

// Type exports for server-side validation
export type ServerLoginFormValues = z.infer<
	Awaited<ReturnType<typeof createLoginSchema>>
>;
export type ServerTotpFormValues = z.infer<
	Awaited<ReturnType<typeof createTotpSchema>>
>;
export type ServerPasswordChangeFormValues = z.infer<
	Awaited<ReturnType<typeof createPasswordChangeSchema>>
>;
