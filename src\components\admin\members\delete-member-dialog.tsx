'use client';

import {
  Al<PERSON>Circle,
  AlertTriangle,
  Home,
  Info,
  Trash2,
  Users,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer';
import { useIsMobile } from '@/hooks/use-mobile';
import { useMessage } from '@/hooks/useMessage';

interface DeleteMemberDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  memberId: number;
  memberName: string;
  onMemberDeleted: () => void;
}

interface DeletionInfo {
  isHouseholdHead: boolean;
  householdId: number | null;
  memberCount: number;
  canDelete: boolean;
  deleteType: 'regular' | 'head_with_others' | 'head_last_member';
  warningMessage?: string;
}

export function DeleteMemberDialog({
  open,
  onOpenChange,
  memberId,
  memberName,
  onMemberDeleted,
}: DeleteMemberDialogProps) {
  const { showError, showSuccess } = useMessage();
  const isMobile = useIsMobile();
  const t = useTranslations('dialogs');
  const tCommon = useTranslations('common');
  const tErrors = useTranslations('errors');
  const [deletionInfo, setDeletionInfo] = useState<DeletionInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);

  const fetchDeletionInfo = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(
        `/api/admin/members/${memberId}/deletion-info`
      );

      if (!response.ok) {
        throw new Error(tErrors('failedToFetchDeletionInfo'));
      }

      const data = await response.json();
      setDeletionInfo(data);
    } catch (error) {
      console.error('Error fetching deletion info:', error);
      showError('failedToLoadDeletionInformation');
      onOpenChange(false);
    } finally {
      setLoading(false);
    }
  }, [memberId, showError, onOpenChange, tErrors]);

  // Fetch deletion info when dialog opens
  useEffect(() => {
    if (open && memberId) {
      fetchDeletionInfo();
    }
  }, [open, memberId, fetchDeletionInfo]);

  const handleDelete = async () => {
    if (!deletionInfo?.canDelete) return;

    try {
      setIsDeleting(true);
      const response = await fetch(`/api/admin/members/${memberId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || t('failedToDeleteMember'));
      }

      // Use centralized alert system with proper key mapping
      if (deletionInfo.deleteType === 'head_last_member') {
        showSuccess('memberAndHouseholdDeleted');
      } else {
        showSuccess('memberDeleted');
      }
      onOpenChange(false);
      onMemberDeleted();
    } catch (error) {
      console.error('Error deleting member:', error);
      showError('failedToDeleteMember');
    } finally {
      setIsDeleting(false);
    }
  };

  const getDialogContent = () => {
    if (loading) {
      return (
        <div className="py-6 text-center">
          <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent" />
          <p className="text-muted-foreground">
            {tCommon('loadingDeletionInformation')}
          </p>
        </div>
      );
    }

    if (!deletionInfo) {
      return (
        <div className="py-6 text-center">
          <AlertCircle className="mx-auto mb-4 h-8 w-8 text-destructive" />
          <p className="text-muted-foreground">
            {tCommon('failedToLoadDeletionInformatio')}
          </p>
        </div>
      );
    }

    switch (deletionInfo.deleteType) {
      case 'regular':
        return (
          <div className="space-y-4">
            <div className="flex items-center gap-3 rounded-lg bg-muted/20 p-4">
              <Trash2 className="h-5 w-5 text-destructive" />
              <div>
                <p className="font-medium">{t('deleteMember')}</p>
                <p className="text-muted-foreground text-sm">
                  {t('thisWillPermanentlyDelete', { memberName })}
                </p>
              </div>
            </div>
            <p className="text-muted-foreground text-sm">
              {t('thisActionCannotBeUndoneMemberRemoved')}
            </p>
          </div>
        );

      case 'head_with_others':
        return (
          <div className="space-y-4">
            <div className="flex items-center gap-3 rounded-lg border border-destructive/20 bg-destructive/10 p-4">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              <div>
                <p className="font-medium text-destructive">
                  {t('cannotDeleteHouseholdHead')}
                </p>
                <p className="text-muted-foreground text-sm">
                  {t('isHouseholdHeadWithOtherMembers', { memberName })}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2 text-muted-foreground text-sm">
              <Users className="h-4 w-4" />
              <span>{deletionInfo.memberCount} members in household</span>
            </div>
            <p className="text-muted-foreground text-sm">
              {t('pleaseDeleteOtherMembersFirst')}
            </p>
          </div>
        );

      case 'head_last_member':
        return (
          <div className="space-y-4">
            <div className="flex items-center gap-3 rounded-lg border border-orange-200 bg-orange-50 p-4 dark:border-orange-800 dark:bg-orange-950/20">
              <AlertTriangle className="h-5 w-5 text-orange-600 dark:text-orange-400" />
              <div>
                <p className="font-medium text-orange-800 dark:text-orange-200">
                  {t('deleteEntireHousehold')}
                </p>
                <p className="text-orange-700 text-sm dark:text-orange-300">
                  {t('isOnlyMemberInHousehold', { memberName })}
                </p>
              </div>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2 text-muted-foreground">
                <Home className="h-4 w-4" />
                <span>{t('thisWillDeleteEntireHousehold')}</span>
              </div>
              <div className="flex items-center gap-2 text-muted-foreground">
                <Info className="h-4 w-4" />
                <span>{tCommon('associatedUniqueCodeWillBeMark')}</span>
              </div>
            </div>
            <p className="text-muted-foreground text-sm">
              {t('thisActionCannotBeUndoneHouseholdRemoved')}
            </p>
          </div>
        );

      default:
        return null;
    }
  };

  const getDialogTitle = () => {
    if (loading || !deletionInfo) return t('deleteMember');

    switch (deletionInfo.deleteType) {
      case 'regular':
        return t('deleteMember');
      case 'head_with_others':
        return t('cannotDeleteMember');
      case 'head_last_member':
        return t('deleteHousehold');
      default:
        return t('deleteMember');
    }
  };

  const getActionButtons = () => {
    if (loading || !deletionInfo) {
      return (
        <Button onClick={() => onOpenChange(false)} variant="outline">
          {tCommon('cancel')}
        </Button>
      );
    }

    if (!deletionInfo.canDelete) {
      return (
        <Button onClick={() => onOpenChange(false)} variant="outline">
          {tCommon('close')}
        </Button>
      );
    }

    return (
      <>
        <Button onClick={() => onOpenChange(false)} variant="outline">
          {tCommon('cancel')}
        </Button>
        <Button
          className="flex items-center gap-2"
          disabled={isDeleting}
          onClick={handleDelete}
          variant="destructive"
        >
          {isDeleting ? (
            <>
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              {tCommon('deleting')}
            </>
          ) : (
            <>
              <Trash2 className="h-4 w-4" />
              {deletionInfo.deleteType === 'head_last_member'
                ? t('deleteHousehold')
                : t('deleteMember')}
            </>
          )}
        </Button>
      </>
    );
  };

  // Mobile implementation using Drawer (native swipe-to-dismiss from bottom)
  // Features: Visual handle bar, smooth swipe gestures, and hidden scrollbar
  if (isMobile) {
    return (
      <Drawer onOpenChange={onOpenChange} open={open}>
        <DrawerContent className="flex max-h-[80vh] min-h-[60vh] flex-col">
          <DrawerHeader className="pb-4 text-left">
            <DrawerTitle className="font-semibold text-lg">
              {getDialogTitle()}
            </DrawerTitle>
            <DrawerDescription className="text-muted-foreground">
              {deletionInfo?.deleteType === 'head_with_others'
                ? t('thisMemberCannotBeDeleted')
                : t('pleaseReviewInformationBelow')}
            </DrawerDescription>
          </DrawerHeader>

          <div className="scrollbar-hide flex-1 space-y-6 overflow-y-auto px-4">
            {getDialogContent()}
          </div>

          <div className="border-t px-4 pt-4 pb-4">
            <div className="flex justify-end gap-2">{getActionButtons()}</div>
          </div>
        </DrawerContent>
      </Drawer>
    );
  }

  // Desktop implementation using Dialog (unchanged)
  return (
    <Dialog onOpenChange={onOpenChange} open={open}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{getDialogTitle()}</DialogTitle>
          <DialogDescription>
            {deletionInfo?.deleteType === 'head_with_others'
              ? t('thisMemberCannotBeDeleted')
              : t('pleaseReviewInformationBelow')}
          </DialogDescription>
        </DialogHeader>

        {getDialogContent()}

        <DialogFooter className="flex gap-2">{getActionButtons()}</DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
