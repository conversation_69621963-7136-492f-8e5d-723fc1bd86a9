#!/usr/bin/env node

/**
 * Enhanced Prisma Field Name Audit Script - Database Schema Driven
 *
 * This enhanced script uses the actual PostgreSQL database schema as the source of truth
 * and incorporates sophisticated context detection to minimise false positives.
 *
 * Features:
 * - Database schema driven field mappings
 * - SQL alias pattern recognition
 * - Professional context detection
 * - Comprehensive reporting with fix suggestions
 * - Zero false positives goal
 *
 * Usage: node enhanced-prisma-audit.js
 */

import { readdirSync, readFileSync, statSync, writeFileSync } from "fs";
import { extname, join, relative } from "path";

// Complete field mappings extracted from actual PostgreSQL database schema
const DATABASE_SCHEMA_MAPPINGS = {
	// system_settings table
	setting_key: "settingKey",
	setting_value: "settingValue",

	// admins table
	full_name: "fullName",
	two_factor_secret: "twoFactorSecret",
	two_factor_enabled: "twoFactorEnabled",
	two_factor_backup_codes: "twoFactorBackupCodes",
	last_login: "lastLogin",

	// census_years table
	is_active: "isActive",
	start_date: "startDate",
	end_date: "endDate",

	// unique_codes table
	is_assigned: "isAssigned",
	assigned_at: "assignedAt",
	household_id: "householdId",
	census_year_id: "censusYearId",

	// suburbs table
	sal_code: "salCode",
	suburb_name: "suburbName",
	state_code: "stateCode",
	state_name: "stateName",
	display_name: "displayName",
	search_text: "searchText",

	// households table
	first_census_year_id: "firstCensusYearId",
	last_census_year_id: "lastCensusYearId",

	// members table
	first_name: "firstName",
	last_name: "lastName",
	date_of_birth: "dateOfBirth",
	mobile_phone: "mobilePhone",

	// household_members table
	member_id: "memberId",
	is_current: "isCurrent",

	// member_household_history table
	from_household_id: "fromHouseholdId",
	to_household_id: "toHouseholdId",
	change_type: "changeType",
	change_reason: "changeReason",
	change_date: "changeDate",

	// sacrament_types table - no snake_case fields

	// sacraments table
	sacrament_type_id: "sacramentTypeId",

	// census_forms table
	last_updated: "lastUpdated",
	completion_date: "completionDate",

	// auth_rate_limits table
	session_token: "sessionToken",
	failed_attempts: "failedAttempts",
	lockout_until: "lockoutUntil",
	escalation_level: "escalationLevel",
	last_failed_attempt: "lastFailedAttempt",

	// Common timestamp fields (all tables)
	created_at: "createdAt",
	updated_at: "updatedAt",
};

// SQL keywords and patterns that indicate SQL context
const SQL_CONTEXT_INDICATORS = [
	"SELECT",
	"INSERT",
	"UPDATE",
	"DELETE",
	"FROM",
	"WHERE",
	"JOIN",
	"LEFT JOIN",
	"RIGHT JOIN",
	"INNER JOIN",
	"OUTER JOIN",
	"GROUP BY",
	"ORDER BY",
	"HAVING",
	"UNION",
	"CREATE TABLE",
	"ALTER TABLE",
	"DROP TABLE",
	"ADD COLUMN",
	"DROP COLUMN",
	"INDEX",
	"CONSTRAINT",
	"information_schema",
	"table_name",
	"column_name",
	"ordinal_position",
];

// Professional SQL patterns that should be excluded from auditing
const LEGITIMATE_SQL_PATTERNS = [
	// SQL column aliases (professional pattern)
	/\b\w+\.\w+ as ["'`]\w+["'`]/i,
	/\bSELECT\s+\w+ as ["'`]\w+["'`]/i,

	// Information schema queries
	/information_schema\./i,
	/table_name/i,
	/column_name/i,
	/ordinal_position/i,

	// Raw SQL query contexts
	/\$queryRaw/,
	/\$executeRaw/,
	/\$queryRawUnsafe/,
	/\$executeRawUnsafe/,

	// Prisma schema contexts
	/@@map\(/,
	/@map\(/,

	// Database migration contexts
	/CREATE TABLE/i,
	/ALTER TABLE/i,
	/DROP TABLE/i,
	/ADD COLUMN/i,
	/DROP COLUMN/i,

	// SQL template literals
	/`[\s\S]*SELECT[\s\S]*`/,
	/`[\s\S]*INSERT[\s\S]*`/,
	/`[\s\S]*UPDATE[\s\S]*`/,
	/`[\s\S]*DELETE[\s\S]*`/,
];

// Files and directories to exclude from auditing
const EXCLUDED_PATHS = [
	"node_modules",
	".next",
	".git",
	"dist",
	"build",
	"coverage",
	"prisma/migrations",
	"new_server/database-postgresql.sql",
	"prisma/schema.prisma",
	"enhanced-prisma-audit.js",
	"prisma-field-audit.js",
	"verify-sql-integrity.js",
];

// File extensions to audit
const AUDITED_EXTENSIONS = [".ts", ".tsx", ".js", ".jsx"];

class EnhancedPrismaAuditor {
	constructor() {
		this.issues = [];
		this.excludedLines = [];
		this.processedFiles = 0;
		this.totalLines = 0;
		this.startTime = Date.now();
	}

	/**
	 * Main audit function
	 */
	async audit() {
		console.log("🔍 Starting Enhanced Prisma Field Name Audit...");
		console.log(
			"📊 Using actual PostgreSQL database schema as source of truth\n",
		);

		this.scanDirectory(".");
		this.generateReport();

		const duration = ((Date.now() - this.startTime) / 1000).toFixed(2);
		console.log(`\n✅ Enhanced audit completed in ${duration}s`);
		console.log(`📁 Scanned ${this.processedFiles} files`);
		console.log(`📄 Processed ${this.totalLines} lines of code`);
		console.log(`🚨 Found ${this.issues.length} field name mismatches`);
		console.log("📄 Report saved to: enhanced-prisma-audit-report.md");
	}

	/**
	 * Recursively scan directory for files to audit
	 */
	scanDirectory(dirPath) {
		const items = readdirSync(dirPath);

		for (const item of items) {
			const fullPath = join(dirPath, item);
			const stat = statSync(fullPath);

			if (stat.isDirectory()) {
				if (!this.shouldExcludePath(fullPath)) {
					this.scanDirectory(fullPath);
				}
			} else if (stat.isFile() && this.shouldAuditFile(fullPath)) {
				this.auditFile(fullPath);
			}
		}
	}

	/**
	 * Check if path should be excluded
	 */
	shouldExcludePath(path) {
		const relativePath = relative(".", path);
		return EXCLUDED_PATHS.some(
			(excluded) =>
				relativePath.includes(excluded) || relativePath.startsWith(excluded),
		);
	}

	/**
	 * Check if file should be audited
	 */
	shouldAuditFile(filePath) {
		const ext = extname(filePath);
		return (
			AUDITED_EXTENSIONS.includes(ext) && !this.shouldExcludePath(filePath)
		);
	}

	/**
	 * Audit a single file
	 */
	auditFile(filePath) {
		try {
			const content = readFileSync(filePath, "utf8");
			const lines = content.split("\n");
			this.processedFiles++;
			this.totalLines += lines.length;

			lines.forEach((line, index) => {
				this.auditLine(filePath, index + 1, line, content);
			});
		} catch (error) {
			console.warn(
				`⚠️  Warning: Could not scan file ${filePath}: ${error.message}`,
			);
		}
	}

	/**
	 * Audit a single line for field name mismatches
	 */
	auditLine(filePath, lineNumber, line, fullContent) {
		const relativePath = relative(".", filePath);

		// Skip if line should be excluded entirely
		if (this.shouldSkipLine(line, fullContent, lineNumber)) {
			return;
		}

		// Check each database field mapping
		for (const [dbField, prismaField] of Object.entries(
			DATABASE_SCHEMA_MAPPINGS,
		)) {
			this.checkFieldUsage(
				relativePath,
				lineNumber,
				line,
				dbField,
				prismaField,
			);
		}
	}

	/**
	 * Check if a line is in SQL context
	 */
	isInSQLContext(line, content, lineNumber) {
		const trimmedLine = line.trim();
		const upperLine = trimmedLine.toUpperCase();

		// Check if line contains SQL keywords
		for (const keyword of SQL_CONTEXT_INDICATORS) {
			if (upperLine.includes(keyword.toUpperCase())) {
				return true;
			}
		}

		// Check for SQL fragments in string literals
		if (
			trimmedLine.includes("AND ") ||
			trimmedLine.includes("WHERE ") ||
			trimmedLine.includes("SELECT ") ||
			trimmedLine.includes("FROM ") ||
			trimmedLine.includes("JOIN ") ||
			trimmedLine.includes("GROUP BY ") ||
			trimmedLine.includes("ORDER BY ")
		) {
			return true;
		}

		// Check for SQL template literal patterns
		if (
			trimmedLine.includes("`AND ") ||
			trimmedLine.includes("`SELECT ") ||
			trimmedLine.includes("`WHERE ") ||
			trimmedLine.includes("`FROM ") ||
			trimmedLine.includes("`JOIN ") ||
			trimmedLine.includes("`GROUP BY ") ||
			trimmedLine.includes("`ORDER BY ")
		) {
			return true;
		}

		// Check if we're inside a SQL template literal
		const lines = content.split("\n");
		let inSQLBlock = false;
		let sqlBlockStart = -1;

		for (let i = 0; i < lines.length; i++) {
			const currentLine = lines[i];

			// Check for start of SQL template literal
			if (
				currentLine.includes("const query = `") ||
				(currentLine.includes("= `") &&
					currentLine.toUpperCase().includes("SELECT"))
			) {
				inSQLBlock = true;
				sqlBlockStart = i + 1;
			}

			// Check for end of template literal
			if (inSQLBlock && currentLine.includes("`;")) {
				if (i + 1 >= sqlBlockStart && i + 1 <= lineNumber) {
					return lineNumber >= sqlBlockStart && lineNumber <= i + 1;
				}
				inSQLBlock = false;
			}

			// If we're at the target line and in SQL block
			if (i + 1 === lineNumber && inSQLBlock) {
				return true;
			}
		}

		return false;
	}

	/**
	 * Check if a line should be skipped entirely
	 */
	shouldSkipLine(line, content, lineNumber) {
		const trimmedLine = line.trim();

		// Skip empty lines and comments
		if (
			!trimmedLine ||
			trimmedLine.startsWith("//") ||
			trimmedLine.startsWith("*") ||
			trimmedLine.startsWith("/*")
		) {
			return true;
		}

		// Skip field mapping definitions (like 'field_name': 'fieldName')
		if (trimmedLine.match(/^['"`][\w_]+['"`]\s*:\s*['"`]\w+['"`],?\s*$/)) {
			this.excludedLines.push({
				line: lineNumber,
				reason: "Field mapping definition",
				pattern: "Field mapping object",
			});
			return true;
		}

		// Skip if line is in SQL context (snake_case is correct in SQL)
		if (this.isInSQLContext(line, content, lineNumber)) {
			this.excludedLines.push({
				line: lineNumber,
				reason: "SQL context - snake_case is correct",
				pattern: "SQL query",
			});
			return true;
		}

		// Skip lines matching legitimate SQL patterns
		for (const pattern of LEGITIMATE_SQL_PATTERNS) {
			if (pattern.test(trimmedLine)) {
				this.excludedLines.push({
					line: lineNumber,
					reason: "Legitimate SQL pattern",
					pattern: pattern.toString(),
				});
				return true;
			}
		}

		return false;
	}

	/**
	 * Check for specific field usage issues
	 */
	checkFieldUsage(filePath, lineNumber, line, dbField, prismaField) {
		const trimmedLine = line.trim();

		// Look for database field name usage in TypeScript contexts
		const patterns = [
			// Object property access: obj.field_name
			new RegExp(`\\b\\w+\\.${dbField}\\b`, "g"),
			// Object destructuring: { field_name }
			new RegExp(`\\{[^}]*\\b${dbField}\\b[^}]*\\}`, "g"),
			// Interface/type definitions: field_name:
			new RegExp(`\\b${dbField}\\s*:`, "g"),
			// String literals in non-SQL contexts
			new RegExp(`['"\`]${dbField}['"\`]`, "g"),
		];

		patterns.forEach((pattern, patternIndex) => {
			const matches = [...trimmedLine.matchAll(pattern)];
			matches.forEach((match) => {
				if (!this.shouldSkipMatch(trimmedLine, match, patternIndex, dbField)) {
					this.issues.push({
						file: filePath,
						line: lineNumber,
						dbField,
						prismaField,
						context: trimmedLine,
						patternType: this.getPatternType(patternIndex),
						suggestion: this.generateFixSuggestion(
							trimmedLine,
							dbField,
							prismaField,
						),
					});
				}
			});
		});
	}

	/**
	 * Check if a specific match should be skipped
	 */
	shouldSkipMatch(line, match, patternIndex, dbField) {
		// Skip if it's in a SQL alias context
		if (line.includes(" as ") && line.includes(dbField)) {
			return true;
		}

		// Skip if it's in a comment
		if (line.includes("//") && line.indexOf("//") < line.indexOf(dbField)) {
			return true;
		}

		return false;
	}

	/**
	 * Get pattern type description
	 */
	getPatternType(patternIndex) {
		const types = [
			"Property Access",
			"Object Destructuring",
			"Interface Definition",
			"String Literal",
		];
		return types[patternIndex] || "Unknown";
	}

	/**
	 * Generate fix suggestion for an issue
	 */
	generateFixSuggestion(line, dbField, prismaField) {
		return line.replace(new RegExp(dbField, "g"), prismaField);
	}

	/**
	 * Generate comprehensive audit report
	 */
	generateReport() {
		const report = this.buildReportContent();
		writeFileSync("enhanced-prisma-audit-report.md", report);
	}

	/**
	 * Build the report content
	 */
	buildReportContent() {
		const duration = ((Date.now() - this.startTime) / 1000).toFixed(2);

		let report = "# Enhanced Prisma Field Name Audit Report\n\n";
		report += `**Generated:** ${new Date().toISOString()}\n`;
		report += `**Duration:** ${duration}s\n`;
		report += `**Files Scanned:** ${this.processedFiles}\n`;
		report += `**Lines Processed:** ${this.totalLines}\n`;
		report += `**Total Issues Found:** ${this.issues.length}\n`;
		report += `**Lines Excluded:** ${this.excludedLines.length}\n\n`;

		report += "## Summary\n\n";
		report +=
			"This enhanced audit uses the actual PostgreSQL database schema as the source of truth\n";
		report +=
			"and incorporates sophisticated context detection to minimize false positives.\n\n";

		if (this.issues.length === 0) {
			report += "## ✅ No Issues Found\n\n";
			report +=
				"All field names are correctly using Prisma naming conventions!\n\n";
		} else {
			report += "## 🚨 Issues Found\n\n";
			this.issues.forEach((issue, index) => {
				report += `### ${index + 1}. ${issue.file}\n\n`;
				report += `**Line:** ${issue.line}\n`;
				report += `**Type:** ${issue.patternType}\n`;
				report += `**Issue:** Database field '${issue.dbField}' should be Prisma field '${issue.prismaField}'\n`;
				report += `**Context:** \`${issue.context}\`\n`;
				report += `**Suggested Fix:** \`${issue.suggestion}\`\n\n`;
				report += "---\n\n";
			});
		}

		// Add field mapping reference
		report += "## 📋 Database Schema Field Mappings\n\n";
		report += "Based on actual PostgreSQL database schema:\n\n";
		report += "| Database Column (snake_case) | Prisma Field (camelCase) |\n";
		report += "|------------------------------|-------------------------|\n";

		Object.entries(DATABASE_SCHEMA_MAPPINGS).forEach(
			([dbField, prismaField]) => {
				report += `| \`${dbField}\` | \`${prismaField}\` |\n`;
			},
		);

		return report;
	}

	/**
	 * Validate database schema completeness
	 */
	validateSchemaCompleteness() {
		console.log("\n🔍 Validating database schema completeness...");

		// Check if all common database patterns are covered
		const commonPatterns = [
			"user_id",
			"created_by",
			"updated_by",
			"deleted_at",
			"deleted_by",
			"start_time",
			"end_time",
			"is_deleted",
			"is_enabled",
			"is_visible",
			"sort_order",
			"display_order",
			"parent_id",
			"external_id",
		];

		const missingMappings = commonPatterns.filter(
			(pattern) => !Object.hasOwn(DATABASE_SCHEMA_MAPPINGS, pattern),
		);

		if (missingMappings.length > 0) {
			console.log(
				`⚠️  Potentially missing field mappings: ${missingMappings.join(", ")}`,
			);
		} else {
			console.log("✅ Database schema mappings appear complete");
		}
	}

	/**
	 * Generate statistics about the audit
	 */
	generateStatistics() {
		const stats = {
			totalFiles: this.processedFiles,
			totalLines: this.totalLines,
			totalIssues: this.issues.length,
			excludedLines: this.excludedLines.length,
			issuesByType: {},
			issuesByFile: {},
			mostCommonIssues: {},
		};

		// Group issues by type
		this.issues.forEach((issue) => {
			stats.issuesByType[issue.patternType] =
				(stats.issuesByType[issue.patternType] || 0) + 1;
			stats.issuesByFile[issue.file] =
				(stats.issuesByFile[issue.file] || 0) + 1;
			stats.mostCommonIssues[issue.dbField] =
				(stats.mostCommonIssues[issue.dbField] || 0) + 1;
		});

		return stats;
	}

	/**
	 * Enhanced audit with validation and statistics
	 */
	async auditWithValidation() {
		await this.audit();
		this.validateSchemaCompleteness();

		const stats = this.generateStatistics();
		console.log("\n📊 Audit Statistics:");
		console.log(
			`   Files with issues: ${Object.keys(stats.issuesByFile).length}`,
		);
		console.log(
			"   Most common issue types:",
			Object.entries(stats.issuesByType)
				.sort(([, a], [, b]) => b - a)
				.slice(0, 3)
				.map(([type, count]) => `${type} (${count})`)
				.join(", "),
		);
	}
}

// Enhanced audit execution with command line options
const args = process.argv.slice(2);
const auditor = new EnhancedPrismaAuditor();

if (args.includes("--validate") || args.includes("-v")) {
	auditor.auditWithValidation().catch(console.error);
} else {
	auditor.audit().catch(console.error);
}
