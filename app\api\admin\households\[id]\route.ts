import { type NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { authOptions } from "@/lib/auth/auth-options";
import { getHouseholdMemberWithDetails } from "@/lib/db/household-members";
import { getHouseholdById, updateHousehold } from "@/lib/db/households";
import { updateMember } from "@/lib/db/members";
import { prisma } from "@/lib/db/prisma";
import {
	getErrorMessage,
	getZodErrorDetails,
} from "@/lib/utils/error-handling";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";
import { createUpdateHouseholdSchema } from "@/lib/validation/household";

/**
 * GET /api/admin/households/:id
 *
 * Fetches a specific household with its members
 * Only accessible to admin users
 */
export async function GET(
	_request: NextRequest,
	{ params }: { params: Promise<{ id: string }> },
) {
	try {
		// Check authentication
		const session = await getServerSession(authOptions);

		// Require admin authentication
		if (!session || session.user.role !== "admin") {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Get locale and translations
		const locale = await getLocaleFromCookies();
		const t = await getTranslations({ locale, namespace: "errors" });

		// In Next.js 15+, params is a Promise that must be awaited
		const { id: idString } = await params;
		// Ensure id is a valid number
		const id = Number.parseInt(idString, 10);

		if (Number.isNaN(id)) {
			return NextResponse.json(
				{ error: t("invalidHouseholdId") },
				{ status: 400 },
			);
		}

		// Get the household
		const household = await getHouseholdById(id);

		if (!household) {
			return NextResponse.json(
				{ error: t("householdNotFound") },
				{ status: 404 },
			);
		}

		// Get the household members (including inactive for complete history view)
		const members = await getHouseholdMemberWithDetails(id, undefined, true);

		// Get census years for the household using Prisma
		const censusYears = await prisma.censusYear.findMany({
			where: {
				id: {
					gte: household.firstCensusYearId,
					lte: household.lastCensusYearId,
				},
			},
			orderBy: { year: "asc" },
		});

		// Get form status for each census year using Prisma
		const formStatus = await prisma.censusForm.findMany({
			where: { householdId: id },
		});

		// Get the most recent unique code for this household using Prisma
		const uniqueCodeResult = await prisma.uniqueCode.findFirst({
			where: { householdId: id },
			orderBy: { createdAt: "desc" },
		});

		const uniqueCode = uniqueCodeResult?.code || null;

		// Transform household object to match expected interface
		const transformedHousehold = {
			id: household.id,
			suburb: household.suburb,
			firstCensusYearId: household.firstCensusYearId,
			lastCensusYearId: household.lastCensusYearId,
			createdAt: household.createdAt.toISOString(),
			updatedAt: household.updatedAt.toISOString(),
		};

		// Transform census years to match expected interface
		const transformedCensusYears = censusYears.map((cy) => ({
			id: cy.id,
			year: cy.year,
			isActive: cy.isActive,
		}));

		// Transform form status to match expected interface
		const transformedFormStatus = formStatus.map((fs) => ({
			id: fs.id,
			householdId: fs.householdId,
			censusYearId: fs.censusYearId,
			status: fs.status,
			completionDate: fs.completionDate
				? fs.completionDate.toISOString()
				: null,
		}));

		return NextResponse.json({
			household: transformedHousehold,
			members,
			censusYears: transformedCensusYears,
			formStatus: transformedFormStatus,
			uniqueCode,
		});
	} catch (error) {
		const locale = await getLocaleFromCookies();
		const t = await getTranslations({ locale, namespace: "admin" });
		return NextResponse.json(
			{ error: t("householdFetchFailed"), details: getErrorMessage(error) },
			{ status: 500 },
		);
	}
}

/**
 * PUT /api/admin/households/:id
 *
 * Updates a household
 * Only accessible to admin users
 */
export async function PUT(
	request: NextRequest,
	{ params }: { params: Promise<{ id: string }> },
) {
	try {
		// Check authentication
		const session = await getServerSession(authOptions);

		// Require admin authentication
		if (!session || session.user.role !== "admin") {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Get locale and translations
		const locale = await getLocaleFromCookies();
		const t = await getTranslations({ locale, namespace: "errors" });

		// In Next.js 15+, params is a Promise that must be awaited
		const { id: idString } = await params;
		const id = Number.parseInt(idString, 10);

		if (Number.isNaN(id)) {
			return NextResponse.json(
				{ error: t("invalidHouseholdId") },
				{ status: 400 },
			);
		}

		// Check if the household exists
		const existingHousehold = await getHouseholdById(id);

		if (!existingHousehold) {
			return NextResponse.json(
				{ error: t("householdNotFound") },
				{ status: 404 },
			);
		}

		// Parse and validate request body with translations
		const body = await request.json();
		const updateHouseholdSchema = await createUpdateHouseholdSchema(locale);
		const validationResult = updateHouseholdSchema.safeParse(body);

		if (!validationResult.success) {
			const t = await getTranslations({ locale, namespace: "admin" });
			return NextResponse.json(
				{
					error: t("invalidRequestData"),
					details: getZodErrorDetails(validationResult.error),
				},
				{ status: 400 },
			);
		}

		const data = validationResult.data;

		// Cannot change first_census_year_id or last_census_year_id as they are historical records
		await updateHousehold(id, {
			suburb: data.suburb,
		});

		// Find the household head member using Prisma
		const headMemberRelation = await prisma.householdMember.findFirst({
			where: {
				householdId: id,
				relationship: "head",
				isCurrent: true,
			},
			include: {
				member: true,
			},
		});

		// Log all members for debugging
		if (process.env.NODE_ENV === "development") {
		}

		if (headMemberRelation) {
			const headMember = headMemberRelation.member;

			if (process.env.NODE_ENV === "development") {
			}

			// Update the household head member information
			await updateMember(headMember.id, {
				firstName: data.head_first_name,
				lastName: data.head_last_name,
				dateOfBirth: data.head_date_of_birth
					? new Date(data.head_date_of_birth)
					: null,
				mobilePhone: data.head_mobile_phone,
				gender: data.head_gender,
			});

			if (process.env.NODE_ENV === "development") {
			}
		} else if (process.env.NODE_ENV === "development") {
		}

		// Get the updated household
		const updatedHousehold = await getHouseholdById(id);

		return NextResponse.json(updatedHousehold);
	} catch (error) {
		if (process.env.NODE_ENV === "development") {
		}
		const locale = await getLocaleFromCookies();
		const t = await getTranslations({ locale, namespace: "errors" });
		return NextResponse.json(
			{ error: t("householdUpdateFailed"), details: getErrorMessage(error) },
			{ status: 500 },
		);
	}
}

/**
 * DELETE /api/admin/households/:id
 *
 * Deletes a household
 * Only accessible to admin users
 */
export async function DELETE(
	_request: NextRequest,
	{ params }: { params: Promise<{ id: string }> },
) {
	try {
		// Check authentication
		const session = await getServerSession(authOptions);

		// Require admin authentication
		if (!session || session.user.role !== "admin") {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Get locale and translations
		const locale = await getLocaleFromCookies();
		const t = await getTranslations({ locale, namespace: "errors" });

		// In Next.js 15+, params is a Promise that must be awaited
		const { id: idString } = await params;
		const id = Number.parseInt(idString, 10);

		if (Number.isNaN(id)) {
			return NextResponse.json(
				{ error: t("invalidHouseholdId") },
				{ status: 400 },
			);
		}

		// Check if the household exists
		const existingHousehold = await getHouseholdById(id);

		if (!existingHousehold) {
			return NextResponse.json(
				{ error: t("householdNotFound") },
				{ status: 404 },
			);
		}

		// Check if the household has any members other than the household head (only check current members)
		const members = await getHouseholdMemberWithDetails(
			Number.parseInt(id.toString(), 10),
		);
		const nonHeadMembers = members.filter(
			(member) => member.relationship !== "head",
		);

		if (nonHeadMembers.length > 0) {
			return NextResponse.json(
				{
					error: t("cannotDeleteHouseholdWithMembers"),
					message:
						"Please remove all non-head members from this household before deleting it",
				},
				{ status: 400 },
			);
		}

		// Use transaction to ensure all operations succeed or fail together
		await prisma.$transaction(async (tx) => {
			// Update any associated unique codes to unassigned
			await tx.uniqueCode.updateMany({
				where: { householdId: id },
				data: {
					isAssigned: false,
					assignedAt: null,
					householdId: null,
					updatedAt: new Date(),
				},
			});

			// Get household head member ID
			const householdHead = members.find(
				(member) => member.relationship === "head",
			);

			if (householdHead) {
				// Delete sacraments for the household head
				await tx.sacrament.deleteMany({
					where: { memberId: householdHead.memberId },
				});

				// Delete household member relationship
				await tx.householdMember.deleteMany({
					where: {
						householdId: id,
						memberId: householdHead.memberId,
					},
				});

				// Delete the household head member with error handling
				try {
					await tx.member.delete({
						where: { id: householdHead.memberId },
					});
				} catch (error: unknown) {
					// Handle P2025 error (record not found) - member already deleted
					if (
						error &&
						typeof error === "object" &&
						"code" in error &&
						error.code === "P2025"
					) {
					} else {
						// Re-throw other errors
						throw error;
					}
				}
			}

			// Delete the household with error handling
			try {
				await tx.household.delete({
					where: { id },
				});
			} catch (error: unknown) {
				// Handle P2025 error (record not found) - household already deleted
				if (
					error &&
					typeof error === "object" &&
					"code" in error &&
					error.code === "P2025"
				) {
					// Continue as if successful since the goal (household deleted) is achieved
				} else {
					// Re-throw other errors
					throw error;
				}
			}
		});

		return NextResponse.json({ success: true });
	} catch (error) {
		const locale = await getLocaleFromCookies();
		const t = await getTranslations({ locale, namespace: "admin" });
		return NextResponse.json(
			{ error: t("householdDeleteFailed"), details: getErrorMessage(error) },
			{ status: 500 },
		);
	}
}
