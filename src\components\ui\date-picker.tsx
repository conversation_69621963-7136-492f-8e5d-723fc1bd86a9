"use client";

/**
 * Date Picker Component
 *
 * A simplified date picker that uses a popover to display a calendar.
 * This component is designed for census participants who only need to select dates (no time).
 *
 * Usage:
 * <DatePicker
 *   date={date}
 *   setDate={setDate}
 *   placeholderText="Select date"
 * />
 */

import { CalendarIcon } from "lucide-react";
import * as React from "react";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { formatDate } from "@/lib/utils/date-time";

interface DatePickerProps {
  date: Date | null;
  setDate: (date: Date | null) => void;
  disabled?: boolean;
  className?: string;
  placeholderText?: string;
  maxDate?: Date; // Maximum selectable date (dates after this will be disabled)
  preventFutureDates?: boolean; // Convenience prop to prevent future dates (uses Sydney timezone)
}

export function DatePicker({
  date,
  setDate,
  disabled = false,
  className,
  placeholderText = "Select date",
  maxDate,
  preventFutureDates = false,
}: DatePickerProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const [temporaryDate, setTemporaryDate] = React.useState<Date | null>(null);
  const [calendarView, setCalendarView] = React.useState<"day" | "month" | "year">("day");
  const [currentMonth, setCurrentMonth] = React.useState<Date>(new Date());

  // Update temporaryDate when date changes (client-side only)
  React.useEffect(() => {
    setTemporaryDate(date);
    if (date) {
      setCurrentMonth(date);
    }
  }, [date]);

  // Handle popover close - update the actual date ONLY if isOpen changes from true to false
  React.useEffect(() => {
    // Only run when the popover closes
    if (isOpen === false) {
      // Update the date if it has changed
      if (temporaryDate !== date) {
        // Use a timeout to prevent state updates during render
        const timer = setTimeout(() => {
          setDate(temporaryDate);
        }, 0);
        return () => clearTimeout(timer);
      }
    }
  }, [isOpen, temporaryDate, date, setDate]);

  // Handle date selection from Calendar component
  const handleDateChange = (newDate: Date) => {
    setTemporaryDate(newDate);
    // Update current month when date changes
    setCurrentMonth(new Date(newDate));
  };

  // Check if the current month being displayed is the current month
  const isCurrentMonthToday = () => {
    const today = new Date();
    return (
      currentMonth.getMonth() === today.getMonth() &&
      currentMonth.getFullYear() === today.getFullYear()
    );
  };

  // Handle the "Done" button click
  const handleDone = () => {
    setDate(temporaryDate);
    setIsOpen(false);
  };

  // Format the date for display
  const formattedDate = date ? formatDate(date, "MMMM d, yyyy") : "";

  return (
    <div className={cn("relative", className)}>
      <Popover onOpenChange={setIsOpen} open={isOpen}>
        <PopoverTrigger asChild>
          <Button
            className={cn(
              "w-full cursor-pointer justify-start text-left font-normal",
              !date && "text-muted-foreground",
              "h-10",
            )}
            disabled={disabled}
            variant="outline"
          >
            <CalendarIcon className="mr-2 h-4 w-4 flex-shrink-0" />
            <span className="truncate">{formattedDate || placeholderText}</span>
          </Button>
        </PopoverTrigger>
        <PopoverContent align="start" className="w-auto p-0">
          {/* Calendar Component */}
          <Calendar
            date={temporaryDate}
            maxDate={maxDate}
            onDateChange={handleDateChange} // Hide the footer in Calendar
            onDone={handleDone} // Hide the Today button in Calendar
            onMonthChange={setCurrentMonth} // This is not a standalone calendar
            onToday={() => {
              const today = new Date();
              setTemporaryDate(today);
              setCurrentMonth(today); // Update current month to today
              setCalendarView("day"); // Always return to day view when Today button is clicked
            }}
            onViewChange={setCalendarView} // Control the view from outside
            preventFutureDates={preventFutureDates} // Track view changes
            showFooter={false} // Track month changes
            showTodayButton={false} // Pass through maxDate prop
            standalone={false} // Pass through preventFutureDates prop
            view={calendarView} // Handle Today button click
          />

          {/* Footer with Today and Done buttons */}
          <div className="flex justify-between border-t p-3">
            {/* Today button - show in month/year views OR when not viewing current month */}
            {calendarView === "month" || calendarView === "year" || !isCurrentMonthToday() ? (
              <Button
                className="cursor-pointer"
                onClick={() => {
                  const today = new Date();
                  setTemporaryDate(today);
                  setCurrentMonth(today); // Update current month to today
                  setCalendarView("day"); // Always return to day view when Today button is clicked
                }}
                size="sm"
                variant="ghost"
              >
                Today
              </Button>
            ) : (
              <div /> /* Empty div to maintain layout when Today button is not shown */
            )}

            {/* Done button */}
            <Button className="cursor-pointer" onClick={handleDone} size="sm">
              Done
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
