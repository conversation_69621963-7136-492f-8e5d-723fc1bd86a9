"use client";

import { useTranslations } from "next-intl";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Drawer, DrawerContent, DrawerHeader, DrawerTitle } from "@/components/ui/drawer";
import { useIsMobile } from "@/hooks/use-mobile";
import type { ICensusWelcomeModalProps } from "@/types/welcome-modal";
import { WelcomeModalContent } from "./WelcomeModalContent";

/**
 * CensusWelcomeModal component provides responsive welcome modal for census participants
 * Uses Dialog for desktop (≥768px) and Drawer for mobile (<768px)
 * Features native swipe-to-dismiss with visual handle bar on mobile
 * Maintains design system consistency with shadcn/ui components
 */
export function CensusWelcomeModal({ isOpen, onClose, onDismiss }: ICensusWelcomeModalProps) {
  const isMobile = useIsMobile();
  const t = useTranslations("onboarding");

  const handleGetStarted = () => {
    // Close modal without permanent dismissal
    onClose();
  };

  const handleDismiss = () => {
    // Permanently dismiss modal and save preference
    onDismiss();
  };

  // Shared content component
  const content = <WelcomeModalContent onGetStarted={handleGetStarted} />;

  // Mobile implementation using Drawer (native swipe-to-dismiss from bottom)
  // Features: Visual handle bar, smooth swipe gestures, and professional scrolling
  if (isMobile) {
    return (
      <Drawer onOpenChange={onClose} open={isOpen}>
        <DrawerContent
          aria-describedby="welcome-modal-description"
          className="flex max-h-[80vh] min-h-[60vh] flex-col"
        >
          <DrawerHeader className="sr-only">
            <DrawerTitle>{t("welcomeDescription")}</DrawerTitle>
          </DrawerHeader>

          <div className="sr-only" id="welcome-modal-description">
            {t("welcomeDescription")}
          </div>

          <div className="scrollbar-hide flex-1 overflow-y-auto px-4">{content}</div>
        </DrawerContent>
      </Drawer>
    );
  }

  // Desktop implementation using Dialog
  return (
    <Dialog onOpenChange={onClose} open={isOpen}>
      <DialogContent
        aria-describedby="welcome-modal-description"
        className="max-h-[90vh] overflow-y-auto sm:max-w-[600px]"
        showCloseButton={true}
      >
        <DialogHeader className="sr-only">
          <DialogTitle>{t("welcomeDescription")}</DialogTitle>
        </DialogHeader>

        <div className="sr-only" id="welcome-modal-description">
          {t("welcomeDescription")}
        </div>

        {content}
      </DialogContent>
    </Dialog>
  );
}
