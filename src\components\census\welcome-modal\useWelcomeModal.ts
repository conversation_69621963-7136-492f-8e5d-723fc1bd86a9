"use client";

import { useCallback, useEffect, useState } from "react";
import { type IUseWelcomeModalReturn, WELCOME_MODAL_STORAGE_KEY } from "@/types/welcome-modal";

/**
 * Custom hook for managing welcome modal state and localStorage preferences
 * Implements census-specific storage keys and proper cleanup mechanisms
 */
export function useWelcomeModal(): IUseWelcomeModalReturn {
  const [isOpen, setIsOpen] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);

  // Initialize dismissal state from localStorage on mount
  useEffect(() => {
    if (typeof window === "undefined") return;

    try {
      const dismissed = localStorage.getItem(WELCOME_MODAL_STORAGE_KEY);
      setIsDismissed(dismissed === "true");
    } catch (error) {
      // Handle localStorage errors gracefully
      setIsDismissed(false);
    }
  }, []);

  /**
   * Dismiss the modal and save preference to localStorage
   * This prevents the modal from showing again for this user
   */
  const dismissModal = useCallback(() => {
    try {
      localStorage.setItem(WELCOME_MODAL_STORAGE_KEY, "true");
      setIsDismissed(true);
      setIsOpen(false);
    } catch (error) {
      // Handle localStorage errors gracefully
      // Still close the modal even if we can't save the preference
      setIsOpen(false);
    }
  }, []);

  /**
   * Reset welcome modal preferences (used during account deletion)
   * This ensures re-registered accounts see the welcome modal again
   */
  const resetPreferences = useCallback(() => {
    try {
      localStorage.removeItem(WELCOME_MODAL_STORAGE_KEY);
      setIsDismissed(false);
    } catch (error) {
      // Handle localStorage errors gracefully
      console.warn("Failed to reset welcome modal preference:", error);
    }
  }, []);

  return {
    isOpen,
    isDismissed,
    setIsOpen,
    dismissModal,
    resetPreferences,
  };
}

/**
 * Utility function to reset welcome modal preferences
 * Can be called from account deletion functionality
 * @deprecated Use clearCensusLocalStorage() instead for comprehensive cleanup
 */
export function resetWelcomeModalPreferences(): void {
  try {
    localStorage.removeItem(WELCOME_MODAL_STORAGE_KEY);
  } catch (error) {
    // Silently handle localStorage errors
  }
}
