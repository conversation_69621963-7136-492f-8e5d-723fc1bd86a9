import { type NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { z } from "zod/v4";
import { authOptions } from "@/lib/auth/auth-options";
import { getHouseholdMemberWithDetails } from "@/lib/db/household-members";
import { getHouseholdById } from "@/lib/db/households";
import { getErrorMessage, getZodErrorDetails } from "@/lib/utils/error-handling";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";
import { createBulkHouseholdValidationSchema } from "@/lib/validation/household";

/**
 * POST /api/admin/households/bulk-delete-validation
 *
 * Validates multiple households for deletion without actually deleting them
 * Only accessible to admin users
 */
export async function POST(request: NextRequest) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const t = await getTranslations({ locale, namespace: "errors" });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require admin authentication
    if (!session || session.user.role !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse and validate request body with translations
    const body = await request.json();
    const bulkValidationSchema = await createBulkHouseholdValidationSchema(locale);
    const validatedData = bulkValidationSchema.parse(body);
    const { householdIds } = validatedData;

    // Validate each household
    const validationResults = [];
    const deletableHouseholds = [];
    const undeletableHouseholds = [];

    for (const householdId of householdIds) {
      try {
        // Check if household exists
        const household = await getHouseholdById(householdId);
        if (!household) {
          validationResults.push({
            householdId,
            canDelete: false,
            reason: "Household not found",
            deleteType: "not_found",
          });
          continue;
        }

        // Get household members (only current members for deletion validation)
        const members = await getHouseholdMemberWithDetails(householdId);
        const nonHeadMembers = members.filter((member) => member.relationship !== "head");
        const householdHead = members.find((member) => member.relationship === "head");

        if (nonHeadMembers.length > 0) {
          // Cannot delete - has non-head members
          const result = {
            householdId,
            canDelete: false,
            reason: "Has members other than household head",
            deleteType: "household_with_members",
            memberCount: members.length,
            nonHeadMemberCount: nonHeadMembers.length,
            householdHead: householdHead
              ? {
                  name: `${householdHead.firstName} ${householdHead.lastName}`,
                  id: householdHead.memberId,
                }
              : null,
          };
          validationResults.push(result);
          undeletableHouseholds.push(result);
        } else {
          // Can delete - only household head
          const result = {
            householdId,
            canDelete: true,
            reason: "Only household head present",
            deleteType: "household_head_only",
            memberCount: members.length,
            nonHeadMemberCount: 0,
            householdHead: householdHead
              ? {
                  name: `${householdHead.firstName} ${householdHead.lastName}`,
                  id: householdHead.memberId,
                }
              : null,
          };
          validationResults.push(result);
          deletableHouseholds.push(result);
        }
      } catch (error) {
        validationResults.push({
          householdId,
          canDelete: false,
          reason: "Validation error",
          deleteType: "error",
          error: getErrorMessage(error),
        });
      }
    }

    // Calculate summary statistics
    const totalHouseholds = householdIds.length;
    const deletableCount = deletableHouseholds.length;
    const undeletableCount = undeletableHouseholds.length;
    const canProceed = deletableCount > 0;

    // Group undeletable households by reason
    const undeletableByReason = undeletableHouseholds.reduce(
      (acc, household) => {
        const reason = household.reason;
        if (!acc[reason]) {
          acc[reason] = [];
        }
        acc[reason].push(household);
        return acc;
      },
      {} as Record<string, typeof undeletableHouseholds>,
    );

    return NextResponse.json({
      canProceed,
      totalHouseholds,
      deletableCount,
      undeletableCount,
      deletableHouseholds,
      undeletableHouseholds,
      undeletableByReason,
      validationResults,
      summary: {
        total: totalHouseholds,
        deletable: deletableCount,
        undeletable: undeletableCount,
        withMembers: undeletableHouseholds.filter((h) => h.deleteType === "household_with_members")
          .length,
        notFound: undeletableHouseholds.filter((h) => h.deleteType === "not_found").length,
        errors: undeletableHouseholds.filter((h) => h.deleteType === "error").length,
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: t("invalidRequestData"),
          details: getZodErrorDetails(error),
        },
        { status: 400 },
      );
    }

    return NextResponse.json(
      {
        error: t("failedToValidateBulkDelete"),
        details: getErrorMessage(error),
      },
      { status: 500 },
    );
  }
}
