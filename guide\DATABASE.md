# WSCCC Census System Database Guide

## Overview

The WSCCC Census System uses PostgreSQL as its primary database with Prisma ORM for type-safe database operations. This guide covers the database schema, relationships, optimization strategies, and management procedures.

## Database Technology Stack

### Core Technologies
- **PostgreSQL 15+**: Primary database engine
- **Prisma ORM**: Type-safe database operations
- **Connection Pooling**: Optimised connection management
- **Migrations**: Schema version control
- **Backup System**: Automated data protection

### Configuration
```typescript
// Prisma configuration
datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("POSTGRES_URL_NON_POOLING")
}
```

## Database Schema

### Core Models

#### SystemSettings
System-wide configuration parameters:
```prisma
model SystemSettings {
  id           Int      @id @default(autoincrement())
  settingKey   String   @unique @map("setting_key") @db.VarChar(50)
  settingValue String?  @map("setting_value")
  description  String?
  updatedAt    DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("system_settings")
}
```

#### CensusYear
Census year tracking and management:
```prisma
model CensusYear {
  id                     Int                      @id @default(autoincrement())
  year                   Int                      @unique
  isActive               Boolean                  @default(false) @map("is_active")
  createdAt              DateTime                 @default(now()) @map("created_at")
  updatedAt              DateTime                 @default(now()) @updatedAt @map("updated_at")
  
  // Relationships
  censusForms            CensusForm[]
  householdsFirst        Household[]              @relation("FirstCensusYear")
  householdsLast         Household[]              @relation("LastCensusYear")
  memberHouseholdHistory MemberHouseholdHistory[]
  sacraments             Sacrament[]
  uniqueCodes            UniqueCode[]

  @@map("census_years")
}
```

#### Household
Household information and location data:
```prisma
model Household {
  id                Int                      @id @default(autoincrement())
  suburb            String                   @db.VarChar(150)
  firstCensusYearId Int                      @map("first_census_year_id")
  lastCensusYearId  Int                      @map("last_census_year_id")
  createdAt         DateTime                 @default(now()) @map("created_at")
  updatedAt         DateTime                 @default(now()) @updatedAt @map("updated_at")
  
  // Relationships
  censusForms       CensusForm[]
  householdMembers  HouseholdMember[]
  uniqueCodes       UniqueCode[]
  firstCensusYear   CensusYear               @relation("FirstCensusYear", fields: [firstCensusYearId], references: [id])
  lastCensusYear    CensusYear               @relation("LastCensusYear", fields: [lastCensusYearId], references: [id])

  @@map("households")
}
```

#### Member
Individual member information:
```prisma
model Member {
  id                     Int                      @id @default(autoincrement())
  firstName              String                   @map("first_name") @db.VarChar(50)
  lastName               String                   @map("last_name") @db.VarChar(50)
  dateOfBirth            DateTime?                @map("date_of_birth") @db.Date
  gender                 Gender
  mobilePhone            String                   @map("mobile_phone") @db.VarChar(10)
  hobby                  String?                  @db.VarChar(100)
  createdAt              DateTime                 @default(now()) @map("created_at")
  updatedAt              DateTime                 @default(now()) @updatedAt @map("updated_at")
  
  // Relationships
  householdMembers       HouseholdMember[]
  memberHouseholdHistory MemberHouseholdHistory[]
  sacraments             Sacrament[]

  @@map("members")
}
```

#### UniqueCode
Secure access codes with variable validation range security system:
```prisma
model UniqueCode {
  id              Int        @id @default(autoincrement())
  code            String     @unique @db.VarChar(25)
  isAssigned      Boolean    @default(false) @map("is_assigned")
  householdId     Int?       @map("household_id")
  censusYearId    Int        @map("census_year_id")
  assignedAt      DateTime?  @map("assigned_at")

  // Variable Validation Range Security Fields (0-based indexing)
  validationStart Int        @map("validation_start")  // Start position for validation (0-based)
  validationEnd   Int        @map("validation_end")    // End position for validation (0-based)
  validationHash  String     @db.VarChar(64) @map("validation_hash") // SHA-256 hash

  createdAt       DateTime   @default(now()) @map("created_at")
  updatedAt       DateTime   @default(now()) @updatedAt @map("updated_at")

  // Relationships
  household       Household? @relation(fields: [householdId], references: [id])
  censusYear      CensusYear @relation(fields: [censusYearId], references: [id])

  @@map("unique_codes")
}
```

**Security Enhancement Features:**
- **Variable Validation Ranges**: Each code validates 8-14 characters from positions 8-22
- **Cryptographic Hashing**: SHA-256 hash of validation portion stored securely
- **Decoy Characters**: 40-60% of visible characters are meaningless to attackers
- **Attack Resistance**: Systematic enumeration becomes computationally infeasible
- **Pattern Obfuscation**: Validation logic hidden from potential attackers
```

### Authentication Models

#### Admin
Administrator user accounts:
```prisma
model Admin {
  id                Int              @id @default(autoincrement())
  username          String           @unique @db.VarChar(50)
  email             String           @unique @db.VarChar(100)
  passwordHash      String           @map("password_hash") @db.VarChar(255)
  fullName          String?          @map("full_name") @db.VarChar(100)
  isActive          Boolean          @default(true) @map("is_active")
  lastLoginAt       DateTime?        @map("last_login_at")
  createdAt         DateTime         @default(now()) @map("created_at")
  updatedAt         DateTime         @default(now()) @updatedAt @map("updated_at")
  
  // Relationships
  auditLogs         AuditLog[]
  twoFactorAuth     TwoFactorAuth?
  userSessions      UserSession[]

  @@map("admins")
}
```

#### TwoFactorAuth
Two-factor authentication settings:
```prisma
model TwoFactorAuth {
  id           Int      @id @default(autoincrement())
  adminId      Int      @unique @map("admin_id")
  secret       String   @db.VarChar(32)
  backupCodes  String[] @map("backup_codes")
  isEnabled    Boolean  @default(false) @map("is_enabled")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @default(now()) @updatedAt @map("updated_at")
  
  // Relationships
  admin        Admin    @relation(fields: [adminId], references: [id], onDelete: Cascade)

  @@map("two_factor_auth")
}
```

#### AuthRateLimit
Rate limiting for authentication attempts:
```prisma
model AuthRateLimit {
  id                 Int       @id @default(autoincrement())
  sessionToken       String    @unique @map("session_token") @db.VarChar(64)
  failedAttempts     Int       @default(0) @map("failed_attempts")
  lockoutUntil       DateTime? @map("lockout_until")
  escalationLevel    Int       @default(0) @map("escalation_level")
  lastFailedAttempt  DateTime? @map("last_failed_attempt")
  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @default(now()) @updatedAt @map("updated_at")

  @@index([lockoutUntil])
  @@index([createdAt])
  @@index([sessionToken, lockoutUntil])
  @@map("auth_rate_limits")
}
```

### Relationship Models

#### HouseholdMember
Many-to-many relationship between households and members:
```prisma
model HouseholdMember {
  id           Int                @id @default(autoincrement())
  householdId  Int                @map("household_id")
  memberId     Int                @map("member_id")
  relationship HouseholdRelation
  createdAt    DateTime           @default(now()) @map("created_at")
  updatedAt    DateTime           @default(now()) @updatedAt @map("updated_at")
  
  // Relationships
  household    Household          @relation(fields: [householdId], references: [id], onDelete: Cascade)
  member       Member             @relation(fields: [memberId], references: [id], onDelete: Cascade)

  @@unique([householdId, memberId])
  @@map("household_members")
}
```

#### Sacrament
Sacrament records for members:
```prisma
model Sacrament {
  id            Int            @id @default(autoincrement())
  memberId      Int            @map("member_id")
  sacramentType SacramentType  @map("sacrament_type")
  date          DateTime?      @db.Date
  location      String?        @db.VarChar(100)
  censusYearId  Int            @map("census_year_id")
  createdAt     DateTime       @default(now()) @map("created_at")
  updatedAt     DateTime       @default(now()) @updatedAt @map("updated_at")
  
  // Relationships
  member        Member         @relation(fields: [memberId], references: [id], onDelete: Cascade)
  censusYear    CensusYear     @relation(fields: [censusYearId], references: [id])

  @@map("sacraments")
}
```

## Enums

### Gender
```prisma
enum Gender {
  male
  female
  other
}
```

### HouseholdRelation
```prisma
enum HouseholdRelation {
  head
  spouse
  child
  parent
  sibling
  relative
  other
}
```

### SacramentType
```prisma
enum SacramentType {
  baptism
  confirmation
  first_communion
  marriage
  holy_orders
}
```

## Database Indexes

### Performance Optimization

#### UniqueCode Table Indexes
```sql
-- Primary key on id (automatically indexed)
-- Unique constraint on code (automatically indexed)
CREATE INDEX idx_unique_codes_census_year ON unique_codes(census_year_id);
CREATE INDEX idx_unique_codes_assigned ON unique_codes(is_assigned);
CREATE INDEX idx_unique_codes_composite ON unique_codes(census_year_id, is_assigned);

-- Security validation indexes for performance
CREATE INDEX idx_unique_codes_validation_hash ON unique_codes(validation_hash);
CREATE INDEX idx_unique_codes_validation_range ON unique_codes(validation_start, validation_end);
```

**Index Performance Benefits:**
- **Code Lookup**: Unique constraint ensures O(1) code validation
- **Census Year Filtering**: Fast filtering by census year for admin operations
- **Assignment Status**: Quick queries for assigned/unassigned codes
- **Validation Hash**: Optimized cryptographic validation lookups
- **Validation Range**: Efficient range-based security queries

#### AuthRateLimit Table Indexes
```sql
CREATE INDEX idx_lockout_until ON auth_rate_limits(lockout_until);
CREATE INDEX idx_cleanup ON auth_rate_limits(created_at);
CREATE INDEX idx_active_lockouts ON auth_rate_limits(session_token, lockout_until);
```

## Connection Management

### Prisma Client Configuration
```typescript
export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : ['error'],
  errorFormat: 'pretty',
});
```

### Connection Pooling
- **Development**: 20 connections, 30 queue limit
- **Production**: 50 connections, 200 queue limit
- **Idle timeout**: 60s (dev) / 30s (prod)
- **Connect timeout**: 20s (dev) / 30s (prod)

## Data Migration

### Prisma Migrations
```bash
# Generate migration
npm run db:migrate

# Apply migrations
npm run db:push

# Generate Prisma client
npm run db:generate
```

### Schema Updates
1. Update `prisma/schema.prisma`
2. Generate migration: `prisma migrate dev`
3. Apply to production: `prisma migrate deploy`
4. Regenerate client: `prisma generate`

## Backup and Export

### Database Export
The system supports multiple export formats:

#### CSV Export
- Quoted fields for safety
- Proper escape handling
- Deterministic header ordering
- CSV injection protection

#### JSON Export
- Structured data format
- Nested relationships
- Type preservation
- Compression support

#### SQL Export
- Complete schema and data
- Restore-ready format
- Transaction safety
- Foreign key constraints

### Backup Strategy
```typescript
// Automated backup configuration
const backupConfig = {
  schedule: '0 2 * * *', // Daily at 2 AM
  retention: 30,         // Keep 30 days
  compression: true,     // Compress backups
  encryption: true,      // Encrypt sensitive data
};
```

## Query Optimization

### Best Practises

#### Efficient Queries
```typescript
// Good: Select specific fields
const members = await prisma.member.findMany({
  select: {
    id: true,
    firstName: true,
    lastName: true,
  },
  where: { /* conditions */ },
});

// Avoid: Select all fields
const members = await prisma.member.findMany();
```

#### Relationship Loading
```typescript
// Efficient relationship loading
const households = await prisma.household.findMany({
  include: {
    householdMembers: {
      include: {
        member: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
      },
    },
  },
});
```

### Performance Monitoring
- Query execution time tracking
- Connection pool monitoring
- Index usage analysis
- Slow query identification

This comprehensive database guide ensures efficient, secure, and maintainable data management for the WSCCC Census System.
