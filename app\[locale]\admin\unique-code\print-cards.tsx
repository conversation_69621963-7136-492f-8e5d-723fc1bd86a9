"use client";

import { <PERSON><PERSON><PERSON>ir<PERSON>, Printer } from "lucide-react";
import Image from "next/image";
import { useTranslations } from "next-intl";
import { useCallback, useEffect, useState } from "react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { generateBatchQRCodes } from "@/lib/utils/qr-code";
import { getSiteUrl } from "@/lib/utils/site-url";

interface UniqueCode {
	id: number;
	code: string;
	isAssigned: boolean;
	assignedAt: string | null;
	householdId: number | null;
	censusYearId: number;
	createdAt: string;
	updatedAt: string;
	census_year: number;
	is_active_year: boolean;
}

export default function PrintCardsPage() {
	const t = useTranslations("admin");
	const tCommon = useTranslations("common");
	const tErrors = useTranslations("errors");
	const tCensus = useTranslations("census");
	const [codes, setCodes] = useState<UniqueCode[]>([]);
	const [qrCodes, setQrCodes] = useState<Record<string, string>>({});
	const [churchName, setChurchName] = useState("WSCCC Church");
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	// Generate QR codes for each unique code
	const generateQrCodes = useCallback(async (codesToProcess: UniqueCode[]) => {
		try {
			// Get the site URL from settings or fall back to default
			const baseUrl = await getSiteUrl();

			// Use the new batch QR code generation utility with size 128 (32 in Tailwind = 8rem = 128px)
			return await generateBatchQRCodes(codesToProcess, baseUrl, 128);
		} catch (_error) {
			return {};
		}
	}, []);

	// Fetch church name
	const fetchChurchName = useCallback(async () => {
		try {
			const response = await fetch("/api/settings/church-info");
			if (response.ok) {
				const data = await response.json();
				return data.churchName || "WSCCC Church";
			}
			return "WSCCC Church";
		} catch (_error) {
			if (process.env.NODE_ENV === "development") {
			}
			return "WSCCC Church";
		}
	}, []);

	useEffect(() => {
		// Check if we're in a browser environment
		if (typeof window === "undefined") {
			return;
		}

		const fetchCodes = async () => {
			try {
				// Fetch codes using JWT token approach
				if (process.env.NODE_ENV === "development") {
				}
				setLoading(true);

				const response = await fetch("/api/unique-code/print-session");

				// Log the response status
				if (process.env.NODE_ENV === "development") {
				}

				if (!response.ok) {
					// Handle error case - likely a page refresh or direct access
					let errorMessage = tErrors("noCodesFoundDetails");

					try {
						const errorData = await response.json();
						if (process.env.NODE_ENV === "development") {
						}
						if (errorData.error && errorData.details) {
							errorMessage = errorData.details;
						}
					} catch {
						const _errorText = await response.text();
						if (process.env.NODE_ENV === "development") {
						}
					}

					// Show error message in the UI
					setError(errorMessage);
					setLoading(false);
					return;
				}

				const data = await response.json();

				if (
					data.success &&
					data.data &&
					Array.isArray(data.data) &&
					data.data.length > 0
				) {
					// Set the codes first
					setCodes(data.data);

					// Then fetch church name and generate QR codes
					const [churchNameResult, qrCodesResult] = await Promise.all([
						fetchChurchName(),
						generateQrCodes(data.data),
					]);

					setChurchName(churchNameResult);
					setQrCodes(qrCodesResult);
					setLoading(false);
				} else {
					setError(tErrors("noCodesFoundPrintError"));
					setLoading(false);
				}
			} catch (_error) {
				setError(tErrors("printCardsLoadError"));
				setLoading(false);
			}
		};

		fetchCodes();
	}, [tErrors, fetchChurchName, generateQrCodes]);

	const handlePrint = () => {
		window.print();
	};

	if (loading) {
		return (
			<div className="space-y-6 p-8">
				{/* Header skeleton */}
				<div className="mb-8 flex items-center justify-between">
					<Skeleton className="h-8 w-64" />
					<div className="flex gap-2">
						<Skeleton className="h-10 w-32" />
						<Skeleton className="h-10 w-24" />
					</div>
				</div>

				{/* Instructions skeleton */}
				<div className="mb-8 space-y-4">
					<Skeleton className="h-24 w-full" />
					<div className="space-y-2">
						<Skeleton className="h-5 w-full" />
						<Skeleton className="h-5 w-4/5" />
						<Skeleton className="h-5 w-5/6" />
					</div>
				</div>

				{/* Cards grid skeleton - 3x3 layout */}
				<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
					{[...new Array(9)].map((_, index) => (
						<div
							className="flex flex-col items-center border border-gray-200 border-dashed p-4"
							key={index}
						>
							<Skeleton className="mb-2 h-6 w-32" />
							<Skeleton className="mb-4 h-4 w-24" />
							<Skeleton className="mb-4 h-32 w-32" />
							<Skeleton className="mb-1 h-4 w-40" />
							<Skeleton className="mb-2 h-6 w-32" />
							<Skeleton className="h-3 w-48" />
						</div>
					))}
				</div>
			</div>
		);
	}

	if (error || codes.length === 0) {
		return (
			<div className="flex min-h-screen flex-col items-center justify-center gap-4">
				<Alert className="max-w-md" variant="destructive">
					<AlertCircle className="h-4 w-4" />
					<AlertTitle>{t("accessError")}</AlertTitle>
					<AlertDescription>
						{error || t("noCodesFoundToPrint")}
					</AlertDescription>
				</Alert>

				<p className="mb-4 max-w-md text-center text-muted-foreground text-sm">
					{error ? t("printSessionExpired") : t("directAccessNotSupported")}
				</p>

				<div className="flex gap-2">
					<Button onClick={() => (window.location.href = "/admin/unique-code")}>
						{t("goToUniqueCodeManagement")}
					</Button>
					<Button onClick={() => window.close()} variant="outline">
						{tCommon("close")}
					</Button>
				</div>
			</div>
		);
	}

	return (
		<div className="p-8 print:p-0">
			{/* Print button - hidden when printing */}
			<div className="mb-8 flex justify-between print:hidden">
				<h1 className="font-bold text-2xl">{t("printUniqueCodeCards")}</h1>
				<div className="flex gap-2">
					<Button onClick={handlePrint}>
						<Printer className="mr-2 h-4 w-4" />
						{tCommon("printCards")}
					</Button>
					<Button onClick={() => window.close()} variant="outline">
						{tCommon("close")}
					</Button>
				</div>
			</div>

			{/* Instructions - hidden when printing */}
			<div className="mb-8 space-y-4 print:hidden">
				<Alert>
					<AlertCircle className="h-4 w-4" />
					<AlertTitle>{t("printInstructions")}</AlertTitle>
					<AlertDescription>
						{t("printInstructionsDescription", {
							count: codes.length.toString(),
							pages: Math.ceil(codes.length / 9).toString(),
						})}
					</AlertDescription>
				</Alert>

				<div className="space-y-2">
					<p>{t("printInstructionsText")}</p>
					<p className="text-muted-foreground text-sm">
						<strong>{t("printingTips")}:</strong> {t("printingTipsDescription")}
					</p>
				</div>
			</div>

			{/* Cards grid - 3x3 layout */}
			<div className="grid grid-cols-1 gap-0 md:grid-cols-3 print:grid-cols-3 print:gap-0">
				{codes.map((code) => (
					<div
						className="page-break-inside-avoid relative flex h-[9.9cm] flex-col items-center justify-center border border-gray-300 border-dashed p-4 print:border-gray-400"
						key={code.id}
					>
						{/* Church name and ID */}
						<div className="mb-2 text-center">
							<div className="mb-1 flex items-center justify-between">
								<div className="rounded bg-muted/30 px-2 py-0.5 font-medium text-xs">
									#{code.id}
								</div>
								<div className="text-muted-foreground text-xs">
									{t("censusFormat", { year: code.census_year.toString() })}
								</div>
							</div>
							<h2 className="font-bold text-lg">{churchName}</h2>
						</div>

						{/* QR Code */}
						<div className="mb-4 rounded-md border border-gray-200 bg-white p-2">
							{qrCodes[code.code] ? (
								<Image
									alt={tCommon("qrCodeFor", { code: code.code })}
									className="h-32 w-32"
									height={128}
									src={qrCodes[code.code]}
									width={128}
								/>
							) : (
								<div className="flex h-32 w-32 items-center justify-center bg-gray-100">
									<p className="text-gray-500 text-sm">
										{tCommon("qrCodeError")}
									</p>
								</div>
							)}
						</div>

						{/* Unique Code */}
						<div className="text-center">
							<p className="mb-1 text-muted-foreground text-sm">
								{tCensus("yourUniqueCodeIs")}
							</p>
							<p className="font-bold font-mono text-lg">{code.code}</p>
						</div>

						{/* Instructions */}
						<div className="mt-2 text-center">
							<p className="text-muted-foreground text-xs">
								{tCommon("scanThisQrCodeToAccessTheCensus")}
							</p>
						</div>

						{/* Cut marks - visible when printing */}
						<div className="absolute top-0 left-0 h-3 w-3 border-gray-400 border-t border-l" />
						<div className="absolute top-0 right-0 h-3 w-3 border-gray-400 border-t border-r" />
						<div className="absolute bottom-0 left-0 h-3 w-3 border-gray-400 border-b border-l" />
						<div className="absolute right-0 bottom-0 h-3 w-3 border-gray-400 border-r border-b" />
					</div>
				))}
			</div>

			{/* Print-specific styles */}
			<style global jsx>{`
        @media print {
          @page {
            size: A4;
            margin: 0;
          }
          body {
            margin: 0;
            padding: 0;
          }
          .page-break-inside-avoid {
            page-break-inside: avoid;
          }
          /* Ensure each card has the correct dimensions for A4 paper (3x3 grid) */
          .grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            width: 210mm;
          }
          /* Each card is approximately 70mm x 99mm */
          .grid > div {
            width: 70mm;
            height: 99mm;
            box-sizing: border-box;
            break-inside: avoid;
          }
          /* Add page breaks after every 9 cards (3 rows of 3) */
          .grid > div:nth-child(9n) {
            page-break-after: always;
          }
        }
      `}</style>
		</div>
	);
}
