"use client";

import { <PERSON><PERSON><PERSON>, UserCog } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod/v4";
import { FormFieldWithTooltip } from "@/components/admin/settings/FormFieldWithTooltip";
import { SettingsCard } from "@/components/admin/settings/SettingsCard";
import { CustomDateTimePicker } from "@/components/ui/custom-datetime-picker";
import { StatusBadge } from "@/components/ui/status-badge";
import { Switch } from "@/components/ui/switch";
import { useFormSubmit } from "@/hooks/useFormSubmit";
import { useMessage } from "@/hooks/useMessage";
import {
  createDateSchema,
  dateRangeRefinement,
  formatForDatabase,
  formatInSydneyTime,
} from "@/lib/utils/date-time";
import { zodResolver } from "@/lib/utils/zod-resolver-compat";

// Define the schema for census controls
const censusControlsSchema = z
  .object({
    isOpen: z.boolean(),
    autoOpenClose: z.boolean(),
    startDate: createDateSchema(),
    endDate: createDateSchema(),
    manualOverride: z.boolean(),
  })
  .superRefine((data, ctx) => {
    const refinement = dateRangeRefinement(
      "startDate",
      "endDate",
      "End date must be after start date",
      "endDate",
    );
    if (!refinement.validation(data)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: refinement.message,
        path: refinement.path,
      });
    }
  });

type CensusControlsFormValues = z.infer<typeof censusControlsSchema>;

export function CensusControlsSettings() {
  const t = useTranslations("admin");
  const tValidation = useTranslations("validation");
  const tErrors = useTranslations("errors");
  const { showSuccess, showError, showWarning } = useMessage();

  // Add state for tracking override status and scheduled state
  const [isOverride, setIsOverride] = useState(false);
  const [scheduledState, setScheduledState] = useState<boolean | null>(null);
  const [nextChangeTime, setNextChangeTime] = useState<Date | null>(null);

  // Initialize form with validation
  const form = useForm<CensusControlsFormValues>({
    resolver: zodResolver(censusControlsSchema), // Zod resolver is properly typed
    defaultValues: {
      isOpen: false,
      autoOpenClose: false,
      startDate: null,
      endDate: null,
      manualOverride: false,
    },
  });

  // Watch form values for UI updates
  const isOpen = form.watch("isOpen");
  const autoOpenClose = form.watch("autoOpenClose");
  const startDate = form.watch("startDate");
  const endDate = form.watch("endDate");

  // Fetch census controls settings from the API
  useEffect(() => {
    const fetchCensusControlsSettings = async () => {
      try {
        const response = await fetch("/api/admin/census-controls");

        // Handle API errors
        if (!response.ok) {
          if (process.env.NODE_ENV === "development") {
            console.warn(`API call failed with status ${response.status}`);
          }

          // If unauthorized, it means we're not logged in
          if (response.status === 401) {
            if (process.env.NODE_ENV === "development") {
              console.warn("Not logged in as admin");
            }
            showWarning("needAdminLoginToViewSettings");
          } else {
            showError("failedToLoadCensusControlsSettings");
          }

          // Set placeholder values
          form.reset({
            isOpen: false,
            autoOpenClose: false,
            startDate: null,
            endDate: null,
            manualOverride: false,
          });
          setIsOverride(false);
          setScheduledState(null);
          setNextChangeTime(null);
          return;
        }

        const data = await response.json();

        // Update form values
        form.reset({
          isOpen: data.isOpen,
          autoOpenClose: data.autoOpenClose,
          startDate: data.startDate ? new Date(data.startDate) : null,
          endDate: data.endDate ? new Date(data.endDate) : null,
          manualOverride: data.manualOverride,
        });

        // Set override status and scheduled state
        setIsOverride(data.manualOverride);
        setScheduledState(data.scheduledState);
        setNextChangeTime(data.nextChangeTime ? new Date(data.nextChangeTime) : null);
      } catch (error) {
        console.error("Error fetching census controls settings:", error);
        // For development purposes, use default values if the API call fails
        form.reset({
          isOpen: false,
          autoOpenClose: false,
          startDate: null,
          endDate: null,
          manualOverride: false,
        });
        setIsOverride(false);
        setScheduledState(null);
        setNextChangeTime(null);
        showWarning("usingDefaultValuesDatabaseUnavailable");
      }
    };

    fetchCensusControlsSettings();
  }, [form, showWarning, showError, t]);

  // Use our custom hook for form submission
  const { handleSubmit: submitCensusControls, isSubmitting } =
    useFormSubmit<CensusControlsFormValues>({
      onSubmit: async (data) => {
        try {
          // If autoOpenClose is enabled and isOpen differs from scheduledState, this is a manual override
          let manualOverride = data.manualOverride;
          if (data.autoOpenClose && scheduledState !== null && data.isOpen !== scheduledState) {
            manualOverride = true;
          } else if (
            data.autoOpenClose &&
            scheduledState !== null &&
            data.isOpen === scheduledState
          ) {
            manualOverride = false;
          }

          // Convert dates to ISO strings for API
          const apiData = {
            ...data,
            manualOverride,
            startDate: data.startDate ? formatForDatabase(data.startDate) : null,
            endDate: data.endDate ? formatForDatabase(data.endDate) : null,
          };

          // Add a timeout to the fetch request
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 10_000); // 10 second timeout

          let response;
          let responseData;

          try {
            response = await fetch("/api/admin/census-controls", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify(apiData),
              signal: controller.signal,
            });

            clearTimeout(timeoutId);

            // Check if the response is valid before trying to parse JSON
            if (!response.ok && response.status !== 400) {
              throw new Error(
                tErrors("serverRespondedWithStatusRespo", {
                  status: response.status.toString(),
                }),
              );
            }

            responseData = await response.json();
          } catch (fetchError) {
            clearTimeout(timeoutId);
            console.error("Fetch error:", fetchError);

            // Handle network errors
            if (
              fetchError &&
              typeof fetchError === "object" &&
              "name" in fetchError &&
              fetchError.name === "AbortError"
            ) {
              throw new Error(t("requestTimedOut"));
            }

            if (
              fetchError &&
              typeof fetchError === "object" &&
              "message" in fetchError &&
              typeof fetchError.message === "string" &&
              fetchError.message.includes("Failed to fetch")
            ) {
              throw new Error(t("networkErrorCheckConnection"));
            }

            throw fetchError;
          }

          // Now we can safely use response and responseData
          if (!response.ok) {
            // If unauthorized, return a specific message
            if (response.status === 401) {
              return {
                success: false,
                message: t("needAdminLoginToSaveSettings"),
              };
            }

            if (responseData.details) {
              // Handle validation errors
              showError("InvalidInput", "settings");
              return {
                success: false,
                message: tValidation("checkFormErrors"),
              };
            }

            showError("UpdateFailed", "settings");
            return {
              success: false,
              message: t("failedToUpdateCensusControls"),
            };
          }

          // Check if this is a development message about database connection
          if (
            responseData.message &&
            responseData.message.includes("would be saved in production")
          ) {
            return {
              success: true,
              message: responseData.message,
            };
          }

          // Success - use centralized alert system
          showSuccess("censusControlsUpdated");
          return {
            success: true,
            suppressAlert: true,
          };
        } catch (error: unknown) {
          console.error("Error updating census controls:", error);

          if (error instanceof Error && error.message === "ValidationError") {
            showError("InvalidInput", "settings");
          } else {
            showError("UpdateFailed", "settings");
          }

          return {
            success: false,
            suppressAlert: true,
          };
        }
      },
      onSuccess: () => {
        // Trigger immediate update of census status indicator in header
        window.dispatchEvent(new CustomEvent("census-status-changed"));
      },
    });

  // Create a status indicator that shows override status
  const statusIndicator = (
    <div className="flex items-center gap-2">
      <StatusBadge variant={isOpen ? "success" : "danger"}>
        {isOpen ? t("censusOpen") : t("censusClosed")}
      </StatusBadge>
      {isOverride && (
        <div className="flex items-center text-muted-foreground text-xs">
          <UserCog className="mr-1 h-3 w-3" />
          <span>{t("manualOverride")}</span>
        </div>
      )}
      {!isOverride && autoOpenClose && (
        <div className="flex items-center text-muted-foreground text-xs">
          <CalendarClock className="mr-1 h-3 w-3" />
          <span>{t("scheduled")}</span>
        </div>
      )}
    </div>
  );

  // Add a section to show scheduled state and next scheduled change
  const scheduleInfo = autoOpenClose && (
    <div className="mt-2 space-y-1 text-sm">
      {scheduledState !== null && (
        <div className="flex items-center gap-2">
          <span className="text-muted-foreground">{t("scheduledState")}:</span>
          <StatusBadge className="text-xs" variant={scheduledState ? "success" : "danger"}>
            {scheduledState ? t("censusOpen") : t("censusClosed")}
          </StatusBadge>
          {isOverride && scheduledState !== isOpen && (
            <span className="text-amber-500 text-xs">({t("manualOverrideActive")})</span>
          )}
        </div>
      )}
      {nextChangeTime && (
        <p className="text-muted-foreground">
          {isOverride && scheduledState !== isOpen
            ? t("manualOverrideWillReset", {
                time: formatInSydneyTime(nextChangeTime),
              })
            : t("nextScheduledChange", {
                time: formatInSydneyTime(nextChangeTime),
              })}
        </p>
      )}
    </div>
  );

  // Handle date selection with form integration
  const handleStartDateSelect = (date: Date | null) => {
    form.setValue("startDate", date, { shouldValidate: true });
  };

  const handleEndDateSelect = (date: Date | null) => {
    form.setValue("endDate", date, { shouldValidate: true });
  };

  // Handle switch changes with form integration
  const handleOpenCloseChange = (checked: boolean) => {
    form.setValue("isOpen", checked, { shouldValidate: true });

    // If auto scheduling is enabled, this is a manual override
    if (autoOpenClose && scheduledState !== null && checked !== scheduledState) {
      form.setValue("manualOverride", true);
      setIsOverride(true);
    } else if (autoOpenClose && scheduledState !== null && checked === scheduledState) {
      // If matching scheduled state, not an override
      form.setValue("manualOverride", false);
      setIsOverride(false);
    }
  };

  const handleAutoOpenCloseChange = (checked: boolean) => {
    form.setValue("autoOpenClose", checked, { shouldValidate: true });

    // If turning off auto scheduling, clear override status
    if (!checked) {
      form.setValue("manualOverride", false);
      setIsOverride(false);
    }
  };

  return (
    <SettingsCard
      description={t("controlWhenCensusAvailable")}
      form={form}
      isSubmitting={isSubmitting}
      onFormSubmit={submitCensusControls}
      statusIndicator={statusIndicator}
      title={t("censusAvailability")}
    >
      <div className="space-y-6">
        <FormFieldWithTooltip
          helperText={t("whenEnabledMembersCanAccess")}
          id="isOpen"
          label={t("censusStatus")}
          tooltip={t("enableDisableCensusAccess")}
        >
          <div className="flex items-center space-x-2">
            <Switch checked={isOpen} id="isOpen" onCheckedChange={handleOpenCloseChange} />
            <span className="font-medium text-sm">
              {isOpen ? t("censusIsOpen") : t("censusIsClosed")}
            </span>
          </div>
          {scheduleInfo}
        </FormFieldWithTooltip>

        <div className="border-t pt-4">
          <h4 className="mb-4 font-medium text-sm">{t("scheduleAvailability")}</h4>

          <FormFieldWithTooltip
            helperText={t("whenEnabledAutomaticOpenClose")}
            id="autoOpenClose"
            label={t("automaticOpenClose")}
            tooltip={t("automaticallyOpenCloseBasedOnSchedule")}
          >
            <div className="mb-4 flex items-center space-x-2">
              <Switch
                checked={autoOpenClose}
                id="autoOpenClose"
                onCheckedChange={handleAutoOpenCloseChange}
              />
              <span className="font-medium text-sm">
                {autoOpenClose ? t("automaticSchedulingEnabled") : t("automaticSchedulingDisabled")}
              </span>
            </div>
          </FormFieldWithTooltip>

          <div className="grid gap-4 sm:grid-cols-2">
            <FormFieldWithTooltip
              error={form.formState.errors.startDate}
              id="startDate"
              label={t("startDateTime")}
              tooltip={t("startDateTimeTooltip")}
            >
              <CustomDateTimePicker
                className={form.formState.errors.startDate ? "border-destructive" : ""}
                date={startDate}
                disabled={!autoOpenClose}
                placeholderText={t("selectStartDateTime")}
                setDate={handleStartDateSelect}
                showSeconds={true}
              />
            </FormFieldWithTooltip>

            <FormFieldWithTooltip
              error={form.formState.errors.endDate}
              id="endDate"
              label={t("endDateTime")}
              tooltip={t("endDateTimeTooltip")}
            >
              <CustomDateTimePicker
                className={form.formState.errors.endDate ? "border-destructive" : ""}
                date={endDate}
                disabled={!autoOpenClose}
                placeholderText={t("selectEndDateTime")}
                setDate={handleEndDateSelect}
                showSeconds={true}
              />
            </FormFieldWithTooltip>
          </div>
        </div>
      </div>
    </SettingsCard>
  );
}
