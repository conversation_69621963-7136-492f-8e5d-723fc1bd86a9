import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { z } from "zod/v4";
import { authOptions } from "@/lib/auth/auth-options";
import { clearCensusStatusCache, isCensusOpen } from "@/lib/census/census-availability";
import {
  getCensusControlsSettings,
  updateCensusControlsSettings,
} from "@/lib/db/census-controls-settings";
import { createSuccessResponse } from "@/lib/utils/api-responses";
import { isDateAfter } from "@/lib/utils/date-time";
import { getErrorMessage, getZodErrorDetails, isZodError } from "@/lib/utils/error-handling";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";

/**
 * Admin-only Census Controls API
 * This ensures system independence by providing admin-specific access to census controls
 * without shared API endpoints between admin and census systems
 */

// Validation schema for census controls
const censusControlsSchema = z.object({
  isOpen: z.boolean(),
  autoOpenClose: z.boolean(),
  startDate: z.string().nullable(),
  endDate: z.string().nullable(),
  manualOverride: z.boolean(),
});

/**
 * GET /api/admin/census-controls
 * Get current census controls settings (admin only)
 */
export async function GET() {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require admin authentication
    if (!session || session.user.role !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    try {
      // Try to get census controls settings from database
      const censusControlsSettings = await getCensusControlsSettings();

      // Get current availability status including override info
      const availabilityStatus = await isCensusOpen();

      // Transform to match the frontend model
      return NextResponse.json({
        isOpen: censusControlsSettings.systemOpen,
        autoOpenClose: censusControlsSettings.autoOpenClose,
        startDate: censusControlsSettings.censusStartDate,
        endDate: censusControlsSettings.censusEndDate,
        manualOverride: censusControlsSettings.manualOverride,
        scheduledState: availabilityStatus.scheduledState,
        nextChangeTime: availabilityStatus.nextChangeTime,
      });
    } catch (_dbError) {
      // Return default values for development
      return NextResponse.json({
        isOpen: false,
        autoOpenClose: false,
        startDate: null,
        endDate: null,
        manualOverride: false,
        scheduledState: null,
        nextChangeTime: null,
      });
    }
  } catch (_error) {
    return NextResponse.json(
      { error: "Failed to fetch census controls settings" },
      { status: 500 },
    );
  }
}

/**
 * POST /api/admin/census-controls
 * Update census controls settings (admin only)
 */
export async function POST(request: Request) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const tAdmin = await getTranslations({ locale, namespace: "admin" });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require admin authentication
    if (!session || session.user.role !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = censusControlsSchema.parse(body);

    // Additional validation for date logic
    if (validatedData.autoOpenClose && validatedData.startDate && validatedData.endDate) {
      const startDate = new Date(validatedData.startDate);
      const endDate = new Date(validatedData.endDate);

      if (isDateAfter(startDate, endDate)) {
        return NextResponse.json(
          {
            error: tAdmin("validationFailed"),
          },
          { status: 400 },
        );
      }
    }

    // Update the census controls settings
    await updateCensusControlsSettings({
      systemOpen: validatedData.isOpen,
      autoOpenClose: validatedData.autoOpenClose,
      censusStartDate: validatedData.startDate,
      censusEndDate: validatedData.endDate,
      manualOverride: validatedData.manualOverride,
    });

    // Clear the census status cache to ensure the latest settings are used
    clearCensusStatusCache();

    // Log the action to console (audit_logs table not implemented yet)
    if (process.env.NODE_ENV === "development") {
    }

    return createSuccessResponse(request, "censusControlsUpdated");
  } catch (error) {
    if (isZodError(error)) {
      const zodDetails = getZodErrorDetails(error);
      return NextResponse.json(
        {
          error: tAdmin("validationFailed"),
          details: zodDetails,
        },
        { status: 400 },
      );
    }
    return NextResponse.json(
      {
        error: tAdmin("censusControlsUpdateFailed"),
        details: getErrorMessage(error),
      },
      { status: 500 },
    );
  }
}
