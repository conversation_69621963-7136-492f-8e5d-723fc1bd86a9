import { type NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { z } from "zod/v4";
import { censusAuthOptions } from "@/lib/census-auth/census-auth-options";
import { prisma } from "@/lib/db/prisma";
import { getZodErrorDetails } from "@/lib/utils/error-handling";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";

/**
 * This endpoint updates the household_id in the unique_codes table
 * which will be reflected in the session on the next session refresh.
 * It does NOT directly modify the NextAuth session.
 */

// Schema for validating session update requests
const updateSessionSchema = z.object({
	householdId: z.string().min(1, { error: "Household ID is required" }),
	name: z.string().min(1, { error: "Name is required" }),
});

/**
 * API endpoint for updating the household ID in the unique_codes table
 * This indirectly updates the session data by modifying the database records
 * that the session is based on. The session will be updated on the next refresh.
 */
export async function POST(request: NextRequest) {
	// Get locale from centralized utility at the beginning
	const locale = await getLocaleFromCookies();
	const tErrors = await getTranslations({ locale, namespace: "errors" });
	const tAdmin = await getTranslations({ locale, namespace: "admin" });

	try {
		// Check if user is authenticated
		const session = await getServerSession(censusAuthOptions);

		if (!session) {
			return NextResponse.json(
				{
					success: false,
					error: tErrors("unauthorized"),
				},
				{ status: 401 },
			);
		}

		// Check if user has the household role
		if (session.user.role !== "household") {
			return NextResponse.json(
				{
					success: false,
					error: tErrors("forbidden"),
				},
				{ status: 403 },
			);
		}

		// Parse request body
		let body;
		try {
			body = await request.json();
		} catch (_error) {
			return NextResponse.json(
				{
					success: false,
					error: tErrors("invalidJsonInRequestBody"),
				},
				{ status: 400 },
			);
		}

		// Validate request body
		const validationResult = updateSessionSchema.safeParse(body);

		if (!validationResult.success) {
			return NextResponse.json(
				{
					success: false,
					error: tAdmin("invalidRequestData"),
					details: getZodErrorDetails(validationResult.error),
				},
				{ status: 400 },
			);
		}

		const data = validationResult.data;

		try {
			// Update the unique code record with the new household ID
			// This will be reflected in the session on the next session refresh
			await prisma.uniqueCode.update({
				where: { id: Number.parseInt(session.user.id, 10) },
				data: { householdId: Number.parseInt(data.householdId, 10) },
			});
		} catch (_dbError) {
			return NextResponse.json(
				{
					success: false,
					error: "Database error during session update",
				},
				{ status: 500 },
			);
		}

		// Return success response
		return NextResponse.json({
			success: true,
			message: tErrors("sessionUpdatedSuccessfully"),
		});
	} catch (_error) {
		return NextResponse.json(
			{
				success: false,
				error: tErrors("sessionUpdateError"),
			},
			{ status: 500 },
		);
	}
}
