import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import createIntlMiddleware from "next-intl/middleware";
import { routing } from "./src/i18n/routing";

// Create the i18n middleware
const handleI18nRouting = createIntlMiddleware(routing);

// Security constants
const STATIC_PATHS = [
  "/_next",
  "/_vercel",
  "/favicon.ico",
  "/robots.txt",
  "/sitemap.xml",
  "/manifest.json",
] as const;

const PUBLIC_API_PATHS = [
  "/api/auth",
  "/api/census/auth",
  "/api/census/status",
  "/api/health",
  "/api/hcaptcha",
] as const;

const PUBLIC_PAGE_PATHS = [
  "/admin/login",
  "/census/auth",
  "/privacy-policy",
  "/terms",
  "/help",
] as const;

/**
 * Professional Next.js middleware with dual authentication system
 * Handles i18n routing, API authentication, and page protection
 * Follows enterprise security best practices
 */
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  try {
    // 1. Handle static assets first (highest priority, no processing needed)
    if (isStaticAsset(pathname)) {
      return NextResponse.next();
    }

    // 2. Handle API routes (no i18n processing needed)
    if (pathname.startsWith("/api")) {
      return await handleApiAuthentication(request);
    }

    // 3. Handle i18n routing for page routes
    const response = handleI18nRouting(request);

    // 4. Skip auth checks for public pages
    if (isPublicPage(pathname)) {
      return response;
    }

    // 5. Handle page route authentication
    return await handlePageAuthentication(request, response);
  } catch (error) {
    // Log error in development only for security
    if (process.env.NODE_ENV === "development") {
      console.error("Middleware error:", error);
    }

    // Return safe fallback response
    return NextResponse.redirect(new URL("/", request.url));
  }
}

/**
 * Helper function to check if a path is a static asset
 */
function isStaticAsset(pathname: string): boolean {
  return (
    STATIC_PATHS.some((path) => pathname.startsWith(path)) ||
    (pathname.includes(".") && !pathname.includes("/api/"))
  );
}

/**
 * Helper function to check if a path is public (no auth required)
 */
function isPublicPage(pathname: string): boolean {
  return PUBLIC_PAGE_PATHS.some((path) => pathname.startsWith(path));
}

/**
 * Helper function to check if an API path is public
 */
function isPublicApiPath(pathname: string): boolean {
  return PUBLIC_API_PATHS.some((path) => pathname.startsWith(path));
}

/**
 * Secure API route authentication handler
 * Implements dual authentication system with proper error handling
 */
async function handleApiAuthentication(request: NextRequest): Promise<NextResponse> {
  const { pathname } = request.nextUrl;

  try {
    // Allow public API routes
    if (isPublicApiPath(pathname)) {
      return NextResponse.next();
    }

    // Admin API routes authentication
    if (
      pathname.startsWith("/api/admin") ||
      pathname.startsWith("/api/census-years") ||
      pathname.startsWith("/api/settings") ||
      pathname.startsWith("/api/unique-code") ||
      pathname.startsWith("/api/database")
    ) {
      return await authenticateAdminApi(request);
    }

    // Census API routes authentication (excluding auth and public status)
    if (
      pathname.startsWith("/api/census") &&
      !pathname.startsWith("/api/census/auth") &&
      pathname !== "/api/census/status"
    ) {
      return await authenticateCensusApi(request);
    }

    // Allow other API routes to pass through
    return NextResponse.next();
  } catch (error) {
    // Log error securely
    if (process.env.NODE_ENV === "development") {
      console.error("API authentication error:", error);
    }

    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

/**
 * Admin API authentication with enhanced security
 */
async function authenticateAdminApi(request: NextRequest): Promise<NextResponse> {
  try {
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET_ADMIN,
      secureCookie: process.env.NODE_ENV === "production",
      cookieName: "admin-session-token",
    });

    if (!token) {
      return NextResponse.json(
        { error: "Unauthorized", message: "Authentication required" },
        {
          status: 401,
          headers: {
            "WWW-Authenticate": 'Bearer realm="Admin API"',
          },
        },
      );
    }

    if (token.role !== "admin") {
      return NextResponse.json(
        { error: "Forbidden", message: "Admin access required" },
        { status: 403 },
      );
    }

    // Authentication successful, continue to API route
    return NextResponse.next();
  } catch (error) {
    if (process.env.NODE_ENV === "development") {
      console.error("Admin API auth error:", error);
    }

    return NextResponse.json({ error: "Authentication failed" }, { status: 401 });
  }
}

/**
 * Census API authentication with account deletion checks
 */
async function authenticateCensusApi(request: NextRequest): Promise<NextResponse> {
  try {
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET_CENSUS,
      secureCookie: process.env.NODE_ENV === "production",
      cookieName: "census-session-token",
    });

    if (!token) {
      return NextResponse.json(
        { error: "Unauthorized", message: "Census authentication required" },
        {
          status: 401,
          headers: {
            "WWW-Authenticate": 'Bearer realm="Census API"',
          },
        },
      );
    }

    // Check for deleted accounts (security feature)
    if (token.accountDeleted) {
      return NextResponse.json(
        {
          error: "Account deleted",
          message: "Your household has been removed by an administrator",
        },
        { status: 403 },
      );
    }

    if (token.role !== "household") {
      return NextResponse.json(
        { error: "Forbidden", message: "Household access required" },
        { status: 403 },
      );
    }

    // Authentication successful, continue to API route
    return NextResponse.next();
  } catch (error) {
    if (process.env.NODE_ENV === "development") {
      console.error("Census API auth error:", error);
    }

    return NextResponse.json({ error: "Authentication failed" }, { status: 401 });
  }
}

/**
 * Secure page route authentication handler
 * Implements dual authentication system for admin and census pages
 */
async function handlePageAuthentication(
  request: NextRequest,
  response: NextResponse,
): Promise<NextResponse> {
  const { pathname } = request.nextUrl;

  try {
    // Admin page routes authentication
    if (pathname.startsWith("/admin") && pathname !== "/admin/login") {
      const authResult = await authenticateAdminPage(request);
      // If authentication failed, return the redirect response
      if (authResult.status !== 200) {
        return authResult;
      }
      // Authentication successful, return the i18n response
      return response;
    }

    // Census page routes authentication
    if (pathname.startsWith("/census") && !pathname.startsWith("/census/auth")) {
      const authResult = await authenticateCensusPage(request);
      // If authentication failed, return the redirect response
      if (authResult.status !== 200) {
        return authResult;
      }
      // Authentication successful, return the i18n response
      return response;
    }

    // No authentication required, return the i18n response
    return response;
  } catch (error) {
    if (process.env.NODE_ENV === "development") {
      console.error("Page authentication error:", error);
    }

    // Safe fallback - redirect to home
    return NextResponse.redirect(new URL("/", request.url));
  }
}

/**
 * Admin page authentication with secure redirects
 */
async function authenticateAdminPage(request: NextRequest): Promise<NextResponse> {
  try {
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET_ADMIN,
      secureCookie: process.env.NODE_ENV === "production",
      cookieName: "admin-session-token",
    });

    if (!token) {
      return NextResponse.redirect(
        new URL(
          "/api/auth/toast-redirect?reason=unauthenticated&redirectTo=/admin/login",
          request.url,
        ),
      );
    }

    if (token.role !== "admin") {
      return NextResponse.redirect(
        new URL(
          "/api/auth/toast-redirect?reason=unauthorized&redirectTo=/admin/login",
          request.url,
        ),
      );
    }

    // Authentication successful, return success response
    return new NextResponse(null, { status: 200 });
  } catch (error) {
    if (process.env.NODE_ENV === "development") {
      console.error("Admin page auth error:", error);
    }

    return NextResponse.redirect(
      new URL("/api/auth/toast-redirect?reason=error&redirectTo=/admin/login", request.url),
    );
  }
}

/**
 * Census page authentication with account deletion checks
 */
async function authenticateCensusPage(request: NextRequest): Promise<NextResponse> {
  try {
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET_CENSUS,
      secureCookie: process.env.NODE_ENV === "production",
      cookieName: "census-session-token",
    });

    if (!token) {
      return NextResponse.redirect(
        new URL("/api/census/auth/toast-redirect?reason=unauthenticated&redirectTo=/", request.url),
      );
    }

    // Check for deleted accounts (security feature)
    if (token.accountDeleted) {
      return NextResponse.redirect(
        new URL("/api/census/auth/toast-redirect?reason=account_deleted&redirectTo=/", request.url),
      );
    }

    if (token.role !== "household") {
      return NextResponse.redirect(
        new URL("/api/census/auth/toast-redirect?reason=unauthorized&redirectTo=/", request.url),
      );
    }

    // Authentication successful, return success response
    return new NextResponse(null, { status: 200 });
  } catch (error) {
    if (process.env.NODE_ENV === "development") {
      console.error("Census page auth error:", error);
    }

    return NextResponse.redirect(
      new URL("/api/census/auth/toast-redirect?reason=error&redirectTo=/", request.url),
    );
  }
}

/**
 * Professional middleware matcher configuration
 * Optimized for performance and security
 *
 * Matches:
 * - All page routes (for i18n and authentication)
 * - All API routes (for authentication)
 *
 * Excludes:
 * - Static assets (_next, _vercel, etc.)
 * - Files with extensions (images, fonts, etc.)
 * - Well-known files (robots.txt, sitemap.xml, etc.)
 */
export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - _vercel (Vercel internals)
     * - favicon.ico, robots.txt, sitemap.xml (static files)
     * - Files with extensions (images, fonts, etc.)
     */
    "/((?!_next/static|_next/image|_vercel|favicon.ico|robots.txt|sitemap.xml|manifest.json|.*\\..*).*)",
  ],
};
