{"name": "wsccc-census-system", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbo", "dev:webpack": "next dev", "build": "prisma generate && next build", "start": "next start", "lint": "ultracite lint", "format": "ultracite format", "db:backup": "node scripts/backup-database.mjs", "db:generate": "dotenv -e .env.local -- prisma generate", "db:push": "dotenv -e .env.local -- prisma db push", "db:migrate": "dotenv -e .env.local -- prisma migrate dev", "db:studio": "dotenv -e .env.local -- prisma studio"}, "dependencies": {"@ai-sdk/google": "^1.2.19", "@ai-sdk/react": "^1.2.12", "@hcaptcha/react-hcaptcha": "^1.12.0", "@hookform/resolvers": "^5.1.0", "@node-rs/argon2": "^2.0.0", "@prisma/client": "^6.12.0", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-avatar": "^1.1.6", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-collapsible": "^1.1.7", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.3", "@t3-oss/env-nextjs": "^0.13.6", "@tanstack/react-query": "^5.74.4", "@types/cookie": "^0.6.0", "@types/google.maps": "^3.58.1", "@types/js-cookie": "^3.0.6", "ai": "^4.3.16", "autoprefixer": "^10.4.21", "axios": "^1.8.4", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cookie": "^1.0.2", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "html-to-image": "^1.11.13", "html2canvas": "^1.4.1", "html2canvas-pro": "^1.5.11", "input-otp": "^1.4.2", "jiti": "^2.4.2", "jose": "^6.0.11", "js-cookie": "^3.0.5", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jszip": "^3.10.1", "lru-cache": "^11.1.0", "lucide-react": "^0.488.0", "nanoid": "^5.1.5", "next": "^15.4.1", "next-auth": "^4.24.11", "next-intl": "^4.3.0", "next-themes": "^0.4.6", "otplib": "^12.0.1", "pg": "^8.16.3", "qr-code-styling": "^1.9.2", "qr-scanner": "^1.4.2", "qrcode": "^1.5.4", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "react-window": "^1.8.11", "recharts": "^2.15.3", "rehype-highlight": "^7.0.1", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.0", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "ua-parser-js": "^2.0.3", "use-places-autocomplete": "^4.0.1", "vaul": "^1.1.2", "zod": "^3.25.64"}, "devDependencies": {"@babel/preset-typescript": "^7.27.1", "@babel/register": "^7.27.1", "@biomejs/biome": "^2.1.2", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/qrcode": "^1.5.5", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "@types/react-window": "^1.8.8", "@types/ua-parser-js": "^0.7.39", "cross-env": "^7.0.3", "dotenv-cli": "^8.0.0", "eslint": "^9.31.0", "eslint-config-next": "^15.4.4", "prisma": "^6.12.0", "tailwindcss": "^4", "typescript": "^5", "ultracite": "^5.0.49"}}