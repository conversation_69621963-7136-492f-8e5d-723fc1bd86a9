/**
 * Census error translation mappings
 * Maps error codes to translation keys in the 'auth' namespace
 * Used by useMessage hook and server-side translation routes
 */
export const censusErrorKeys: Record<string, string> = {
  // Census code validation errors
  InvalidCode: "invalidCode",
  ExpiredCode: "expiredCode",
  UsedCode: "usedCode",

  // Census submission errors
  SubmissionFailed: "submissionFailed",
  IncompleteData: "incompleteData",
  UnexpectedError: "unexpectedError",
  registrationError: "registrationError",
  DuplicateSacramentTypes: "duplicateSacramentTypes",

  // Member management errors
  failedToDeleteHouseholdMember: "failedToDeleteHouseholdMember",

  // Network and timeout errors
  requestTimeout: "requestTimeout",
  networkError: "networkError",

  // Census authentication errors
  unauthenticated: "unauthenticated",
  unauthorized: "unauthorized",
  census_closed: "censusClosed",
  CensusClosed: "censusClosed",
  session_expired: "sessionExpired",
  account_deleted: "accountDeleted",
  TooManyAttempts: "tooManyAttempts",

  // Default error message
  default: "censusError",
};
