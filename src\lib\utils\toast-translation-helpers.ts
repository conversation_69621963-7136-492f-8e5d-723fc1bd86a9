/**
 * Client-side toast translation helpers
 * Provides secure, non-blocking translation enhancement for the existing AlertContext system
 * Maintains complete separation between admin and census auth systems
 */

import { authErrorKeys } from "@/lib/errors/auth-errors";
import { censusErrorKeys } from "@/lib/errors/census-errors";
import { settingsErrorKeys } from "@/lib/errors/settings-errors";

/**
 * Toast message data structure
 */
export interface ToastData {
	type: "success" | "error" | "info" | "warning";
	message: string;
}

/**
 * Validates if a given string is a supported locale
 *
 * @param locale - The locale string to validate
 * @returns boolean - True if the locale is supported
 */
export function isSupportedLocale(locale: string): locale is "en" | "zh-CN" {
	return locale === "en" || locale === "zh-CN";
}

/**
 * Gets a safe locale with fallback to English
 *
 * @param locale - The locale to validate
 * @returns 'en' | 'zh-CN' - The validated locale or 'en' as fallback
 */
export function getSafeLocale(locale?: string | null): "en" | "zh-CN" {
	if (!locale) return "en";
	return isSupportedLocale(locale) ? locale : "en";
}

/**
 * Gets the appropriate error key mapping based on error type
 *
 * @param errorType - The type of error (auth, census, settings)
 * @returns Record<string, string> - The error key mapping
 */
export function getErrorKeyMapping(
	errorType: "auth" | "census" | "settings",
): Record<string, string> {
	switch (errorType) {
		case "auth":
			return authErrorKeys;
		case "census":
			return censusErrorKeys;
		case "settings":
			return settingsErrorKeys;
		default:
			return authErrorKeys;
	}
}

/**
 * Determines the auth context from the current URL path
 * This maintains separation between admin and census systems
 *
 * @param pathname - The current pathname
 * @returns 'admin' | 'census' - The determined auth context
 */
export function getAuthContextFromPath(pathname: string): "admin" | "census" {
	// Admin paths start with /admin or are API routes under /api/admin
	if (pathname.startsWith("/admin") || pathname.startsWith("/api/admin")) {
		return "admin";
	}

	// Census paths are everything else (including root and /census)
	return "census";
}

/**
 * Safely parses toast data from a cookie value
 *
 * @param cookieValue - The cookie value to parse
 * @returns ToastData | null - The parsed toast data or null if invalid
 */
export function parseToastData(cookieValue: string): ToastData | null {
	try {
		const parsed = JSON.parse(cookieValue);

		// Validate the structure
		if (
			parsed &&
			typeof parsed === "object" &&
			typeof parsed.type === "string" &&
			typeof parsed.message === "string" &&
			["success", "error", "info", "warning"].includes(parsed.type)
		) {
			return parsed as ToastData;
		}

		return null;
	} catch {
		return null;
	}
}

/**
 * @deprecated Use next-intl's useLocale() hook instead
 * This function is redundant since next-intl provides built-in locale detection
 *
 * @returns 'en' | 'zh-CN' - The current locale or 'en' as fallback
 */
export function getCurrentLocale(): "en" | "zh-CN" {
	console.warn(
		"[getCurrentLocale] DEPRECATED: Use next-intl useLocale() hook instead",
	);

	if (typeof window === "undefined") {
		return "en";
	}

	try {
		// Client-side: read from cookie (but useLocale() is better)
		const cookies = document.cookie.split(";");
		const localeCookie = cookies.find((cookie) =>
			cookie.trim().startsWith("NEXT_LOCALE="),
		);

		if (localeCookie) {
			const localeValue = localeCookie.split("=")[1]?.trim();
			return getSafeLocale(localeValue);
		}

		return "en";
	} catch {
		return "en";
	}
}

/**
 * Creates a secure error message with proper fallbacks
 * This function ensures that error messages are always displayable
 *
 * @param message - The message to validate
 * @param fallback - The fallback message if validation fails
 * @returns string - A safe error message
 */
export function createSecureErrorMessage(
	message: string,
	fallback = "An error occurred",
): string {
	// Basic validation to ensure the message is safe to display
	if (typeof message !== "string" || message.trim().length === 0) {
		return fallback;
	}

	// Limit message length for security
	const maxLength = 500;
	if (message.length > maxLength) {
		return message.substring(0, maxLength) + "...";
	}

	return message.trim();
}
