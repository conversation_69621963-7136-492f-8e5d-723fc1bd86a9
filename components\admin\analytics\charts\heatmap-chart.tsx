"use client";

import { type BaseChartProps, registerChart } from "./chart-registry";
import { getHeatmapColor } from "./utils";

interface HeatmapDataPoint {
	x: string;
	y: string;
	value: number;
	[key: string]: unknown;
}

function HeatmapChartComponent({
	data,
	isAnimationActive = true,
	className,
}: BaseChartProps) {
	// Note: Config could be used for customisation but current implementation uses direct data structure

	// Get unique x and y values
	const xValues = Array.from(
		new Set(data.data.map((d) => (d as HeatmapDataPoint).x)),
	).sort();
	const yValues = Array.from(
		new Set(data.data.map((d) => (d as HeatmapDataPoint).y)),
	).sort();

	// Find min and max values for color scaling
	const values = data.data.map((d) => (d as HeatmapDataPoint).value);
	const minValue = Math.min(...values);
	const maxValue = Math.max(...values);
	const valueRange = maxValue - minValue;

	// Create a map for quick lookup
	const dataMap = new Map<string, number>();
	data.data.forEach((d) => {
		const point = d as HeatmapDataPoint;
		dataMap.set(`${point.x}-${point.y}`, point.value);
	});

	// Generate color based on value using utility function
	const getColor = (value: number | undefined) =>
		getHeatmapColor(value, minValue, maxValue);

	// Calculate cell dimensions with improved bounds
	const cellWidth = Math.max(
		80,
		Math.min(150, Math.max(800 / xValues.length, 60)),
	);
	const cellHeight = Math.max(
		50,
		Math.min(100, Math.max(600 / yValues.length, 40)),
	);

	return (
		<div className={`w-full overflow-x-auto ${className || ""}`}>
			<div className="inline-block min-w-full">
				{/* Chart title and legend */}
				<div className="mb-4 flex items-center justify-between">
					<div className="font-medium text-slate-700 text-sm dark:text-slate-300">
						{data.title || "Heatmap"}
					</div>

					{/* Color scale legend */}
					<div className="flex items-center gap-2 text-slate-600 text-xs dark:text-slate-400">
						<span>{minValue.toFixed(1)}</span>
						<div className="flex h-4 w-32 overflow-hidden rounded">
							{Array.from({ length: 20 }, (_, i) => (
								<div
									className="flex-1"
									key={i}
									style={{
										backgroundColor: getColor(minValue + (valueRange * i) / 19),
									}}
								/>
							))}
						</div>
						<span>{maxValue.toFixed(1)}</span>
					</div>
				</div>

				{/* Heatmap grid */}
				<div className="relative">
					{/* Y-axis labels */}
					<div className="absolute top-8 left-0">
						{yValues.map((yValue, yIndex) => (
							<div
								className="flex items-center justify-end pr-2 text-slate-600 text-xs dark:text-slate-400"
								key={yValue}
								style={{
									height: cellHeight,
									transform: `translateY(${yIndex * cellHeight}px)`,
								}}
							>
								{yValue}
							</div>
						))}
					</div>

					{/* Main heatmap */}
					<div className="ml-20">
						{/* X-axis labels */}
						<div className="mb-2 flex">
							{xValues.map((xValue) => (
								<div
									className="truncate px-1 text-center text-slate-600 text-xs dark:text-slate-400"
									key={xValue}
									style={{ width: cellWidth }}
									title={xValue}
								>
									{xValue}
								</div>
							))}
						</div>

						{/* Heatmap cells */}
						<div
							className="grid gap-1"
							style={{
								gridTemplateColumns: `repeat(${xValues.length}, ${cellWidth}px)`,
							}}
						>
							{yValues.map((yValue) =>
								xValues.map((xValue) => {
									const value = dataMap.get(`${xValue}-${yValue}`);
									const color = getColor(value);

									return (
										<div
											className="group relative cursor-pointer rounded border border-slate-200 transition-all duration-200 hover:z-10 hover:scale-105 dark:border-slate-600"
											key={`${xValue}-${yValue}`}
											style={{
												width: cellWidth,
												height: cellHeight,
												backgroundColor: color,
												animation: isAnimationActive
													? `fadeIn 0.5s ease-out ${Math.min((yValues.indexOf(yValue) + xValues.indexOf(xValue)) * 50, 2000)}ms both`
													: "none",
											}}
											title={`${xValue} × ${yValue}: ${value?.toFixed(2) || "No data"}`}
										>
											{/* Value label */}
											{value !== undefined && (
												<div className="absolute inset-0 flex items-center justify-center">
													<span className="font-medium text-slate-800 text-xs dark:text-slate-200">
														{value.toFixed(1)}
													</span>
												</div>
											)}

											{/* Hover tooltip */}
											<div className="-translate-x-1/2 pointer-events-none absolute bottom-full left-1/2 z-20 mb-2 transform whitespace-nowrap rounded bg-slate-900 px-2 py-1 text-white text-xs opacity-0 transition-opacity duration-200 group-hover:opacity-100 dark:bg-slate-100 dark:text-slate-900">
												<div className="font-medium">
													{xValue} × {yValue}
												</div>
												<div>Value: {value?.toFixed(2) || "No data"}</div>
												<div className="-translate-x-1/2 absolute top-full left-1/2 transform border-4 border-transparent border-t-slate-900 dark:border-t-slate-100" />
											</div>
										</div>
									);
								}),
							)}
						</div>
					</div>
				</div>
			</div>

			{/* Add CSS animation */}
			<style jsx>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: scale(0.8);
          }
          to {
            opacity: 1;
            transform: scale(1);
          }
        }
      `}</style>
		</div>
	);
}

// Register the component
registerChart("heatmap", HeatmapChartComponent);

export default HeatmapChartComponent;
