import { getTranslations } from 'next-intl/server';
import { z } from 'zod/v4';
import { getTodayInSydney, startOfDay } from '@/lib/utils/date-time';

/**
 * Server-side validation schemas for census forms
 * These schemas now support translations using next-intl's errorMap pattern
 * For client-side validation with translations, use the client validation utilities below
 */

// Legacy household member schema is replaced by createHouseholdMemberSchema factory function

/**
 * Create household member schema with translations
 */
export async function createHouseholdMemberSchema(
  locale: 'en' | 'zh-CN' = 'en'
) {
  const tValidation = await getTranslations({
    locale,
    namespace: 'validation',
  });

  return z.object({
    id: z.number().optional(),
    firstName: z.string().min(1, { error: tValidation('firstNameRequired') }),
    lastName: z.string().min(1, { error: tValidation('lastNameRequired') }),
    dateOfBirth: z.coerce.date().refine(
      (date) => {
        const today = getTodayInSydney();
        const dateToCheck = startOfDay(date);
        return dateToCheck <= today;
      },
      { error: tValidation('dateOfBirthCannotBeInTheFuture') }
    ),
    gender: z.enum(['male', 'female', 'other'], {
      error: tValidation('genderRequired'),
    }),
    mobilePhone: z
      .string()
      .min(10, { error: tValidation('mobilePhoneMustBeAtLeast10Digi') })
      .max(10, { error: tValidation('mobilePhoneCannotExceed10Digit') })
      .regex(/^\d+$/, { error: tValidation('mobilePhoneCanOnlyContainNumbe') }),
    hobby: z.string().optional().or(z.literal('')),
    occupation: z.string().optional().or(z.literal('')),
    relationship: z.enum(
      ['head', 'spouse', 'child', 'parent', 'relative', 'other'],
      { error: tValidation('relationshipRequired') }
    ),
  });
}

/**
 * Create sacrament schema with translations
 */
export async function createSacramentSchema(locale: 'en' | 'zh-CN' = 'en') {
  const tValidation = await getTranslations({
    locale,
    namespace: 'validation',
  });

  return z.object({
    id: z.number().optional(),
    memberId: z.number().optional(),
    sacramentTypeId: z.number(),
    date: z
      .union([
        z.date().refine(
          (date) => {
            const today = getTodayInSydney();
            const dateToCheck = startOfDay(date);
            return dateToCheck <= today;
          },
          {
            message: tValidation('sacramentDateCannotBeInTheFutu'),
          }
        ),
        z.string().refine(
          (val) => {
            if (!val || val === '') return true; // Allow empty strings
            const parsedDate = new Date(val);
            if (isNaN(parsedDate.getTime())) return false; // Invalid date format
            const today = getTodayInSydney();
            const dateToCheck = startOfDay(parsedDate);
            return dateToCheck <= today;
          },
          {
            message: tValidation('sacramentDateCannotBeInTheFutu'),
          }
        ),
        z.null(),
      ])
      .optional(),
    place: z.string().optional().or(z.literal('')),
  });
}

/**
 * Create combined member sacrament schema with translations
 */
export async function createCombinedMemberSacramentSchema(
  locale: 'en' | 'zh-CN' = 'en'
) {
  const tValidation = await getTranslations({
    locale,
    namespace: 'validation',
  });
  const householdMemberSchema = await createHouseholdMemberSchema(locale);

  return householdMemberSchema.extend({
    sacraments: z
      .array(
        z.object({
          memberId: z.number().optional(),
          sacramentTypeId: z.number(),
          date: z
            .union([
              z.date().refine(
                (date) => {
                  const today = getTodayInSydney();
                  const dateToCheck = startOfDay(date);
                  return dateToCheck <= today;
                },
                {
                  message: tValidation('sacramentDateCannotBeInTheFutu'),
                }
              ),
              z.string().refine(
                (val) => {
                  if (!val || val === '') return true; // Allow empty strings
                  const parsedDate = new Date(val);
                  if (isNaN(parsedDate.getTime())) return false; // Invalid date format
                  const today = getTodayInSydney();
                  const dateToCheck = startOfDay(parsedDate);
                  return dateToCheck <= today;
                },
                {
                  message: tValidation('sacramentDateCannotBeInTheFutu'),
                }
              ),
              z.null(),
            ])
            .optional(),
          place: z.string().optional().or(z.literal('')),
        })
      )
      .optional(),
  });
}

/**
 * Create census form status schema with translations
 */
export async function createCensusFormStatusSchema(
  locale: 'en' | 'zh-CN' = 'en'
) {
  const tValidation = await getTranslations({
    locale,
    namespace: 'validation',
  });

  return z.object({
    householdId: z.number(),
    censusYearId: z.number(),
    status: z.enum(['not_started', 'in_progress', 'completed']),
    householdComment: z
      .string()
      .max(1000, { error: tValidation('householdCommentTooLong') })
      .optional()
      .or(z.literal('')),
  });
}

/**
 * Create add household member schema with translations
 */
export async function createAddHouseholdMemberSchema(
  locale: 'en' | 'zh-CN' = 'en'
) {
  const householdMemberSchema = await createHouseholdMemberSchema(locale);
  return householdMemberSchema.omit({ id: true });
}

/**
 * Create update household member schema with translations
 */
export async function createUpdateHouseholdMemberSchema(
  locale: 'en' | 'zh-CN' = 'en'
) {
  return await createHouseholdMemberSchema(locale);
}

/**
 * Create add sacrament schema with translations
 */
export async function createAddSacramentSchema(locale: 'en' | 'zh-CN' = 'en') {
  const sacramentSchema = await createSacramentSchema(locale);
  return sacramentSchema.omit({ id: true });
}

/**
 * Create update sacrament schema with translations
 */
export async function createUpdateSacramentSchema(
  locale: 'en' | 'zh-CN' = 'en'
) {
  return await createSacramentSchema(locale);
}

// Type definitions for server-side validation
export type ServerHouseholdMemberFormValues = z.infer<
  Awaited<ReturnType<typeof createHouseholdMemberSchema>>
>;
export type ServerAddHouseholdMemberFormValues = z.infer<
  Awaited<ReturnType<typeof createAddHouseholdMemberSchema>>
>;
export type ServerUpdateHouseholdMemberFormValues = z.infer<
  Awaited<ReturnType<typeof createUpdateHouseholdMemberSchema>>
>;
export type ServerSacramentFormValues = z.infer<
  Awaited<ReturnType<typeof createSacramentSchema>>
>;
export type ServerAddSacramentFormValues = z.infer<
  Awaited<ReturnType<typeof createAddSacramentSchema>>
>;
export type ServerUpdateSacramentFormValues = z.infer<
  Awaited<ReturnType<typeof createUpdateSacramentSchema>>
>;
export type ServerCombinedMemberSacramentFormValues = z.infer<
  Awaited<ReturnType<typeof createCombinedMemberSacramentSchema>>
>;
export type ServerCensusFormStatusValues = z.infer<
  Awaited<ReturnType<typeof createCensusFormStatusSchema>>
>;

// ============================================================================
// NOTE: For client-side validation with translations, use:
// - src/lib/validation/client/census-client.ts
// This file contains only server-side schemas for API validation
// ============================================================================
