import { type NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { censusAuthOptions } from "@/lib/census-auth/census-auth-options";
import { getHouseholdMemberWithDetails } from "@/lib/db/household-members";
import { prisma } from "@/lib/db/prisma";
// import { ISacrament } from '@/types'; // Unused import
import {
	createSacrament,
	deleteSacrament,
	updateSacrament,
} from "@/lib/db/sacraments";
import { getZodErrorDetails } from "@/lib/utils/error-handling";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";
import { createSacramentSchema } from "@/lib/validation/census-form";

/**
 * GET /api/census/sacraments
 *
 * Fetches all sacraments for a specific member
 */
export async function GET(request: NextRequest) {
	// Get locale from centralized utility at the beginning
	const locale = await getLocaleFromCookies();
	const t = await getTranslations({ locale, namespace: "errors" });

	try {
		// Check if user is authenticated
		const session = await getServerSession(censusAuthOptions);

		if (!session) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Check if user has the household role
		if (session.user.role !== "household") {
			return NextResponse.json({ error: t("forbidden") }, { status: 403 });
		}

		// Check if household ID exists in session
		if (!session.user.householdId) {
			return NextResponse.json(
				{
					error: t("householdNotRegistered"),
					message: t("pleaseCompleteHouseholdRegistrationFirst"),
				},
				{ status: 400 },
			);
		}

		// Get the member ID from the URL
		const url = new URL(request.url);
		const memberId = url.searchParams.get("memberId");

		if (!memberId) {
			return NextResponse.json(
				{ error: t("memberIdRequired") },
				{ status: 400 },
			);
		}

		const memberIdNum = Number.parseInt(memberId, 10);
		const householdId = Number.parseInt(session.user.householdId, 10);
		const censusYearId = Number.parseInt(session.user.censusYearId, 10);

		// Verify the member belongs to this household
		const members = await getHouseholdMemberWithDetails(
			householdId,
			censusYearId,
		);
		const memberExists = members.some((m) => m.memberId === memberIdNum);

		if (!memberExists) {
			return NextResponse.json(
				{
					error: t("memberNotFoundInThisHousehold"),
				},
				{ status: 404 },
			);
		}

		// Get all sacraments for this member using Prisma
		const sacraments = await prisma.sacrament.findMany({
			where: { memberId: memberIdNum },
			include: {
				sacramentType: {
					select: {
						name: true,
						code: true,
					},
				},
			},
		});

		// Transform to match expected format
		const transformedSacraments = sacraments.map((s) => ({
			id: s.id,
			memberId: s.memberId,
			sacramentTypeId: s.sacramentTypeId,
			sacrament_name: s.sacramentType.name,
			sacrament_code: s.sacramentType.code,
			date: s.date,
			place: s.place,
			notes: s.notes,
			censusYearId: s.censusYearId,
		}));

		return NextResponse.json(transformedSacraments);
	} catch (error) {
		return NextResponse.json(
			{
				error: t("failedToFetchSacraments"),
				details: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 },
		);
	}
}

/**
 * POST /api/census/sacraments
 *
 * Creates a new sacrament record
 */
export async function POST(request: NextRequest) {
	// Get locale from centralized utility at the beginning
	const locale = await getLocaleFromCookies();
	const t = await getTranslations({ locale, namespace: "errors" });
	const tNotifications = await getTranslations({
		locale,
		namespace: "notifications",
	});

	try {
		// Check if user is authenticated
		const session = await getServerSession(censusAuthOptions);

		if (!session) {
			return NextResponse.json({ error: t("unauthorized") }, { status: 401 });
		}

		// Check if user has the household role
		if (session.user.role !== "household") {
			return NextResponse.json({ error: t("forbidden") }, { status: 403 });
		}

		// Check if household ID exists in session
		if (!session.user.householdId) {
			return NextResponse.json(
				{
					error: t("householdNotRegistered"),
					message: t("pleaseCompleteHouseholdRegistrationFirst"),
				},
				{ status: 400 },
			);
		}

		// Parse request body
		const body = await request.json();

		// Log request for debugging (development only, no personal data)
		if (process.env.NODE_ENV === "development") {
		}

		// Validate request body with translations
		const addSacramentSchema = await createSacramentSchema(locale);
		const validationResult = addSacramentSchema.safeParse(body);

		if (!validationResult.success) {
			return NextResponse.json(
				{
					error: t("invalidRequestData"),
					details: validationResult.error.issues,
				},
				{ status: 400 },
			);
		}

		const data = validationResult.data;
		const householdId = Number.parseInt(session.user.householdId, 10);
		const censusYearId = Number.parseInt(session.user.censusYearId, 10);

		// Verify the member belongs to this household
		const members = await getHouseholdMemberWithDetails(
			householdId,
			censusYearId,
		);
		const memberExists = members.some((m) => m.memberId === data.memberId);

		if (!memberExists) {
			return NextResponse.json(
				{
					error: t("memberNotFoundInThisHousehold"),
				},
				{ status: 404 },
			);
		}

		// Get the total number of distinct sacrament types available
		const maxSacramentTypes = await prisma.sacramentType.count();

		// Check current number of sacraments for this member
		const currentMemberSacramentsCount = await prisma.sacrament.count({
			where: {
				memberId: data.memberId,
				censusYearId,
			},
		});

		if (currentMemberSacramentsCount >= maxSacramentTypes) {
			return NextResponse.json(
				{
					error: t("memberCannotHaveMoreThanMaxSacraments", {
						max: maxSacramentTypes.toString(),
					}),
				},
				{ status: 409 },
			); // 409 Conflict or 400 Bad Request
		}

		// Check if this sacrament type already exists for this member
		const existingSacramentType = await prisma.sacrament.findFirst({
			where: {
				memberId: data.memberId,
				sacramentTypeId: data.sacramentTypeId,
				censusYearId,
			},
		});

		if (existingSacramentType) {
			return NextResponse.json(
				{ message: t("sacramentAlreadyExists") },
				{ status: 409 },
			);
		}

		// Prepare data for database insertion
		// Ensure memberId is defined
		if (data.memberId === undefined) {
			return NextResponse.json(
				{
					error: t("memberIdRequired"),
					details: "The memberId field is required for creating a sacrament",
				},
				{ status: 400 },
			);
		}

		// Prepare sacrament data for database insertion
		// Ensure all fields are properly typed and handle null values correctly
		const sacramentDataForDb = {
			memberId: data.memberId,
			sacramentTypeId: data.sacramentTypeId,
			date: data.date
				? typeof data.date === "string"
					? new Date(data.date)
					: data.date
				: null,
			place: data.place || null,
			notes: null, // Notes field is not currently used in the form but required by the interface
			censusYearId,
		};

		// Create the sacrament
		const sacrament = await createSacrament(sacramentDataForDb);
		const sacramentId = sacrament.id;

		// Update census form last_updated timestamp
		await prisma.censusForm.updateMany({
			where: {
				householdId,
				censusYearId,
			},
			data: {
				lastUpdated: new Date(),
			},
		});

		// Get the created sacrament
		const createdSacrament = await prisma.sacrament.findUnique({
			where: { id: sacramentId },
			include: {
				sacramentType: true,
			},
		});

		return NextResponse.json({
			success: true,
			message: tNotifications("sacramentAddedSuccessfully"),
			sacrament: createdSacrament
				? {
						...createdSacrament,
						sacrament_name: createdSacrament.sacramentType.name,
						sacrament_code: createdSacrament.sacramentType.code,
					}
				: null,
		});
	} catch (error) {
		return NextResponse.json(
			{
				error: t("failedToCreateSacrament"),
				details: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 },
		);
	}
}

/**
 * PUT /api/census/sacraments/:id
 *
 * Updates an existing sacrament record
 */
export async function PUT(request: NextRequest) {
	// Get locale from centralized utility at the beginning
	const locale = await getLocaleFromCookies();
	const t = await getTranslations({ locale, namespace: "errors" });
	const tNotifications = await getTranslations({
		locale,
		namespace: "notifications",
	});

	try {
		// Check if user is authenticated
		const session = await getServerSession(censusAuthOptions);

		if (!session) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Check if user has the household role
		if (session.user.role !== "household") {
			return NextResponse.json({ error: t("forbidden") }, { status: 403 });
		}

		// Check if household ID exists in session
		if (!session.user.householdId) {
			return NextResponse.json(
				{
					error: t("householdNotRegistered"),
					message: t("pleaseCompleteHouseholdRegistrationFirst"),
				},
				{ status: 400 },
			);
		}

		// Parse request body
		const body = await request.json();

		// Log request for debugging (development only, no personal data)
		if (process.env.NODE_ENV === "development") {
		}

		// Validate request body with translations
		const updateSacramentSchema = await createSacramentSchema(locale);
		const validationResult = updateSacramentSchema.safeParse(body);

		if (!validationResult.success) {
			return NextResponse.json(
				{
					error: t("invalidRequestData"),
					details: getZodErrorDetails(validationResult.error),
				},
				{ status: 400 },
			);
		}

		const data = validationResult.data;
		const householdId = Number.parseInt(session.user.householdId, 10);
		const censusYearId = Number.parseInt(session.user.censusYearId, 10);

		// Verify the member belongs to this household
		const members = await getHouseholdMemberWithDetails(
			householdId,
			censusYearId,
		);
		const memberExists = members.some((m) => m.memberId === data.memberId);

		if (!memberExists) {
			return NextResponse.json(
				{
					error: t("memberNotFoundInThisHousehold"),
				},
				{ status: 404 },
			);
		}

		// Verify the sacrament exists
		const existingSacrament = await prisma.sacrament.findUnique({
			where: { id: data.id },
		});

		if (!existingSacrament) {
			return NextResponse.json(
				{
					error: t("sacramentNotFound"),
				},
				{ status: 404 },
			);
		}

		// Update the sacrament
		await updateSacrament(data.id!, {
			date: data.date
				? typeof data.date === "string"
					? new Date(data.date)
					: data.date
				: null,
			place: data.place || null,
		});

		// Update census form last_updated timestamp
		await prisma.censusForm.updateMany({
			where: {
				householdId,
				censusYearId,
			},
			data: {
				lastUpdated: new Date(),
			},
		});

		// Get the updated sacrament
		const updatedSacrament = await prisma.sacrament.findUnique({
			where: { id: data.id },
			include: {
				sacramentType: true,
			},
		});

		return NextResponse.json({
			success: true,
			message: tNotifications("sacramentUpdatedSuccessfully"),
			sacrament: updatedSacrament
				? {
						...updatedSacrament,
						sacrament_name: updatedSacrament.sacramentType.name,
						sacrament_code: updatedSacrament.sacramentType.code,
					}
				: null,
		});
	} catch (error) {
		return NextResponse.json(
			{
				error: t("sacramentUpdateFailed"),
				details: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 },
		);
	}
}

/**
 * DELETE /api/census/sacraments/:id
 *
 * Deletes a sacrament record
 */
export async function DELETE(request: NextRequest) {
	// Get locale from centralized utility at the beginning
	const locale = await getLocaleFromCookies();
	const t = await getTranslations({ locale, namespace: "errors" });
	const tNotifications = await getTranslations({
		locale,
		namespace: "notifications",
	});

	try {
		// Check if user is authenticated
		const session = await getServerSession(censusAuthOptions);

		if (!session) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Check if user has the household role
		if (session.user.role !== "household") {
			return NextResponse.json({ error: t("forbidden") }, { status: 403 });
		}

		// Get the sacrament ID from the URL
		const url = new URL(request.url);
		const sacramentId = url.searchParams.get("id");

		if (!sacramentId) {
			return NextResponse.json(
				{ error: t("sacramentIdRequired") },
				{ status: 400 },
			);
		}

		const sacramentIdNum = Number.parseInt(sacramentId, 10);
		const householdId = Number.parseInt(session.user.householdId!, 10);
		const censusYearId = Number.parseInt(session.user.censusYearId, 10);

		// Verify the sacrament belongs to a member in this household using Prisma
		const sacrament = await prisma.sacrament.findFirst({
			where: {
				id: sacramentIdNum,
				member: {
					householdMembers: {
						some: {
							householdId,
							censusYearId,
						},
					},
				},
			},
		});

		if (!sacrament) {
			return NextResponse.json(
				{
					error: t("sacramentNotFoundOrDoesNotBelong"),
				},
				{ status: 404 },
			);
		}

		// Delete the sacrament
		await deleteSacrament(sacramentIdNum);

		// Update census form last_updated timestamp using Prisma
		await prisma.censusForm.updateMany({
			where: {
				householdId,
				censusYearId,
			},
			data: {
				lastUpdated: new Date(),
			},
		});

		return NextResponse.json({
			success: true,
			message: tNotifications("sacramentDeletedSuccessfully"),
		});
	} catch (error) {
		return NextResponse.json(
			{
				error: t("sacramentDeleteFailed"),
				details: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 },
		);
	}
}
