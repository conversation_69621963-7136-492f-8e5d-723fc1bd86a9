'use client';

// Recharts imports removed - now using modular chart system
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart3,
  <PERSON>,
  <PERSON><PERSON><PERSON>cle,
  Copy,
  Grid3X3,
  Info,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Table as TableIcon,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useTheme } from 'next-themes';
import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import {
  oneDark,
  oneLight,
} from 'react-syntax-highlighter/dist/cjs/styles/prism';
import rehypeHighlight from 'rehype-highlight';
import rehypeSanitize from 'rehype-sanitize';
import remarkGfm from 'remark-gfm';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent } from '@/components/ui/collapsible';
import type { ChartData } from '../../../lib/utils/chart-data-formatter';
import { CollapsibleHeader } from '../../../src/components/shared/collapsible-header';
import { ErrorBoundaryWrapper } from '../../../src/components/shared/error-boundary-wrapper';
// Import new hooks and components for improved architecture
import { useCollapsibleState } from '../../../src/hooks/use-collapsible-state';
import { useMemoryCleanup } from '../../../src/hooks/use-memory-cleanup';
import {
  generateChartKey,
  generateTableKey,
} from '../../../src/utils/performance/keyGeneration';

// Import the modular chart system
import { validateChartDataComprehensive } from './charts';
import { ExportButton } from './charts/export-button';
import { MobileResponsiveChartWrapper } from './charts/mobile-responsive-chart-wrapper';
import { TableExportButton } from './table-export-button';
import {
  shouldUseTableVirtualization,
  VirtualizedTable,
} from './virtualized-table';

interface RichMessageRendererProps {
  content: string;
  queryType?: 'members' | 'households' | 'unique_codes' | 'general';
}

// Remove global state - will be moved to component-level state

// Global chart animation tracker to prevent restarts across all instances
const chartAnimationTracker = new Set<string>();

// Chart data validation is now handled by the modular chart system

export function RichMessageRenderer({ content }: RichMessageRendererProps) {
  const { theme } = useTheme();
  const [copiedCode, setCopiedCode] = useState<string | null>(null);
  const t = useTranslations('admin');

  // Component-level state to store table export data (prevents global state leaks)
  const [tableExportMeta, setTableExportMeta] = useState<{
    data: Record<string, unknown>[];
    title?: string;
    queryType?: string;
  } | null>(null);

  // Extract table export data from content during render (safe approach)
  const extractedTableExportData = React.useMemo(() => {
    try {
      const tableDataMatch = content.match(/TABLE_EXPORT_DATA:\s*({.*?})\s*$/);
      if (tableDataMatch) {
        const tableExportMetadata = JSON.parse(tableDataMatch[1]);
        return {
          data: tableExportMetadata.data,
          title: tableExportMetadata.title,
          queryType: tableExportMetadata.queryType,
        };
      }
    } catch (_error) {}
    return null;
  }, [content]);

  // Update table export meta when extracted data changes
  React.useEffect(() => {
    if (
      extractedTableExportData &&
      JSON.stringify(tableExportMeta) !==
        JSON.stringify(extractedTableExportData)
    ) {
      setTableExportMeta(extractedTableExportData);
    }
  }, [extractedTableExportData, tableExportMeta]);

  const handleCopyCode = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code);
      setCopiedCode(code);
      setTimeout(() => setCopiedCode(null), 2000);
    } catch (_error) {}
  };

  // Define proper types for markdown components
  interface CodeProps {
    inline?: boolean;
    className?: string;
    children?: React.ReactNode;
    [key: string]: unknown;
  }

  interface BasicProps {
    children?: React.ReactNode;
  }

  // Custom components for markdown rendering
  const components: any = {
    // Enhanced code blocks with syntax highlighting and copy functionality
    code({ inline, className, children, ...props }: CodeProps) {
      const match = /language-(\w+)/.exec(className || '');
      const language = match ? match[1] : '';
      const codeString = String(children).replace(/\n$/, '');

      if (!inline && language) {
        return (
          <div className="group relative my-6 overflow-hidden rounded-lg border border-border bg-card">
            {/* Header with language badge and copy button - Grok style */}
            <div className="flex items-center justify-between border-border border-b bg-muted px-4 py-2.5">
              <Badge
                className="border-border font-medium text-muted-foreground text-xs"
                variant="outline"
              >
                {language.toUpperCase()}
              </Badge>
              <Button
                className="h-7 w-7 rounded p-0 opacity-70 transition-opacity duration-200 hover:bg-muted hover:opacity-100"
                onClick={() => handleCopyCode(codeString)}
                size="sm"
                title={copiedCode === codeString ? t('copied') : t('copyCode')}
                variant="ghost"
              >
                {copiedCode === codeString ? (
                  <Check className="h-3.5 w-3.5 text-green-600" />
                ) : (
                  <Copy className="h-3.5 w-3.5 text-muted-foreground" />
                )}
              </Button>
            </div>

            {/* Code content with horizontal scroll */}
            <div className="thin-scrollbar relative w-full max-w-full overflow-x-auto">
              <SyntaxHighlighter
                className="!mt-0 !mb-0 !rounded-none !border-0"
                codeTagProps={{
                  style: {
                    fontFamily:
                      'ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
                    fontWeight: '400',
                    whiteSpace: 'pre',
                  },
                }}
                customStyle={{
                  margin: 0,
                  padding: '1.25rem',
                  background: 'transparent',
                  fontSize: '0.875rem',
                  lineHeight: '1.5',
                  minWidth: 'fit-content',
                }}
                language={language}
                lineNumberStyle={{
                  minWidth: '3em',
                  paddingRight: '1em',
                  color: theme === 'dark' ? '#6b7280' : '#9ca3af',
                  borderRight: `1px solid ${theme === 'dark' ? '#374151' : '#e5e7eb'}`,
                  marginRight: '1em',
                  textAlign: 'right' as const,
                  userSelect: 'none' as const,
                }}
                PreTag="div"
                showLineNumbers={codeString.split('\n').length > 3}
                style={theme === 'dark' ? oneDark : oneLight}
                {...props}
              >
                {codeString}
              </SyntaxHighlighter>
            </div>
          </div>
        );
      }

      return (
        <code
          className="rounded-md border border-border bg-muted px-2 py-1 font-mono text-foreground text-sm"
          {...props}
        >
          {children}
        </code>
      );
    },

    // Enhanced tables with collapsible functionality and export capability
    table({ children }: BasicProps) {
      return (
        <CollapsibleTable
          exportData={tableExportMeta?.data}
          exportQueryType={tableExportMeta?.queryType}
          exportTitle={tableExportMeta?.title}
        >
          {children}
        </CollapsibleTable>
      );
    },

    thead({ children }: BasicProps) {
      return (
        <thead className="border-slate-200 border-b-2 bg-gradient-to-r from-slate-50 to-slate-100 dark:border-slate-600 dark:from-slate-800 dark:to-slate-700">
          {children}
        </thead>
      );
    },

    th({ children }: BasicProps) {
      return (
        <th className="border-border border-r bg-background/50 px-6 py-4 text-left font-semibold text-muted-foreground text-xs uppercase tracking-wider last:border-r-0">
          <div className="flex items-center gap-2">{children}</div>
        </th>
      );
    },

    tbody({ children }: BasicProps) {
      return (
        <tbody className="divide-y divide-border bg-background">
          {children}
        </tbody>
      );
    },

    tr({ children }: BasicProps) {
      return (
        <tr className="group transition-colors duration-150 hover:bg-slate-50 dark:hover:bg-slate-800/50">
          {children}
        </tr>
      );
    },

    td({ children }: BasicProps) {
      // Check if content is numeric for better formatting
      const isNumeric =
        typeof children === 'string' && /^\d+(\.\d+)?$/.test(children.trim());

      // Check if this looks like a phone number or ID (should not be formatted as number)
      const isPhoneOrId =
        typeof children === 'string' &&
        (/^0\d{9}$/.test(children.trim()) || // Australian mobile format (0xxxxxxxxx)
          /^\d{10}$/.test(children.trim()) || // 10-digit numbers (likely phone)
          children.trim().length > 8); // Long numbers are likely IDs/phones

      return (
        <td
          className={`border-border border-r px-6 py-4 text-foreground text-sm transition-colors duration-150 last:border-r-0 hover:bg-muted/30 ${isNumeric && !isPhoneOrId ? 'number-cell' : ''}`}
        >
          <div
            className={`flex items-center ${isNumeric && !isPhoneOrId ? 'justify-end' : ''}`}
          >
            {isNumeric && !isPhoneOrId ? (
              <span className="font-medium text-foreground">
                {typeof children === 'string'
                  ? Number(children.replace(/,/g, '')).toLocaleString(
                      undefined,
                      { maximumFractionDigits: 2 }
                    )
                  : children}
              </span>
            ) : (
              children
            )}
          </div>
        </td>
      );
    },

    // Enhanced blockquotes
    blockquote({ children }: BasicProps) {
      return (
        <blockquote className="my-4 border-[#FF6308] border-l-4 bg-[#FF6308]/5 py-2 pl-4 italic">
          {children}
        </blockquote>
      );
    },

    // Custom chart component detection and table export data detection
    p({ children }: BasicProps) {
      const text = typeof children === 'string' ? children : '';

      // Check for chart data patterns
      if (text.includes('CHART_DATA:')) {
        try {
          const chartDataMatch = text.match(/CHART_DATA:\s*({.*?})\s*$/);
          if (chartDataMatch) {
            const chartData = JSON.parse(chartDataMatch[1]);

            // Validate chart data structure for security
            const validation = validateChartDataComprehensive(chartData);
            if (validation.isValid) {
              // Create a stable key based on chart data to prevent unnecessary re-renders
              // Use a hash of the essential data properties for stability
              const dataHash =
                chartData.data.length > 0
                  ? `${chartData.type}-${chartData.data.length}-${chartData.data[0]?.name || 'chart'}-${chartData.data[0]?.value || 0}`
                  : `${chartData.type}-empty`;
              // Use the same key for consistent state management
              return (
                <ModernChartRenderer
                  data={chartData}
                  key={`chart-${dataHash}`}
                />
              );
            }
          }
        } catch (_error) {}
      }

      // Check for table export data patterns - just hide the metadata, don't process it here
      if (text.includes('TABLE_EXPORT_DATA:')) {
        // Return nothing - this is just metadata for the table that's processed at component level
        return null;
      }

      return <p className="mb-4 leading-relaxed">{children}</p>;
    },

    // Enhanced headings
    h1({ children }: BasicProps) {
      return (
        <h1 className="mb-4 font-bold text-2xl text-foreground">{children}</h1>
      );
    },
    h2({ children }: BasicProps) {
      return (
        <h2 className="mb-3 font-semibold text-foreground text-xl">
          {children}
        </h2>
      );
    },
    h3({ children }: BasicProps) {
      return (
        <h3 className="mb-2 font-medium text-foreground text-lg">{children}</h3>
      );
    },

    // Enhanced lists
    ul({ children }: BasicProps) {
      return (
        <ul className="mb-4 list-inside list-disc space-y-1">{children}</ul>
      );
    },
    ol({ children }: BasicProps) {
      return (
        <ol className="mb-4 list-inside list-decimal space-y-1">{children}</ol>
      );
    },
    li({ children }: BasicProps) {
      return <li className="text-foreground">{children}</li>;
    },
  };

  return (
    <div className="prose prose-sm dark:prose-invert max-w-none">
      <ReactMarkdown
        components={components}
        rehypePlugins={[rehypeSanitize, rehypeHighlight]}
        remarkPlugins={[remarkGfm]}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
}

// Modern chart renderer using the modular chart system
interface ModernChartRendererProps {
  data: ChartData;
}

const ModernChartRenderer = React.memo(function ModernChartRenderer({
  data,
}: ModernChartRendererProps) {
  const t = useTranslations('admin');

  // Generate efficient key using new utility
  const chartKey = React.useMemo(() => generateChartKey(data), [data]);

  // Use new collapsible state hook with automatic memory cleanup
  const { isCollapsed, toggle } = useCollapsibleState({
    key: chartKey,
    defaultCollapsed: false,
  });

  // Setup memory cleanup for this component
  const { trackKey } = useMemoryCleanup({
    keys: [chartKey],
    cleanupOnUnmount: true,
  });

  // Track the key for cleanup
  React.useEffect(() => {
    trackKey(chartKey);
  }, [chartKey, trackKey]);

  const [isAnimationActive, setIsAnimationActive] = useState(() => {
    // Check if this chart type has already been animated globally
    return !chartAnimationTracker.has(data.type);
  });
  const chartRef = React.useRef<HTMLDivElement>(null);

  // Disable animation after first render to prevent loops
  React.useEffect(() => {
    if (isAnimationActive) {
      chartAnimationTracker.add(data.type);
      const timer = setTimeout(() => {
        setIsAnimationActive(false);
      }, 1000); // Allow animation to complete once
      return () => clearTimeout(timer);
    }
  }, [data.type, isAnimationActive]);

  if (!data.data || data.data.length === 0) {
    return (
      <div className="my-4 rounded-lg bg-muted p-4 text-center text-muted-foreground">
        No data available for chart
      </div>
    );
  }

  const getChartIcon = () => {
    switch (data.type) {
      case 'bar':
        return <BarChart3 className="h-4 w-4" />;
      case 'pie':
        return <PieChartIcon className="h-4 w-4" />;
      case 'line':
        return <BarChart3 className="h-4 w-4" />;
      case 'area':
        return <BarChart3 className="h-4 w-4" />;
      case 'scatter':
        return <BarChart3 className="h-4 w-4" />;
      case 'heatmap':
        return <Grid3X3 className="h-4 w-4" />;
      case 'radar':
        return <BarChart3 className="h-4 w-4" />;
      case 'treemap':
        return <Grid3X3 className="h-4 w-4" />;
      case 'waffle':
        return <Grid3X3 className="h-4 w-4" />;
      default:
        return <TableIcon className="h-4 w-4" />;
    }
  };

  return (
    <ErrorBoundaryWrapper>
      <div className="my-6 overflow-hidden rounded-lg border border-border bg-card">
        <Collapsible
          onOpenChange={(open) => !open && toggle()}
          open={!isCollapsed}
        >
          <CollapsibleHeader
            actions={
              <ExportButton
                chartElementRef={chartRef}
                data={data}
                isCollapsed={isCollapsed}
                onRestoreCollapse={() => {
                  if (!isCollapsed) {
                    toggle(); // Collapse the chart back
                  }
                }}
                onTemporaryExpand={() => {
                  if (isCollapsed) {
                    toggle(); // Expand the chart
                  }
                }}
                size="sm"
                variant="ghost"
              />
            }
            badge={
              <Badge className="text-xs" variant="outline">
                {data.data.length} items
              </Badge>
            }
            icon={getChartIcon()}
            isCollapsed={isCollapsed}
            onToggle={toggle}
            title={
              data.title ||
              `${data.type.charAt(0).toUpperCase() + data.type.slice(1)} Chart`
            }
            variant="chart"
          />
          <CollapsibleContent>
            {/* Chart Recommendation Alert */}
            {data.recommendation &&
              (data.recommendation.requestedType ||
                data.recommendation.confidence < 80) && (
                <div className="border-border border-b p-4">
                  <Alert
                    className={`${
                      data.recommendation.userOverride
                        ? 'border-orange-200 bg-orange-50'
                        : data.recommendation.requestedType &&
                            data.recommendation.requestedType !==
                              data.recommendation.suggestedType
                          ? 'border-amber-200 bg-amber-50'
                          : 'border-blue-200 bg-blue-50'
                    }`}
                  >
                    <div className="flex items-start gap-2">
                      {data.recommendation.userOverride ? (
                        <AlertTriangle className="mt-0.5 h-4 w-4 flex-shrink-0 text-orange-600" />
                      ) : data.recommendation.requestedType &&
                        data.recommendation.requestedType !==
                          data.recommendation.suggestedType ? (
                        <Info className="mt-0.5 h-4 w-4 flex-shrink-0 text-amber-600" />
                      ) : (
                        <CheckCircle className="mt-0.5 h-4 w-4 flex-shrink-0 text-blue-600" />
                      )}
                      <AlertDescription className="text-sm">
                        <div className="space-y-1">
                          <p className="font-medium">
                            {data.recommendation.requestedType
                              ? `Chart Type: ${data.type} (${data.recommendation.confidence}% suitable)`
                              : `Recommended Chart: ${data.type} (${data.recommendation.confidence}% confidence)`}
                          </p>
                          <p className="text-xs opacity-90">
                            {data.recommendation.reasoning}
                          </p>
                          {data.recommendation.alternatives &&
                            data.recommendation.alternatives.length > 0 && (
                              <p className="text-xs opacity-75">
                                <strong>{t('alternatives')}:</strong>{' '}
                                {data.recommendation.alternatives
                                  .map(
                                    (alt) => `${alt.type} (${alt.suitability}%)`
                                  )
                                  .join(', ')}
                              </p>
                            )}
                        </div>
                      </AlertDescription>
                    </div>
                  </Alert>
                </div>
              )}

            <div className="w-full">
              <div className="w-full" ref={chartRef}>
                <MobileResponsiveChartWrapper
                  className="w-full border-0 bg-transparent"
                  data={data}
                  enableExport={false}
                  isAnimationActive={isAnimationActive}
                  showDataSummary={false}
                />
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    </ErrorBoundaryWrapper>
  );
});

// Table configuration constants
const TABLE_CONFIG = {
  MAX_HEIGHT: 500, // Maximum table height in pixels
  ROW_HEIGHT_ESTIMATE: 40, // Estimated height per row for calculations
  LARGE_TABLE_THRESHOLD: 50, // Show warning for tables with more than 50 rows
  VIRTUALIZATION_THRESHOLD: 100, // Use virtualization for tables with more than 100 rows
  PAGINATION_THRESHOLD: 25, // Use pagination for tables with more than 25 rows
  ITEMS_PER_PAGE: 20, // Items per page for pagination
};

// Collapsible table renderer with height protection
interface CollapsibleTableProps {
  children: React.ReactNode;
  exportData?: Record<string, unknown>[];
  exportTitle?: string;
  exportQueryType?: string;
}

const CollapsibleTable = React.memo(function CollapsibleTable({
  children,
  exportData,
  exportTitle,
  exportQueryType,
}: CollapsibleTableProps) {
  const t = useTranslations('admin');

  // Generate efficient key using new utility
  const tableKey = React.useMemo(() => generateTableKey(children), [children]);

  // Use new collapsible state hook with automatic memory cleanup
  const { isCollapsed, toggle } = useCollapsibleState({
    key: tableKey,
    defaultCollapsed: false,
  });

  // Setup memory cleanup for this component
  const { trackKey } = useMemoryCleanup({
    keys: [tableKey],
    cleanupOnUnmount: true,
  });

  // Track the key for cleanup
  React.useEffect(() => {
    trackKey(tableKey);
  }, [tableKey, trackKey]);

  const tableRef = React.useRef<HTMLDivElement>(null);

  // Enhanced row counting with height protection analysis
  const getTableMetrics = () => {
    try {
      // Try to count actual rows from children
      const childrenArray = React.Children.toArray(children);
      let rowCount = 0;
      let hasHeader = false;

      childrenArray.forEach((child) => {
        if (React.isValidElement(child)) {
          if (child.type === 'thead') {
            hasHeader = true;
          } else if (child.type === 'tbody') {
            const tbodyChildren = React.Children.toArray(
              (child.props as { children: React.ReactNode }).children
            );
            rowCount += tbodyChildren.filter(
              (row) => React.isValidElement(row) && row.type === 'tr'
            ).length;
          }
        }
      });

      const estimatedHeight =
        (rowCount + (hasHeader ? 1 : 0)) * TABLE_CONFIG.ROW_HEIGHT_ESTIMATE;
      const needsHeightProtection = estimatedHeight > TABLE_CONFIG.MAX_HEIGHT;
      const isLargeTable = rowCount > TABLE_CONFIG.LARGE_TABLE_THRESHOLD;
      const needsVirtualization =
        rowCount > TABLE_CONFIG.VIRTUALIZATION_THRESHOLD;

      return {
        rowCount,
        hasHeader,
        estimatedHeight,
        needsHeightProtection,
        isLargeTable,
        needsVirtualization,
        displayText: rowCount > 0 ? `${rowCount} rows` : 'Table',
      };
    } catch {
      return {
        rowCount: 0,
        hasHeader: false,
        estimatedHeight: 0,
        needsHeightProtection: false,
        isLargeTable: false,
        needsVirtualization: false,
        displayText: 'Table',
      };
    }
  };

  const tableMetrics = getTableMetrics();

  // Check if virtualization is recommended
  const virtualizationStrategy = shouldUseTableVirtualization(
    tableMetrics.rowCount
  );

  // Determine table container styles based on metrics
  const getTableContainerStyles = () => {
    const baseStyles =
      'w-full max-w-full overflow-x-auto thin-scrollbar analytics-table-container';
    const mobileStyles = 'text-sm'; // Smaller text on mobile

    if (tableMetrics.needsHeightProtection) {
      return `${baseStyles} ${mobileStyles} overflow-y-auto scrollable`;
    }

    return `${baseStyles} ${mobileStyles}`;
  };

  const getTableContainerInlineStyles = () => {
    if (tableMetrics.needsHeightProtection) {
      return { maxHeight: `${TABLE_CONFIG.MAX_HEIGHT}px` };
    }
    return {};
  };

  return (
    <ErrorBoundaryWrapper>
      <div className="my-6 overflow-hidden rounded-xl border border-border bg-card shadow-sm">
        <Collapsible
          onOpenChange={(open) => !open && toggle()}
          open={!isCollapsed}
        >
          <CollapsibleHeader
            actions={
              exportData &&
              exportData.length > 0 && (
                <TableExportButton
                  data={exportData}
                  queryType={exportQueryType}
                  size="sm"
                  title={exportTitle}
                  variant="ghost"
                />
              )
            }
            badge={
              <div className="flex items-center gap-2">
                <Badge
                  className="border-border bg-muted text-xs"
                  variant="outline"
                >
                  {tableMetrics.displayText}
                </Badge>
                {tableMetrics.isLargeTable && (
                  <Badge
                    className="border-amber-200 bg-amber-50 text-amber-700 text-xs"
                    variant="outline"
                  >
                    Large
                  </Badge>
                )}
                {tableMetrics.needsHeightProtection && (
                  <Badge
                    className="border-blue-200 bg-blue-50 text-blue-700 text-xs"
                    variant="outline"
                  >
                    Scrollable
                  </Badge>
                )}
                {virtualizationStrategy.strategy === 'virtualization' && (
                  <Badge
                    className="border-purple-200 bg-purple-50 text-purple-700 text-xs"
                    variant="outline"
                  >
                    Virtualized
                  </Badge>
                )}
                {virtualizationStrategy.strategy === 'pagination' && (
                  <Badge
                    className="border-green-200 bg-green-50 text-green-700 text-xs"
                    variant="outline"
                  >
                    Paginated
                  </Badge>
                )}
              </div>
            }
            icon={<TableIcon className="h-4 w-4" />}
            isCollapsed={isCollapsed}
            onToggle={toggle}
            title={t('dataTable')}
            variant="table"
          />
          <CollapsibleContent>
            {/* Large table warning */}
            {tableMetrics.isLargeTable && !isCollapsed && (
              <div className="mx-4 mt-3 mb-2 rounded-lg border border-amber-200 bg-amber-50 p-3">
                <div className="flex items-start gap-2">
                  <Info className="mt-0.5 h-4 w-4 flex-shrink-0 text-amber-600" />
                  <div className="text-amber-800 text-sm">
                    <p className="font-medium">Large Table Detected</p>
                    <p className="mt-1 text-xs opacity-90">
                      This table contains {tableMetrics.rowCount} rows.
                      {virtualizationStrategy.strategy === 'virtualization' &&
                        ' Using virtualization for optimal performance.'}
                      {virtualizationStrategy.strategy === 'pagination' &&
                        ' Using pagination for better navigation.'}
                      {tableMetrics.needsHeightProtection &&
                        virtualizationStrategy.strategy === 'normal' &&
                        ' Vertical scrolling is enabled for better performance.'}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Render table based on virtualization strategy */}
            {virtualizationStrategy.strategy === 'virtualization' ||
            virtualizationStrategy.strategy === 'pagination' ? (
              <VirtualizedTable
                className="mx-4 mb-4"
                enableVirtualization={true}
              >
                {children}
              </VirtualizedTable>
            ) : (
              <div
                className={getTableContainerStyles()}
                ref={tableRef}
                style={getTableContainerInlineStyles()}
              >
                <div className="min-w-full">
                  <table className="analytics-table w-full bg-background">
                    {children}
                  </table>
                </div>
              </div>
            )}
          </CollapsibleContent>
        </Collapsible>
      </div>
    </ErrorBoundaryWrapper>
  );
});

// Legacy components removed - now using modular chart system

// Export the main component
export default RichMessageRenderer;
