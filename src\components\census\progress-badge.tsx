"use client";

import { useTranslations } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";

interface ProgressBadgeProps {
  progress: number; // Progress percentage (0-100)
  onClick: () => void;
  className?: string;
}

/**
 * ProgressBadge component - Minimalist progress ring for census completion tracking
 *
 * Features:
 * - Circular SVG progress ring with percentage in center
 * - Smooth progress transitions with 500ms duration
 * - <PERSON>lt<PERSON> showing "Census completion: X%" on hover
 * - Click action to open welcome modal/drawer
 * - Responsive design (32px w-8 h-8 for optimal visibility)
 */
export function ProgressBadge({ progress, onClick, className = "" }: ProgressBadgeProps) {
  const t = useTranslations("onboarding");

  // Ensure progress is within valid range
  const clampedProgress = Math.max(0, Math.min(100, progress));

  // Calculate stroke dash array for progress ring
  // Circle circumference = 2 * π * r, where r = 14
  const radius = 14;
  const circumference = 2 * Math.PI * radius;
  const strokeDasharray = `${(clampedProgress / 100) * circumference} ${circumference}`;

  return (
    <Button
      aria-label={t("progressBadgeTooltip", {
        progress: clampedProgress.toString(),
      })}
      className={`flex h-8 items-center gap-2 rounded-full px-2 py-1 transition-colors hover:bg-accent ${className}`}
      onClick={onClick}
      title={t("progressBadgeTooltip", {
        progress: clampedProgress.toString(),
      })}
      variant="ghost"
    >
      {/* Compact Progress Ring */}
      <div className="relative h-6 w-6 flex-shrink-0">
        <svg aria-hidden="true" className="-rotate-90 h-6 w-6 transform" viewBox="0 0 24 24">
          {/* Background ring */}
          <circle
            className="text-muted-foreground/20"
            cx="12"
            cy="12"
            fill="none"
            r="10"
            stroke="currentColor"
            strokeWidth="2"
          />

          {/* Progress ring */}
          <circle
            className="text-primary transition-all duration-500 ease-in-out"
            cx="12"
            cy="12"
            fill="none"
            r="10"
            stroke="currentColor"
            strokeDasharray={`${(clampedProgress / 100) * 62.83} 62.83`}
            strokeLinecap="round"
            strokeWidth="2"
          />
        </svg>
      </div>

      {/* Compact percentage text */}
      <span className="font-medium text-primary text-xs leading-none">{clampedProgress}%</span>
    </Button>
  );
}
