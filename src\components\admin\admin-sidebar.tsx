"use client";

import { Bar<PERSON>hart3, Home, LayoutDashboard, <PERSON><PERSON>, Settings, Users } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useTranslations } from "next-intl";
import { Collapsible } from "@/components/ui/collapsible";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { useIsMobile } from "@/hooks/use-mobile";

export function AdminSidebar(): React.ReactElement {
  const pathname = usePathname();
  const { setOpenMobile } = useSidebar();
  const isMobile = useIsMobile();
  const t = useTranslations("navigation");
  const tBrand = useTranslations("brand");

  // Handle navigation click on mobile - close sidebar for better UX
  const handleNavClick = () => {
    if (isMobile) {
      setOpenMobile(false);
    }
  };

  const navItems = [
    {
      title: t("dashboard"),
      url: "/admin/dashboard",
      icon: LayoutDashboard,
      isActive: pathname === "/admin/dashboard",
    },
    {
      title: t("household"),
      url: "/admin/household",
      icon: Home,
      isActive: pathname === "/admin/household",
    },
    {
      title: t("members"),
      url: "/admin/members",
      icon: Users,
      isActive: pathname === "/admin/members",
    },
    {
      title: t("uniqueCode"),
      url: "/admin/unique-code",
      icon: Scroll,
      isActive: pathname === "/admin/unique-code",
    },
    {
      title: t("analytics"),
      url: "/admin/analytics",
      icon: BarChart3,
      isActive: pathname === "/admin/analytics",
    },
    {
      title: t("settings"),
      url: "/admin/settings",
      icon: Settings,
      isActive: pathname === "/admin/settings",
    },
  ];

  return (
    <Sidebar collapsible="icon" variant="inset">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild size="lg">
              <Link
                className="flex w-full items-center gap-2 hover:bg-sidebar-accent/0"
                href="/admin/dashboard"
                onClick={handleNavClick}
              >
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                  <svg
                    fill="none"
                    height="16"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    width="16"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z" />
                    <path d="M3 9V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v4" />
                    <path d="M13 13h4" />
                    <path d="M13 17h4" />
                    <path d="M7 13h2v4H7z" />
                  </svg>
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">{tBrand("name")}</span>
                  <span className="truncate text-sidebar-foreground/70 text-xs">
                    {t("adminPortal")}
                  </span>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>{t("navigation")}</SidebarGroupLabel>
          <SidebarMenu>
            {navItems.map((item) => (
              <Collapsible asChild defaultOpen={item.isActive} key={item.title}>
                <SidebarMenuItem>
                  <SidebarMenuButton asChild isActive={item.isActive} tooltip={item.title}>
                    <Link className="cursor-pointer" href={item.url} onClick={handleNavClick}>
                      <item.icon />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              </Collapsible>
            ))}
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>{/* Footer content removed - Quick Access moved to header */}</SidebarFooter>
    </Sidebar>
  );
}
