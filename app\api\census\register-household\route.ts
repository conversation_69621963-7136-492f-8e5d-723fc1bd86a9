import { type NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { z } from "zod/v4";
import { censusAuthOptions } from "@/lib/census-auth/census-auth-options";
import { createHouseholdMember } from "@/lib/db/household-members";
import { createHousehold } from "@/lib/db/households";
import { createMember } from "@/lib/db/members";
import { prisma } from "@/lib/db/prisma";
import {
	getErrorMessage,
	getZodErrorDetails,
} from "@/lib/utils/error-handling";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";
import { createHouseholdRegistrationSchema } from "@/lib/validation/household-registration";

export async function POST(request: NextRequest) {
	// Get locale from centralized utility at the beginning
	const locale = await getLocaleFromCookies();
	const tErrors = await getTranslations({ locale, namespace: "errors" });

	try {
		// Check if user is authenticated
		const session = await getServerSession(censusAuthOptions);

		if (!session) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Check if user has the household role
		if (session.user.role !== "household") {
			return NextResponse.json({ error: "Forbidden" }, { status: 403 });
		}

		// Parse request body
		const body = await request.json();

		// Create validation schema with translations
		const householdRegistrationSchema =
			await createHouseholdRegistrationSchema(locale);

		// Extended schema for API validation including the session data
		const apiSchema = householdRegistrationSchema.extend({
			uniqueCodeId: z
				.string()
				.min(1, { error: tErrors("uniqueCodeIdRequired") }),
			censusYearId: z
				.string()
				.min(1, { error: tErrors("censusYearIdRequired") }),
		});

		// Validate request body
		const validationResult = apiSchema.safeParse(body);

		if (!validationResult.success) {
			return NextResponse.json(
				{
					error: tErrors("invalidRequestData"),
					details: getZodErrorDetails(validationResult.error),
				},
				{ status: 400 },
			);
		}

		const data = validationResult.data;

		// Verify that the unique code ID matches the authenticated user
		if (data.uniqueCodeId !== session.user.id) {
			return NextResponse.json(
				{ error: tErrors("invalidUniqueCode") },
				{ status: 400 },
			);
		}

		// Check if the unique code already has a household associated with it
		const uniqueCodeId = Number.parseInt(data.uniqueCodeId, 10);
		const uniqueCode = await prisma.uniqueCode.findUnique({
			where: { id: uniqueCodeId },
			select: { householdId: true },
		});

		if (uniqueCode?.householdId) {
			return NextResponse.json(
				{
					error: tErrors("uniqueCodeAlreadyAssociated"),
				},
				{ status: 400 },
			);
		}

		// Convert census year ID to number
		const censusYearId = Number.parseInt(data.censusYearId, 10);

		// Start a transaction to ensure all operations succeed or fail together
		try {
			// 1. Create household
			let householdId;
			try {
				const household = await createHousehold({
					suburb: data.suburb,
					firstCensusYearId: censusYearId,
					lastCensusYearId: censusYearId,
				});
				householdId = household.id;
				if (process.env.NODE_ENV === "development") {
				}
			} catch (error) {
				if (process.env.NODE_ENV === "development") {
				}
				return NextResponse.json(
					{
						success: false,
						error: tErrors("failedToCreateHousehold"),
						details: getErrorMessage(error),
					},
					{ status: 500 },
				);
			}

			// 2. Create member (household head)
			let memberId;
			try {
				const member = await createMember({
					firstName: data.headFirstName,
					lastName: data.headLastName,
					gender: data.gender,
					mobilePhone: data.mobilePhone,
					dateOfBirth: data.headDateOfBirth,
					hobby: null, // Use null instead of undefined for SQL compatibility
					occupation: null, // Use null instead of undefined for SQL compatibility
				});
				memberId = member.id;
				if (process.env.NODE_ENV === "development") {
				}
			} catch (error) {
				if (process.env.NODE_ENV === "development") {
				}
				// Clean up the household we created
				try {
					await prisma.household.delete({ where: { id: householdId } });
				} catch (_cleanupError) {
					if (process.env.NODE_ENV === "development") {
					}
				}
				return NextResponse.json(
					{
						success: false,
						error: tErrors("failedToCreateHouseholdMember"),
						details: getErrorMessage(error),
					},
					{ status: 500 },
				);
			}

			// 3. Create household_member relationship
			try {
				await createHouseholdMember({
					householdId,
					memberId,
					relationship: "head",
					censusYearId,
					isCurrent: true,
				});
				if (process.env.NODE_ENV === "development") {
				}
			} catch (error) {
				if (process.env.NODE_ENV === "development") {
				}
				// Clean up the household and member we created
				try {
					await prisma.member.delete({ where: { id: memberId } });
					await prisma.household.delete({ where: { id: householdId } });
				} catch (_cleanupError) {
					if (process.env.NODE_ENV === "development") {
					}
				}
				return NextResponse.json(
					{
						success: false,
						error: tErrors("failedToCreateHouseholdMemberRelationship"),
						details: getErrorMessage(error),
					},
					{ status: 500 },
				);
			}

			// 4. Update unique_code with household_id
			try {
				await prisma.uniqueCode.update({
					where: { id: uniqueCodeId },
					data: { householdId },
				});
				if (process.env.NODE_ENV === "development") {
				}
			} catch (error) {
				if (process.env.NODE_ENV === "development") {
				}
				// We don't need to clean up here as the household and member are valid
				// The user can try again and the unique code will be updated
				return NextResponse.json(
					{
						success: false,
						error: tErrors("failedToUpdateUniqueCode"),
						details: getErrorMessage(error),
					},
					{ status: 500 },
				);
			}

			// Return success response with the new household ID
			return NextResponse.json({
				success: true,
				householdId,
				memberId,
			});
		} catch (error) {
			if (process.env.NODE_ENV === "development") {
			}
			return NextResponse.json(
				{
					success: false,
					error: tErrors("failedToRegisterHousehold"),
					details: getErrorMessage(error),
				},
				{ status: 500 },
			);
		}
	} catch (error) {
		if (process.env.NODE_ENV === "development") {
		}
		return NextResponse.json(
			{
				success: false,
				error: tErrors("errorOccurredDuringRegistration"),
				details: getErrorMessage(error),
			},
			{ status: 500 },
		);
	}
}
