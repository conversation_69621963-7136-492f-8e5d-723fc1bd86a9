import "next-auth";
import { DefaultSession } from "next-auth";

declare module "next-auth" {
  interface User {
    id: string;
    code: string;
    role: string;
    name: string;
    householdId: string | null;
    censusYearId: string;
  }

  interface Session {
    user: {
      id: string;
      code: string;
      role: string;
      name: string;
      householdId: string | null;
      censusYearId: string;
    } & DefaultSession["user"];
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string;
    code: string;
    role: string;
    name: string;
    householdId: string | null;
    censusYearId: string;
    accountDeleted?: boolean;
  }
}
