import { type NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { z } from "zod/v4";
import { censusAuthOptions } from "@/lib/census-auth/census-auth-options";
import { prisma } from "@/lib/db/prisma";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";

// Validation schema for suburb search - will be created dynamically with translations
const createSuburbSearchSchema = async (locale: "en" | "zh-CN") => {
	const t = await getTranslations({ locale, namespace: "validation" });
	return z.object({
		q: z
			.string()
			.min(2, { error: t("searchTermMinLength") })
			.max(50, { error: t("searchTermMaxLength") })
			.trim()
			// Permissive validation for Australian place names - allows letters, numbers, spaces,
			// apostrophes, hyphens, periods, parentheses, and ampersands
			// Examples: O'Connor, St. Kilda, Mt. Gravatt, Kings Cross, D'Aguilar Heights
			.regex(/^[a-zA-Z0-9\s\-'.&()]+$/, { error: t("invalidSearchCharacters") })
			// Additional security: prevent potential script injection patterns
			.refine((val) => !/<|>|script|javascript|onload|onerror/i.test(val), {
				error: t("invalidCharactersDetected"),
			}),
	});
};

// Interface for suburb search results (used in the API response)
// interface _SuburbResult {
//   id: number;
//   display_name: string;
//   suburb_name: string;
//   state_code: string;
// }

/**
 * GET /api/census/suburbs
 * Search for suburbs based on query parameter
 * Protected by census authentication middleware
 */
export async function GET(request: NextRequest) {
	try {
		// Authentication is handled by middleware, but we can still check session for additional validation
		const session = await getServerSession(censusAuthOptions);

		if (!session) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Get locale for translations
		const locale = await getLocaleFromCookies();
		const tErrors = await getTranslations({ locale, namespace: "errors" });

		// Get search query from URL parameters
		const { searchParams } = new URL(request.url);
		const searchQuery = searchParams.get("q");

		if (!searchQuery) {
			return NextResponse.json(
				{
					error: 'Search query parameter "q" is required',
				},
				{ status: 400 },
			);
		}

		// Validate search query
		let validatedQuery: string;
		try {
			const suburbSearchSchema = await createSuburbSearchSchema(locale);
			const result = suburbSearchSchema.parse({ q: searchQuery });
			validatedQuery = result.q; // This is already trimmed and validated
		} catch (validationError) {
			if (validationError instanceof z.ZodError) {
				// Log the validation error in development for debugging
				if (process.env.NODE_ENV === "development") {
				}

				return NextResponse.json(
					{
						error: tErrors("invalidSearchQuery"),
						details: validationError.issues[0].message,
					},
					{ status: 400 },
				);
			}
			return NextResponse.json(
				{
					error: tErrors("invalidSearchQuery"),
					details: tErrors("pleaseCheckYourInput"),
				},
				{ status: 400 },
			);
		}

		// Search suburbs using Prisma with comprehensive search
		const suburbs =
			(await prisma.suburb
				?.findMany({
					where: {
						OR: [
							{
								displayName: { contains: validatedQuery, mode: "insensitive" },
							},
							{ searchText: { contains: validatedQuery, mode: "insensitive" } },
							{ suburbName: { contains: validatedQuery, mode: "insensitive" } },
						],
					},
					select: {
						id: true,
						displayName: true,
						suburbName: true,
						stateCode: true,
					},
					take: 50,
					orderBy: [{ displayName: "asc" }],
				})
				.catch(() => [])) || [];

		// Transform to match expected format
		const transformedSuburbs = suburbs.map((suburb) => ({
			id: suburb.id,
			displayName: suburb.displayName,
			suburbName: suburb.suburbName,
			stateCode: suburb.stateCode,
		}));

		// Log search activity in development
		if (process.env.NODE_ENV === "development") {
		}

		return NextResponse.json({
			success: true,
			results: transformedSuburbs,
			count: transformedSuburbs.length,
			query: searchQuery,
		});
	} catch (error) {
		// Return user-friendly error message
		return NextResponse.json(
			{
				error: "Failed to search suburbs",
				details:
					process.env.NODE_ENV === "development"
						? error instanceof Error
							? error.message
							: "Unknown error"
						: "Please try again later",
			},
			{ status: 500 },
		);
	}
}
