{"$schema": "https://biomejs.dev/schemas/2.1.2/schema.json", "extends": ["ultracite"], "files": {"ignoreUnknown": false, "includes": ["**/*.js", "**/*.jsx", "**/*.ts", "**/*.tsx", "**/*.json"], "experimentalScannerIgnores": [".next/**", "node_modules/**", "out/**", "build/**", "dist/**", "coverage/**", "public/**", "*.generated.*", "new_server/**", "test/**", "ssh/**", "backups/**", "guide/**", ".trae/**", ".vercel/**"]}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 80}, "linter": {"enabled": true, "rules": {"recommended": true, "suspicious": {"noConsole": "off", "noArrayIndexKey": "warn"}, "style": {"noNegationElse": "off"}, "correctness": {"useExhaustiveDependencies": "warn"}}}, "javascript": {"formatter": {"quoteStyle": "single", "jsxQuoteStyle": "double", "trailingCommas": "es5", "semicolons": "always"}}}