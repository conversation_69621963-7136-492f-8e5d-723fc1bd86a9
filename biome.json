{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "extends": ["ultracite"], "files": {"ignore": [".next/**", "node_modules/**", "out/**", "build/**", "dist/**", "coverage/**", "public/**", "*.generated.*", "new_server/**", "test/**", "ssh/**", "backups/**", "guide/**", ".trae/**", ".vercel/**"]}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 80}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "suspicious": {"noConsoleLog": "off", "noArrayIndexKey": "warn"}, "style": {"noNegationElse": "off"}, "correctness": {"useExhaustiveDependencies": "warn"}}}, "javascript": {"formatter": {"quoteStyle": "single", "jsxQuoteStyle": "double", "trailingCommas": "es5", "semicolons": "always"}}, "json": {"formatter": {"enabled": true}}}