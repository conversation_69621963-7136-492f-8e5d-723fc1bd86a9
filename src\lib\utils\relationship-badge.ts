/**
 * Utility functions for member badge styling
 * Ensures consistent relationship and gender badge colours across the application
 */

export type RelationshipBadgeVariant = "info" | "success" | "default" | "warning";

/**
 * Get the StatusBadge variant for a relationship type
 * This ensures consistency between census portal and admin portal
 *
 * @param relationship - The relationship type (can be null/undefined)
 * @returns The StatusBadge variant to use
 */
export function getRelationshipBadgeVariant(
  relationship: string | null | undefined,
): RelationshipBadgeVariant {
  if (!relationship) {
    return "default"; // Primary for null/undefined
  }

  switch (relationship) {
    case "head":
      return "info"; // Blue
    case "spouse":
      return "success"; // Green
    case "child":
      return "default"; // Primary
    case "parent":
      return "warning"; // Yellow
    case "relative":
      return "warning"; // Yellow
    case "other":
      return "default"; // Primary
    default:
      return "default"; // Primary
  }
}

/**
 * Get the Badge className for a gender type
 * This ensures consistent gender badge colours across the application
 *
 * @param gender - The gender type (can be null/undefined)
 * @returns The Badge className to use
 */
export function getGenderBadgeColor(gender: string | null | undefined): string {
  if (!gender) {
    return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
  }

  switch (gender) {
    case "male":
      return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
    case "female":
      return "bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300";
    case "other":
      return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
  }
}
