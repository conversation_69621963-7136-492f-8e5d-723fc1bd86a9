"use client";

import { useRouter } from "next/navigation";
import { signIn } from "next-auth/react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/forms/common/Form";
import { FormField } from "@/components/forms/common/FormField";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useMessage } from "@/hooks/useMessage";
import { zodResolver } from "@/lib/utils/zod-resolver-compat";
import {
  type ClientLoginFormValues,
  type ClientTotpFormValues,
  createClientLoginSchema,
  createClientTotpSchema,
} from "@/lib/validation/client/auth-client";

export function AdminLoginForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [showTotpForm, setShowTotpForm] = useState(false);
  const [username, setUsername] = useState("");
  const router = useRouter();
  const { showInfo, showError, showSuccess } = useMessage();
  const t = useTranslations("auth");
  const tForms = useTranslations("forms");
  const tAdmin = useTranslations("admin");
  const tValidation = useTranslations("validation");

  // Create client-side validation schemas with translations
  const loginSchema = createClientLoginSchema(tValidation);
  const totpSchema = createClientTotpSchema(tValidation);

  const loginForm = useForm<ClientLoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: "",
      password: "",
    },
  });

  const totpForm = useForm<ClientTotpFormValues>({
    resolver: zodResolver(totpSchema),
    defaultValues: {
      token: "",
    },
  });

  const onLoginSubmit = async (data: ClientLoginFormValues) => {
    setIsLoading(true);
    try {
      const result = await signIn("credentials", {
        username: data.username,
        password: data.password,
        redirect: false,
      });

      if (result?.error === "TotpRequired") {
        setUsername(data.username);
        setShowTotpForm(true);
        showInfo("twoFactorRequired");
      } else if (result?.error) {
        showError("loginFailed", "auth");
      } else if (result?.ok) {
        showSuccess("loginSuccessful");
        // Add a small delay to allow the alert to show
        setTimeout(() => {
          router.push("/admin/dashboard");
        }, 1000);
      } else {
        showError("unknownLoginError", "auth");
      }
    } catch (error) {
      showError("errorDuringLogin", "auth");
      console.error("Login error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const onTotpSubmit = async (data: ClientTotpFormValues) => {
    setIsLoading(true);
    try {
      const result = await signIn("credentials", {
        username,
        totp: data.token,
        redirect: false,
      });

      if (result?.error) {
        showError("invalidTotpToken", "auth");
      } else {
        router.push("/admin/dashboard");
        showSuccess("loginSuccessful");
      }
    } catch (error) {
      showError("verificationError", "auth");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md border-none shadow-lg">
      <CardHeader className="space-y-1">
        <CardTitle className="text-center font-bold text-2xl">{t("adminLogin")}</CardTitle>
        <CardDescription className="text-center">{t("signInToAccessDashboard")}</CardDescription>
      </CardHeader>
      <CardContent>
        {showTotpForm ? (
          <div className="space-y-4">
            <Form
              form={totpForm}
              isLoading={isLoading}
              onSubmit={onTotpSubmit}
              submitText={tAdmin("verify")}
            >
              <FormField
                error={totpForm.formState.errors.token}
                id="token"
                label={t("twoFactorAuthCode")}
                placeholder={t("enterSixDigitCode")}
                register={totpForm.register}
                required
              />
            </Form>
            <Button
              className="w-full"
              onClick={() => setShowTotpForm(false)}
              type="button"
              variant="outline"
            >
              {t("backToLogin")}
            </Button>
          </div>
        ) : (
          <Form
            form={loginForm}
            isLoading={isLoading}
            onSubmit={onLoginSubmit}
            submitText={t("signIn")}
          >
            <FormField
              error={loginForm.formState.errors.username}
              id="username"
              label={t("username")}
              placeholder={tForms("enterUsername")}
              register={loginForm.register}
              required
            />
            <FormField
              error={loginForm.formState.errors.password}
              id="password"
              label={t("password")}
              placeholder={tForms("enterPassword")}
              register={loginForm.register}
              required
              type="password"
            />
          </Form>
        )}
      </CardContent>
    </Card>
  );
}
