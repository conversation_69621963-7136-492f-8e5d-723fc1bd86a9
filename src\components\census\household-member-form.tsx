"use client";

import { Plus, Trash2, X } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { Form } from "@/components/forms/common/Form";
import { FormField } from "@/components/forms/common/FormField";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { DatePicker } from "@/components/ui/date-picker";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { useMessage } from "@/hooks/useMessage";
import { zodResolver } from "@/lib/utils/zod-resolver-compat";
import {
  type ClientCombinedMemberSacramentFormValues,
  createClientCombinedMemberSacramentSchema,
} from "@/lib/validation/client/census-client";

interface SacramentType {
  id: number;
  code: string;
  name: string;
  description?: string;
}

interface HouseholdMemberFormProps {
  initialData?: ClientCombinedMemberSacramentFormValues;
  onSubmit: (data: ClientCombinedMemberSacramentFormValues) => Promise<void>;
  onCancel: () => void;
  sacramentTypes: SacramentType[];
  isEdit?: boolean;
}

export function HouseholdMemberForm({
  initialData,
  onSubmit,
  onCancel,
  sacramentTypes,
  isEdit = false,
}: HouseholdMemberFormProps) {
  const { showError, showInfo, showDirect } = useMessage();
  const t = useTranslations("census");
  const tCommon = useTranslations("common");
  const tForms = useTranslations("forms");
  const tValidation = useTranslations("validation");
  const tGenders = useTranslations("genders");
  const tRelationships = useTranslations("relationships");
  const tSacraments = useTranslations("sacraments");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [sacramentAccordionValue, setSacramentAccordionValue] = useState<string>("");
  const [selectedSacramentTypeIds, setSelectedSacramentTypeIds] = useState<number[]>(() => {
    return (
      (initialData?.sacraments
        ?.map((s) => s.sacramentTypeId)
        .filter((id) => id !== undefined) as number[]) || []
    );
  });

  // Create client-side validation schema with translations
  const clientSchema = createClientCombinedMemberSacramentSchema(tValidation);

  const form = useForm<ClientCombinedMemberSacramentFormValues>({
    resolver: zodResolver(clientSchema),
    defaultValues: initialData || {
      firstName: "",
      lastName: "",
      dateOfBirth: undefined,
      gender: undefined,
      mobilePhone: "",
      hobby: "",
      occupation: "",
      relationship: undefined,
      sacraments: [],
    },
  });

  const addSacrament = () => {
    const currentSacraments = form.getValues("sacraments") || [];
    if (currentSacraments.length >= sacramentTypes.length) {
      showInfo("cannotAddMoreSacraments");
      return;
    }
    const allTypesSelected = sacramentTypes.every((type) =>
      selectedSacramentTypeIds.includes(type.id),
    );
    if (allTypesSelected) {
      showInfo("allSacramentTypesAdded");
      return;
    }
    form.setValue("sacraments", [
      ...currentSacraments,
      {
        sacramentTypeId: 0,
        date: null,
        place: "",
      },
    ]);
    setSacramentAccordionValue(`sacrament-${currentSacraments.length}`);
  };

  const removeSacrament = (index: number) => {
    const currentSacraments = form.getValues("sacraments") || [];
    const removedSacrament = currentSacraments[index];
    if (removedSacrament && removedSacrament.sacramentTypeId !== undefined) {
      setSelectedSacramentTypeIds((prev) =>
        prev.filter((id) => id !== removedSacrament.sacramentTypeId),
      );
    }
    form.setValue(
      "sacraments",
      currentSacraments.filter((_, i) => i !== index),
    );
  };

  const handleSacramentDateSelect = (date: Date | null, index: number) => {
    const currentSacraments = form.getValues("sacraments") || [];
    if (currentSacraments[index]) {
      // Set the date value directly
      if (date) {
        currentSacraments[index].date = date;
      } else {
        // If date is null, we can use an empty string which the schema will handle
        delete currentSacraments[index].date;
      }
      form.setValue("sacraments", currentSacraments, { shouldValidate: true });
    }
  };

  const handleSubmit = async (data: ClientCombinedMemberSacramentFormValues) => {
    try {
      setIsSubmitting(true);
      if (!data.firstName) {
        showDirect("error", tValidation("firstNameRequired"));
        setIsSubmitting(false);
        return;
      }
      if (!data.lastName) {
        showDirect("error", tValidation("lastNameRequired"));
        setIsSubmitting(false);
        return;
      }
      if (!data.gender) {
        showDirect("error", tValidation("genderRequired"));
        setIsSubmitting(false);
        return;
      }
      if (!data.mobilePhone) {
        showDirect("error", tValidation("mobileRequired"));
        setIsSubmitting(false);
        return;
      }
      if (!data.relationship) {
        showDirect("error", tValidation("relationshipRequired"));
        setIsSubmitting(false);
        return;
      }
      if (data.sacraments && data.sacraments.length > 0) {
        for (let i = 0; i < data.sacraments.length; i++) {
          const sacrament = data.sacraments[i];
          if (!sacrament.sacramentTypeId || sacrament.sacramentTypeId === 0) {
            showDirect(
              "error",
              tValidation("sacramentTypeRequired", {
                number: (i + 1).toString(),
              }),
            );
            setIsSubmitting(false);
            return;
          }
        }
      }
      console.log("Submitting combined household member form data:", data);
      await onSubmit(data);
    } catch (error) {
      console.error("Error submitting household member form:", error);
      showError("failedToSaveHouseholdMember");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDateOfBirthSelect = (date: Date | null) => {
    // The schema uses z.coerce.date() which can handle null values
    // We need to pass the date directly to match the schema's expectations
    if (date) {
      form.setValue("dateOfBirth", date, { shouldValidate: true });
    } else {
      // If date is null, set it to a default date that will be validated by the schema
      const defaultDate = new Date();
      form.setValue("dateOfBirth", defaultDate, { shouldValidate: true });
    }
  };

  const getAvailableSacramentTypes = (currentIndex: number): SacramentType[] => {
    const currentSacramentTypeId = form.getValues("sacraments")?.[currentIndex]?.sacramentTypeId;
    return sacramentTypes.filter(
      (type) => !selectedSacramentTypeIds.includes(type.id) || type.id === currentSacramentTypeId,
    );
  };

  const canAddMoreSacraments =
    (form.getValues("sacraments") || []).length < sacramentTypes.length &&
    !sacramentTypes.every((type) => selectedSacramentTypeIds.includes(type.id));

  return (
    <div className="rounded-lg border bg-card p-6 shadow-sm">
      <div className="mb-4 flex items-center justify-between">
        <h2 className="font-semibold text-xl">
          {isEdit ? "Edit Household Member" : "Add Household Member"}
        </h2>
        <Button onClick={onCancel} size="icon" variant="ghost">
          <X className="h-4 w-4" />
        </Button>
      </div>

      <Separator className="my-4" />

      <Form
        className="space-y-6"
        form={form}
        isLoading={isSubmitting}
        onSubmit={handleSubmit}
        submitText={isEdit ? t("updateMember") : t("addMember")}
      >
        <div className="space-y-4">
          <h3 className="font-medium text-md">{t("personalInformation")}</h3>
          <div className="grid gap-4 sm:grid-cols-2">
            <FormField
              error={form.formState.errors.firstName}
              id="firstName"
              label={t("firstName")}
              placeholder={t("enterFirstName")}
              register={form.register}
              required
            />
            <FormField
              error={form.formState.errors.lastName}
              id="lastName"
              label={t("lastName")}
              placeholder={t("enterLastName")}
              register={form.register}
              required
            />
          </div>
          <div className="grid gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <Label className="font-medium text-sm" htmlFor="dateOfBirth">
                Date of Birth<span className="ml-1 text-destructive">*</span>
              </Label>
              <DatePicker
                className={form.formState.errors.dateOfBirth ? "border-destructive" : ""}
                date={
                  form.getValues("dateOfBirth") instanceof Date
                    ? (form.getValues("dateOfBirth") as Date)
                    : null
                }
                placeholderText="Select date of birth"
                preventFutureDates={true}
                setDate={handleDateOfBirthSelect}
              />
              {form.formState.errors.dateOfBirth && (
                <p className="text-destructive text-xs">
                  {form.formState.errors.dateOfBirth.message}
                </p>
              )}
            </div>
            <FormField
              error={form.formState.errors.mobilePhone}
              id="mobilePhone"
              label={tCommon("mobilePhone")}
              placeholder={tForms("enterMobileNumberPlaceholder")}
              register={form.register}
              required
            />
          </div>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div className="col-span-1 md:col-span-1 lg:col-span-2">
              <FormField
                error={form.formState.errors.hobby}
                id="hobby"
                label={tForms("hobby")}
                placeholder={tForms("enterHobby")}
                register={form.register}
              />
            </div>
            <div className="col-span-1 md:col-span-1 lg:col-span-1">
              <div className="space-y-2">
                <Label className="font-medium text-sm" htmlFor="gender">
                  {tCommon("gender")}
                  <span className="ml-1 text-destructive">*</span>
                </Label>
                <Select
                  onValueChange={(value) =>
                    form.setValue("gender", value as "male" | "female" | "other", {
                      shouldValidate: true,
                    })
                  }
                  value={form.getValues("gender")}
                >
                  <SelectTrigger
                    className={form.formState.errors.gender ? "border-destructive" : ""}
                    id="gender"
                  >
                    <SelectValue placeholder={tForms("selectGender")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="male">{tGenders("male")}</SelectItem>
                    <SelectItem value="female">{tGenders("female")}</SelectItem>
                    <SelectItem value="other">{tGenders("other")}</SelectItem>
                  </SelectContent>
                </Select>
                {form.formState.errors.gender && (
                  <p className="text-destructive text-xs">{form.formState.errors.gender.message}</p>
                )}
              </div>
            </div>
            <div className="col-span-1 md:col-span-1 lg:col-span-1">
              <div className="space-y-2">
                <Label className="font-medium text-sm" htmlFor="relationship">
                  {tForms("relationshipToHousehold")}
                  <span className="ml-1 text-destructive">*</span>
                </Label>
                <Select
                  disabled={isEdit && initialData?.relationship === "head"}
                  onValueChange={(value) =>
                    form.setValue(
                      "relationship",
                      value as "head" | "spouse" | "child" | "parent" | "relative" | "other",
                      { shouldValidate: true },
                    )
                  }
                  value={form.getValues("relationship")}
                >
                  <SelectTrigger
                    className={form.formState.errors.relationship ? "border-destructive" : ""}
                    id="relationship"
                  >
                    <SelectValue placeholder={tForms("selectRelationship")} />
                  </SelectTrigger>
                  <SelectContent>
                    {!isEdit && <SelectItem value="head">{tRelationships("head")}</SelectItem>}
                    <SelectItem value="spouse">{tRelationships("spouse")}</SelectItem>
                    <SelectItem value="child">{tRelationships("child")}</SelectItem>
                    <SelectItem value="parent">{tRelationships("parent")}</SelectItem>
                    <SelectItem value="relative">{tForms("relative")}</SelectItem>
                    <SelectItem value="other">{tRelationships("other")}</SelectItem>
                  </SelectContent>
                </Select>
                {form.formState.errors.relationship && (
                  <p className="text-destructive text-xs">
                    {form.formState.errors.relationship.message}
                  </p>
                )}
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4">
            <FormField
              error={form.formState.errors.occupation}
              id="occupation"
              label={tForms("occupation")}
              placeholder={tForms("enterOccupationPlaceholder")}
              register={form.register}
            />
          </div>
        </div>

        <div className="mt-6 space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-md">Sacraments</h3>
            <Button
              disabled={!canAddMoreSacraments}
              onClick={addSacrament}
              size="sm"
              type="button"
              variant="outline"
            >
              <Plus className="mr-2 h-4 w-4" /> Add Sacrament
            </Button>
          </div>

          {form.getValues("sacraments")?.length === 0 ? (
            <div className="rounded-md bg-muted/30 py-4 text-center">
              <p className="text-muted-foreground">No sacraments added yet.</p>
              <p className="mt-1 text-muted-foreground text-sm">
                Click &quot;Add Sacrament&quot; to record a sacrament.
              </p>
            </div>
          ) : (
            <Accordion
              className="space-y-2"
              collapsible
              onValueChange={setSacramentAccordionValue}
              type="single"
              value={sacramentAccordionValue}
            >
              {form.getValues("sacraments")?.map((sacrament, index) => (
                <AccordionItem
                  className="overflow-hidden rounded-md border"
                  key={index}
                  value={`sacrament-${index}`}
                >
                  <div className="flex items-center justify-between px-4">
                    <AccordionTrigger className="flex-1 py-2">
                      {sacrament.sacramentTypeId
                        ? sacramentTypes.find((type) => type.id === sacrament.sacramentTypeId)
                            ?.name || "Sacrament"
                        : `Sacrament #${index + 1}`}
                    </AccordionTrigger>
                    <Button
                      className="h-8 w-8"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeSacrament(index);
                      }}
                      size="icon"
                      type="button"
                      variant="ghost"
                    >
                      <Trash2 className="h-4 w-4 text-destructive" />
                    </Button>
                  </div>
                  <AccordionContent className="px-4 pb-4">
                    <div className="space-y-4">
                      <div className="grid gap-4 sm:grid-cols-2">
                        <div className="space-y-2">
                          <Label
                            className="font-medium text-sm"
                            htmlFor={`sacraments.${index}.sacramentTypeId`}
                          >
                            Sacrament Type
                            <span className="ml-1 text-destructive">*</span>
                          </Label>
                          <Select
                            onValueChange={(value) => {
                              const newSacramentTypeId = Number.parseInt(value);
                              const oldSacramentTypeId = form.getValues(
                                `sacraments.${index}.sacramentTypeId`,
                              );
                              form.setValue(
                                `sacraments.${index}.sacramentTypeId`,
                                newSacramentTypeId,
                                { shouldValidate: true },
                              );
                              setSelectedSacramentTypeIds((prev) => {
                                const newSelected = prev.filter((id) => id !== oldSacramentTypeId);
                                if (newSacramentTypeId !== undefined && newSacramentTypeId !== 0) {
                                  newSelected.push(newSacramentTypeId);
                                }
                                return newSelected;
                              });
                            }}
                            value={sacrament.sacramentTypeId?.toString()}
                          >
                            <SelectTrigger
                              className={
                                form.formState.errors.sacraments?.[index]?.sacramentTypeId
                                  ? "border-destructive"
                                  : ""
                              }
                              id={`sacraments.${index}.sacramentTypeId`}
                            >
                              <SelectValue placeholder={tForms("selectSacramentType")} />
                            </SelectTrigger>
                            <SelectContent>
                              {getAvailableSacramentTypes(index).map((type) => (
                                <SelectItem key={type.id} value={type.id.toString()}>
                                  {tSacraments(type.code as any)}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          {form.formState.errors.sacraments?.[index]?.sacramentTypeId && (
                            <p className="text-destructive text-xs">
                              {form.formState.errors.sacraments[index].sacramentTypeId.message}
                            </p>
                          )}
                        </div>
                        <div className="space-y-2">
                          <Label
                            className="font-medium text-sm"
                            htmlFor={`sacraments.${index}.date`}
                          >
                            Date
                          </Label>
                          <DatePicker
                            className={
                              form.formState.errors.sacraments?.[index]?.date
                                ? "border-destructive"
                                : ""
                            }
                            date={sacrament.date instanceof Date ? sacrament.date : null}
                            placeholderText="Select date"
                            preventFutureDates={true}
                            setDate={(date) => handleSacramentDateSelect(date, index)}
                          />
                          {form.formState.errors.sacraments?.[index]?.date && (
                            <p className="text-destructive text-xs">
                              {form.formState.errors.sacraments[index].date.message}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label
                          className="font-medium text-sm"
                          htmlFor={`sacraments.${index}.place`}
                        >
                          Place
                        </Label>
                        <input
                          className={`flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:font-medium file:text-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 ${
                            form.formState.errors.sacraments?.[index]?.place
                              ? "border-destructive"
                              : ""
                          }`}
                          id={`sacraments.${index}.place`}
                          onChange={(e) => {
                            const currentSacraments = form.getValues("sacraments") || [];
                            if (currentSacraments[index]) {
                              currentSacraments[index].place = e.target.value;
                              form.setValue("sacraments", currentSacraments, {
                                shouldValidate: true,
                              });
                            }
                          }}
                          placeholder={tForms("enterPlaceEgChurchName")}
                          value={sacrament.place || ""}
                        />
                        {form.formState.errors.sacraments?.[index]?.place && (
                          <p className="text-destructive text-xs">
                            {form.formState.errors.sacraments[index].place.message}
                          </p>
                        )}
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          )}
        </div>
      </Form>
    </div>
  );
}
