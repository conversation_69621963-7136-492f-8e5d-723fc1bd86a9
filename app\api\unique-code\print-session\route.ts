import { jwtVerify, SignJWT } from "jose";
import { type NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { z } from "zod/v4";
import { authOptions } from "@/lib/auth/auth-options";
import { prisma } from "@/lib/db/prisma";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";

// Cookie name for print session
const PRINT_SESSION_COOKIE = "print_session_token";

// Session TTL in seconds (5 minutes)
const SESSION_TTL = 5 * 60; // 5 minutes

// Validation schema for creating a print session
const createPrintSessionSchema = z.object({
  codeIds: z.array(z.number()),
});

/**
 * Create a JWT token for print session
 * @param codeIds Array of code IDs to include in the session
 * @returns JWT token string
 */
async function createPrintSessionToken(codeIds: number[]): Promise<string> {
  // Get the secret key from environment variables
  const secret = new TextEncoder().encode(
    process.env.NEXTAUTH_SECRET_ADMIN || "fallback-secret-do-not-use-in-production",
  );

  // Create a JWT token with the code IDs
  const token = await new SignJWT({ codeIds })
    .setProtectedHeader({ alg: "HS256" })
    .setIssuedAt()
    .setExpirationTime(`${SESSION_TTL}s`)
    .sign(secret);

  return token;
}

/**
 * Verify and decode a JWT token for print session
 * @param token JWT token string
 * @returns Array of code IDs if valid, null otherwise
 */
async function verifyPrintSessionToken(token: string): Promise<number[] | null> {
  try {
    // Get the secret key from environment variables
    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET_ADMIN || "fallback-secret-do-not-use-in-production",
    );

    // Verify the JWT token
    const { payload } = await jwtVerify(token, secret);

    // Check if the payload contains code IDs
    if (!(payload.codeIds && Array.isArray(payload.codeIds))) {
      return null;
    }

    return payload.codeIds as number[];
  } catch (_error) {
    return null;
  }
}

/**
 * POST endpoint to create a new print session
 */
export async function POST(request: NextRequest) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const tAdmin = await getTranslations({ locale, namespace: "admin" });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require admin authentication
    if (!session || session.user.role !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse and validate request body
    const data = await request.json();

    try {
      const validatedData = createPrintSessionSchema.parse(data);

      // Validate code IDs (max 100)
      if (validatedData.codeIds.length === 0) {
        return NextResponse.json({ error: tAdmin("noCodeIdsProvided") }, { status: 400 });
      }

      if (validatedData.codeIds.length > 100) {
        return NextResponse.json({ error: tAdmin("maxCodesPerPrintSession") }, { status: 400 });
      }

      // Create a JWT token with the code IDs
      const token = await createPrintSessionToken(validatedData.codeIds);

      // Create response
      const response = NextResponse.json({
        success: true,
      });

      // Set the JWT token in an HTTP-only cookie
      response.cookies.set(PRINT_SESSION_COOKIE, token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        maxAge: SESSION_TTL, // 5 minutes
        path: "/",
        sameSite: "lax",
      });

      return response;
    } catch (validationError) {
      if (validationError instanceof z.ZodError) {
        return NextResponse.json(
          { error: tAdmin("validationError"), details: validationError.issues },
          { status: 400 },
        );
      }
      throw validationError;
    }
  } catch (_error) {
    return NextResponse.json({ error: tAdmin("failedToCreatePrintSession") }, { status: 500 });
  }
}

/**
 * GET endpoint to retrieve code IDs from a print session
 */
export async function GET(request: NextRequest) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const tErrors = await getTranslations({ locale, namespace: "errors" });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require admin authentication
    if (!session || session.user.role !== "admin") {
      return NextResponse.json({ error: tErrors("unauthorized") }, { status: 401 });
    }

    // Get JWT token from cookie
    const token = request.cookies.get(PRINT_SESSION_COOKIE)?.value;

    // If we don't have a token, return a user-friendly error
    if (!token) {
      return NextResponse.json(
        {
          error: tErrors("noPrintSessionFound"),
          details: tErrors("selectCodesToPrint"),
        },
        { status: 404 },
      );
    }

    // Verify and decode the JWT token
    const codeIds = await verifyPrintSessionToken(token);

    if (!codeIds) {
      return NextResponse.json(
        {
          error: tErrors("printSessionExpired"),
          details: tErrors("printSessionExpiredDetails"),
        },
        { status: 404 },
      );
    }

    // If there are no code IDs, return an error
    if (codeIds.length === 0) {
      return NextResponse.json(
        {
          error: tErrors("noCodesFound"),
          details: tErrors("noCodesFoundDetails"),
        },
        { status: 404 },
      );
    }

    // Fetch the codes using Prisma - SECURITY: Only select non-sensitive fields
    const uniqueCodes = await prisma.uniqueCode.findMany({
      where: {
        id: { in: codeIds },
      },
      select: {
        id: true,
        code: true,
        isAssigned: true,
        assignedAt: true,
        householdId: true,
        censusYearId: true,
        createdAt: true,
        updatedAt: true,
        // SECURITY: Explicitly exclude validation fields (validationStart, validationEnd, validationHash)
        censusYear: {
          select: {
            year: true,
            isActive: true,
          },
        },
      },
      orderBy: { id: "asc" },
    });

    // Transform to match expected format
    const transformedCodes = uniqueCodes.map((uc) => ({
      id: uc.id,
      code: uc.code,
      isAssigned: uc.isAssigned,
      assignedAt: uc.assignedAt,
      householdId: uc.householdId,
      censusYearId: uc.censusYearId,
      createdAt: uc.createdAt,
      updatedAt: uc.updatedAt,
      census_year: uc.censusYear?.year,
      is_active_year: uc.censusYear?.isActive,
    }));

    // Create the response
    const response = NextResponse.json({
      success: true,
      data: transformedCodes,
    });

    // Clear the cookie after successful retrieval
    response.cookies.set(PRINT_SESSION_COOKIE, "", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      maxAge: 0, // Expire immediately
      path: "/",
      sameSite: "lax",
    });

    return response;
  } catch (_error) {
    if (process.env.NODE_ENV === "development") {
    }
    return NextResponse.json(
      {
        error: tErrors("failedToRetrievePrintSession"),
        details: tErrors("printSessionRetrievalError"),
      },
      { status: 500 },
    );
  }
}
