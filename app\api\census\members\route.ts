import { type NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { censusAuthOptions } from "@/lib/census-auth/census-auth-options";
import {
  createHouseholdMember,
  getHouseholdMemberWithDetails,
  updateHouseholdMember,
} from "@/lib/db/household-members";
import {
  createMember,
  deleteMemberWithChecks,
  HouseholdHeadDeletionError,
  updateMember,
} from "@/lib/db/members";
import { prisma } from "@/lib/db/prisma";
import { getZodErrorDetails } from "@/lib/utils/error-handling";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";
import { createHouseholdMemberSchema } from "@/lib/validation/census-form";

/**
 * GET /api/census/members
 *
 * Fetches all members for the current household
 */
export async function GET(_request: NextRequest) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const t = await getTranslations({ locale, namespace: "errors" });

  try {
    // Check if user is authenticated
    const session = await getServerSession(censusAuthOptions);

    if (!session) {
      return NextResponse.json({ error: t("unauthorized") }, { status: 401 });
    }

    // Check if user has the household role
    if (session.user.role !== "household") {
      return NextResponse.json({ error: t("forbidden") }, { status: 403 });
    }

    // Check if household ID exists in session
    if (!session.user.householdId) {
      return NextResponse.json(
        {
          error: t("householdNotRegistered"),
          message: t("pleaseCompleteHouseholdRegistrationFirst"),
        },
        { status: 400 },
      );
    }

    const householdId = Number.parseInt(session.user.householdId, 10);
    const censusYearId = Number.parseInt(session.user.censusYearId, 10);

    // Get all members for this household
    const members = await getHouseholdMemberWithDetails(householdId, censusYearId);

    return NextResponse.json(members);
  } catch (error) {
    return NextResponse.json(
      {
        error: t("failedToFetchHouseholdMembers"),
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

/**
 * POST /api/census/members
 *
 * Creates a new household member
 */
export async function POST(request: NextRequest) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const t = await getTranslations({ locale, namespace: "errors" });
  const tNotifications = await getTranslations({
    locale,
    namespace: "notifications",
  });

  try {
    // Check if user is authenticated
    const session = await getServerSession(censusAuthOptions);

    if (!session) {
      return NextResponse.json({ error: t("unauthorized") }, { status: 401 });
    }

    // Check if user has the household role
    if (session.user.role !== "household") {
      return NextResponse.json({ error: t("forbidden") }, { status: 403 });
    }

    // Check if household ID exists in session
    if (!session.user.householdId) {
      return NextResponse.json(
        {
          error: t("householdNotRegistered"),
          message: t("pleaseCompleteHouseholdRegistrationFirst"),
        },
        { status: 400 },
      );
    }

    // Parse request body
    const body = await request.json();

    // Log request for debugging (development only, no personal data)
    if (process.env.NODE_ENV === "development") {
    }

    // Validate request body with translations
    const addHouseholdMemberSchema = await createHouseholdMemberSchema(locale);
    const validationResult = addHouseholdMemberSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: t("invalidRequestData"),
          details: getZodErrorDetails(validationResult.error),
        },
        { status: 400 },
      );
    }

    const data = validationResult.data;
    const householdId = Number.parseInt(session.user.householdId, 10);
    const censusYearId = Number.parseInt(session.user.censusYearId, 10);

    // Create the member
    const member = await createMember({
      firstName: data.firstName,
      lastName: data.lastName,
      dateOfBirth: data.dateOfBirth,
      gender: data.gender,
      mobilePhone: data.mobilePhone,
      hobby: data.hobby || null, // Use null instead of undefined for SQL compatibility
      occupation: data.occupation || null, // Use null instead of undefined for SQL compatibility
    });

    // Create the household member relationship
    await createHouseholdMember({
      householdId,
      memberId: member.id,
      relationship: data.relationship,
      censusYearId,
      isCurrent: true,
    });

    // Update census form status to in_progress if it's not already
    await prisma.censusForm.upsert({
      where: {
        householdId_censusYearId: {
          householdId,
          censusYearId,
        },
      },
      update: {
        status: "in_progress",
        lastUpdated: new Date(),
      },
      create: {
        householdId,
        censusYearId,
        status: "in_progress",
        lastUpdated: new Date(),
      },
    });

    // Get the created member with details
    const createdMember = await getHouseholdMemberWithDetails(householdId, censusYearId);
    const newMember = createdMember.find((m) => m.memberId === member.id);

    return NextResponse.json({
      success: true,
      message: tNotifications("memberAddedSuccessfully"),
      member: newMember,
    });
  } catch (error) {
    return NextResponse.json(
      {
        error: t("failedToCreateHouseholdMember"),
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

/**
 * PUT /api/census/members/:id
 *
 * Updates an existing household member
 */
export async function PUT(request: NextRequest) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const t = await getTranslations({ locale, namespace: "errors" });
  const tNotifications = await getTranslations({
    locale,
    namespace: "notifications",
  });

  try {
    // Check if user is authenticated
    const session = await getServerSession(censusAuthOptions);

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has the household role
    if (session.user.role !== "household") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Check if household ID exists in session
    if (!session.user.householdId) {
      return NextResponse.json(
        {
          error: t("householdNotRegistered"),
          message: t("pleaseCompleteHouseholdRegistrationFirst"),
        },
        { status: 400 },
      );
    }

    // Parse request body
    const body = await request.json();

    // Log request for debugging (development only, no personal data)
    if (process.env.NODE_ENV === "development") {
    }

    // Validate request body with translations
    const updateHouseholdMemberSchema = await createHouseholdMemberSchema(locale);
    const validationResult = updateHouseholdMemberSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: t("invalidRequestData"),
          details: getZodErrorDetails(validationResult.error),
        },
        { status: 400 },
      );
    }

    const data = validationResult.data;
    const householdId = Number.parseInt(session.user.householdId, 10);
    const censusYearId = Number.parseInt(session.user.censusYearId, 10);

    // Verify the member belongs to this household
    const members = await getHouseholdMemberWithDetails(householdId, censusYearId);
    const memberExists = members.some((m) => m.memberId === data.id);

    if (!memberExists) {
      return NextResponse.json(
        {
          error: t("memberNotFoundInThisHousehold"),
        },
        { status: 404 },
      );
    }

    // Update the member
    await updateMember(data.id!, {
      firstName: data.firstName,
      lastName: data.lastName,
      dateOfBirth: data.dateOfBirth,
      gender: data.gender,
      mobilePhone: data.mobilePhone,
      hobby: data.hobby || null, // Use null instead of undefined for SQL compatibility
      occupation: data.occupation || null, // Use null instead of undefined for SQL compatibility
    });

    // Update the household member relationship
    const householdMember = await prisma.householdMember.findFirst({
      where: {
        householdId,
        memberId: data.id,
        censusYearId,
      },
    });

    if (householdMember) {
      await updateHouseholdMember(householdMember.id, {
        relationship: data.relationship,
      });
    }

    // Update census form last_updated timestamp
    await prisma.censusForm.updateMany({
      where: {
        householdId,
        censusYearId,
      },
      data: {
        lastUpdated: new Date(),
      },
    });

    // Get the updated member
    const updatedMembers = await getHouseholdMemberWithDetails(householdId, censusYearId);
    const updatedMember = updatedMembers.find((m) => m.memberId === data.id);

    return NextResponse.json({
      success: true,
      message: tNotifications("memberUpdatedSuccessfully"),
      member: updatedMember,
    });
  } catch (error) {
    return NextResponse.json(
      {
        error: t("failedToUpdateHouseholdMember"),
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

/**
 * DELETE /api/census/members/:id
 *
 * Deletes a household member
 */
export async function DELETE(request: NextRequest) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const t = await getTranslations({ locale, namespace: "errors" });
  const tNotifications = await getTranslations({
    locale,
    namespace: "notifications",
  });

  try {
    // Check if user is authenticated
    const session = await getServerSession(censusAuthOptions);

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has the household role
    if (session.user.role !== "household") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Get the member ID from the URL
    const url = new URL(request.url);
    const memberId = url.searchParams.get("id");

    if (!memberId) {
      return NextResponse.json({ error: t("memberIdRequired") }, { status: 400 });
    }

    const memberIdNum = Number.parseInt(memberId, 10);
    const householdId = Number.parseInt(session.user.householdId!, 10);
    const censusYearId = Number.parseInt(session.user.censusYearId, 10);

    // Verify the member belongs to this household
    const members = await getHouseholdMemberWithDetails(householdId, censusYearId);
    const memberToDelete = members.find((m) => m.memberId === memberIdNum);

    if (!memberToDelete) {
      return NextResponse.json(
        {
          error: t("memberNotFoundInThisHousehold"),
        },
        { status: 404 },
      );
    }

    // Check if this is the household head - don't allow deletion of the head
    if (memberToDelete.relationship === "head") {
      return NextResponse.json(
        {
          error: t("cannotDeleteHouseholdHead"),
        },
        { status: 400 },
      );
    }

    // Delete the member completely (including all relationships and sacraments)
    // This uses the same robust deletion logic as the admin portal
    await deleteMemberWithChecks(memberIdNum);

    // Update census form last_updated timestamp
    await prisma.censusForm.updateMany({
      where: {
        householdId,
        censusYearId,
      },
      data: {
        lastUpdated: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      message: tNotifications("memberDeletedSuccessfully"),
    });
  } catch (error) {
    // Handle specific validation errors (same as admin portal)
    if (error instanceof HouseholdHeadDeletionError) {
      return NextResponse.json(
        {
          error: t("cannotDeleteHouseholdHead"),
        },
        { status: 400 },
      );
    }

    return NextResponse.json(
      {
        error: t("failedToDeleteHouseholdMember"),
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
