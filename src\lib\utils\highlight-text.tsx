import type React from "react";

/**
 * Highlights occurrences of a search term within a text string.
 *
 * @param text The text to search within
 * @param searchTerm The term to highlight
 * @param highlightClassName CSS class to apply to highlighted text (default: "bg-yellow-100 dark:bg-yellow-800/50 rounded px-1")
 * @returns React elements with highlighted matches
 */
export function highlightText(
  text: string | null | undefined,
  searchTerm: string,
  highlightClassName = "bg-yellow-100 dark:bg-yellow-800/50 rounded px-1",
): React.ReactNode {
  if (!(text && searchTerm)) {
    return text || "";
  }

  // Escape special regex characters in the search term
  const escapedSearchTerm = searchTerm.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");

  // Create a regex that's case insensitive
  const regex = new RegExp(`(${escapedSearchTerm})`, "gi");

  // Split the text by the regex
  const parts = text.split(regex);

  // Map each part to either plain text or highlighted text
  return parts.map((part, i) => {
    // Check if this part matches the search term (case insensitive)
    if (part.toLowerCase() === searchTerm.toLowerCase()) {
      return (
        <span className={highlightClassName} key={`highlight-${text.length}-${i}-${part.length}`}>
          {part}
        </span>
      );
    }
    return part;
  });
}
