"use client";

import { Ava<PERSON>, AvatarFallback } from "@/components/ui/avatar";

interface FluidIconProps {
  size?: "sm" | "md" | "lg";
  className?: string;
}

/**
 * FluidIcon component - Original animated AI agent indicator moved from census modal
 *
 * Features:
 * - Smooth flowing color blobs with organic movement
 * - Original gradient colours and animations from census modal
 * - Breathing/pulsing animation for alive feeling
 * - Adjustable size for different contexts
 * - Professional yet organic aesthetic
 */
export function FluidIcon({ size = "md", className = "" }: FluidIconProps) {
  // Size configurations based on original modal design
  const sizeConfig = {
    sm: {
      container: "w-12 h-12",
      avatar: "h-12 w-12",
      blob1: "w-6 h-6 -top-1 -left-1",
      blob2: "w-8 h-8 -top-2 -right-2",
      blob3: "w-4 h-4 -bottom-0.5 -left-0.5",
      blob4: "w-3 h-3 top-1/2 right-0",
    },
    md: {
      container: "w-16 h-16",
      avatar: "h-16 w-16",
      blob1: "w-8 h-8 -top-2 -left-2",
      blob2: "w-10 h-10 -top-3 -right-3",
      blob3: "w-6 h-6 -bottom-1 -left-1",
      blob4: "w-4 h-4 top-1/2 right-0",
    },
    lg: {
      container: "w-20 h-20",
      avatar: "h-20 w-20",
      blob1: "w-10 h-10 -top-2 -left-2",
      blob2: "w-12 h-12 -top-3 -right-3",
      blob3: "w-8 h-8 -bottom-1 -left-1",
      blob4: "w-5 h-5 top-1/2 right-0",
    },
  };

  const config = sizeConfig[size];

  return (
    <div
      className={`mx-auto ${config.container} relative mb-4 overflow-hidden rounded-full ${className}`}
    >
      {/* Subtle outer glow */}
      <div className="-inset-1 absolute rounded-full bg-gradient-to-br from-[#FF6308]/10 via-[#97A4FF]/8 to-[#BDC9E6]/6 blur-lg" />

      {/* Base gradient layer */}
      <div className="absolute inset-0 rounded-full bg-gradient-to-br from-[#FF6308]/60 via-[#97A4FF]/60 to-[#BDC9E6]/60" />

      {/* Flowing color blob 1 - Orange (Original from modal) */}
      <div
        className={`absolute ${config.blob1} rounded-full`}
        style={{
          background: "radial-gradient(circle, #FF6308 0%, #FF6308 30%, transparent 70%)",
          animation: "floatBlob1 8s ease-in-out infinite",
          filter: "blur(2px)",
        }}
      />

      {/* Flowing color blob 2 - Purple (Original from modal) */}
      <div
        className={`absolute ${config.blob2} rounded-full`}
        style={{
          background: "radial-gradient(circle, #97A4FF 0%, #97A4FF 25%, transparent 65%)",
          animation: "floatBlob2 12s ease-in-out infinite",
          filter: "blur(3px)",
        }}
      />

      {/* Flowing color blob 3 - Light Blue (Original from modal) */}
      <div
        className={`absolute ${config.blob3} rounded-full`}
        style={{
          background: "radial-gradient(circle, #BDC9E6 0%, #BDC9E6 35%, transparent 75%)",
          animation: "floatBlob3 10s ease-in-out infinite",
          filter: "blur(1px)",
        }}
      />

      {/* Additional flowing element (Original from modal) */}
      <div
        className={`absolute ${config.blob4} rounded-full`}
        style={{
          background: "radial-gradient(circle, #FF6308 0%, transparent 60%)",
          animation: "floatBlob4 6s ease-in-out infinite",
          filter: "blur(2px)",
        }}
      />

      {/* Subtle breathing overlay (Original from modal) */}
      <div
        className="absolute inset-0 rounded-full bg-gradient-to-tr from-[#FF6308]/15 via-transparent to-[#97A4FF]/10"
        style={{
          animation: "gentleBreathe 4s ease-in-out infinite",
        }}
      />

      <Avatar className={`${config.avatar} relative z-10`}>
        <AvatarFallback className="bg-transparent font-medium text-white text-xs shadow-sm">
          {/* Empty - AI agent indicator */}
        </AvatarFallback>
      </Avatar>

      {/* Original keyframe animations from census modal */}
      <style jsx>{`
        @keyframes floatBlob1 {
          0% {
            transform: translate(0px, 0px) scale(1);
            opacity: 0.8;
          }
          25% {
            transform: translate(40px, 20px) scale(1.2);
            opacity: 0.9;
          }
          50% {
            transform: translate(60px, 50px) scale(0.8);
            opacity: 0.7;
          }
          75% {
            transform: translate(20px, 60px) scale(1.1);
            opacity: 0.85;
          }
          100% {
            transform: translate(0px, 0px) scale(1);
            opacity: 0.8;
          }
        }

        @keyframes floatBlob2 {
          0% {
            transform: translate(0px, 0px) scale(1);
            opacity: 0.7;
          }
          30% {
            transform: translate(-30px, 25px) scale(0.9);
            opacity: 0.8;
          }
          60% {
            transform: translate(-50px, -20px) scale(1.3);
            opacity: 0.6;
          }
          90% {
            transform: translate(-10px, -40px) scale(1.1);
            opacity: 0.75;
          }
          100% {
            transform: translate(0px, 0px) scale(1);
            opacity: 0.7;
          }
        }

        @keyframes floatBlob3 {
          0% {
            transform: translate(0px, 0px) scale(1);
            opacity: 0.6;
          }
          40% {
            transform: translate(35px, -15px) scale(1.4);
            opacity: 0.8;
          }
          80% {
            transform: translate(-25px, -35px) scale(0.7);
            opacity: 0.5;
          }
          100% {
            transform: translate(0px, 0px) scale(1);
            opacity: 0.6;
          }
        }

        @keyframes floatBlob4 {
          0% {
            transform: translate(0px, 0px) scale(1);
            opacity: 0.5;
          }
          50% {
            transform: translate(-20px, 15px) scale(1.5);
            opacity: 0.7;
          }
          100% {
            transform: translate(0px, 0px) scale(1);
            opacity: 0.5;
          }
        }

        @keyframes gentleBreathe {
          0%, 100% {
            opacity: 0.2;
            transform: scale(1);
          }
          50% {
            opacity: 0.4;
            transform: scale(1.03);
          }
        }
      `}</style>
    </div>
  );
}
