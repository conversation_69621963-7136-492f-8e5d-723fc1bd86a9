import { type NextRequest, NextResponse } from "next/server";
import { getTranslations } from "next-intl/server";
import { generateCensusRateLimitTokenFromRequest } from "@/lib/auth/census-rate-limit-utils";
import { getEnhancedRateLimitStatus } from "@/lib/auth/rate-limiting-service";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";

// Simple in-memory rate limiting for status check API
const statusCheckLimits = new Map<
	string,
	{ count: number; resetTime: number }
>();
const STATUS_CHECK_LIMIT = 30; // Max 30 requests per minute
const STATUS_CHECK_WINDOW = 60 * 1000; // 1 minute

/**
 * GET /api/census/auth/rate-limit-status
 * Returns enhanced rate limiting status for hybrid client-server approach
 * Includes server timestamp and lockout expiry for client-side countdown
 * Uses IP-based rate limiting (same as census authentication)
 */
export async function GET(request: NextRequest) {
	// Get locale from centralized utility at the beginning
	const locale = await getLocaleFromCookies();
	const t = await getTranslations({ locale, namespace: "errors" });

	try {
		// Generate consistent IP-based token (matches census auth logic)
		const sessionToken = generateCensusRateLimitTokenFromRequest(request);

		// Simple in-memory rate limiting for status check API
		const now = Date.now();
		const clientLimit = statusCheckLimits.get(sessionToken);

		if (!clientLimit || now > clientLimit.resetTime) {
			// Reset or create new limit window
			statusCheckLimits.set(sessionToken, {
				count: 1,
				resetTime: now + STATUS_CHECK_WINDOW,
			});
		} else if (clientLimit.count >= STATUS_CHECK_LIMIT) {
			// Rate limit exceeded for status checks
			return NextResponse.json(
				{
					success: false,
					error: t("tooManyRequests"),
				},
				{ status: 429 },
			);
		} else {
			// Increment count
			clientLimit.count++;
		}

		// Clean up expired entries periodically
		if (statusCheckLimits.size > 1000) {
			for (const [key, value] of statusCheckLimits.entries()) {
				if (now > value.resetTime) {
					statusCheckLimits.delete(key);
				}
			}
		}

		// Get enhanced rate limit status with server timestamp and lockout expiry
		const status = await getEnhancedRateLimitStatus(sessionToken);

		return NextResponse.json({
			success: true,
			data: {
				isLocked: status.isLocked,
				remainingTime: status.remainingTime,
				attemptsRemaining: status.attemptsRemaining,
				escalationLevel: status.escalationLevel,
				lockoutExpiry: status.lockoutExpiry,
			},
		});
	} catch (_error) {
		// Return safe defaults on error with current server timestamp
		const serverTimestamp = Date.now();
		return NextResponse.json({
			success: true,
			data: {
				isLocked: false,
				remainingTime: 0,
				attemptsRemaining: 5,
				escalationLevel: 0,
				serverTimestamp,
				lockoutExpiry: null,
			},
		});
	}
}
