"use client";

import { useTranslations } from "next-intl";
import type { FieldValues, UseFormReturn } from "react-hook-form";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

interface SettingsCardProps<TFormValues extends FieldValues = FieldValues> {
  title: string;
  description: string;
  children: React.ReactNode;
  saveButtonText?: string;
  statusIndicator?: React.ReactNode;
  footerContent?: React.ReactNode;
  isSubmitting?: boolean;
  // For form integration
  form?: UseFormReturn<TFormValues>;
  onFormSubmit?: (data: TFormValues) => Promise<unknown>;
  // For direct save (non-form)
  onSave?: () => Promise<void>;
  isLoading?: boolean;
}

export function SettingsCard<TFormValues extends FieldValues = FieldValues>({
  title,
  description,
  children,
  saveButtonText,
  statusIndicator,
  footerContent,
  isSubmitting = false,
  // Form props
  form,
  onFormSubmit,
  // Direct save props
  onSave,
  isLoading = false,
}: SettingsCardProps<TFormValues>) {
  const tCommon = useTranslations("common");
  // Determine if we're using a form or direct save
  const isFormBased = !!form;
  const defaultSaveText = saveButtonText || tCommon("save");

  // Content to be rendered inside either the form or card
  const content = (
    <>
      <CardHeader className="border-border border-b px-4 py-4 sm:px-6">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="font-medium text-lg">{title}</CardTitle>
            <CardDescription className="mt-1 text-muted-foreground text-sm">
              {description}
            </CardDescription>
          </div>
          {statusIndicator}
        </div>
      </CardHeader>
      <CardContent className="space-y-6 px-4 py-5 sm:px-6">{children}</CardContent>
      {(onSave || form || footerContent) && (
        <CardFooter className="flex justify-end border-border border-t px-4 py-3 sm:px-6">
          {footerContent}
          {!isFormBased && onSave && (
            <Button disabled={isLoading} onClick={onSave}>
              {isLoading ? tCommon("saving") : defaultSaveText}
            </Button>
          )}
          {isFormBased && (
            <Button disabled={isSubmitting} type="submit">
              {isSubmitting ? tCommon("saving") : defaultSaveText}
            </Button>
          )}
        </CardFooter>
      )}
    </>
  );

  // Return either a form or a card depending on whether form is provided
  return (
    <Card className="w-full overflow-hidden border border-border shadow-sm">
      {isFormBased ? (
        <form onSubmit={form!.handleSubmit(onFormSubmit || (() => Promise.resolve()))}>
          {content}
        </form>
      ) : (
        content
      )}
    </Card>
  );
}
