import { z } from "zod/v4";

/**
 * Client-side validation schemas for admin settings forms
 * These schemas use translation functions for user-facing error messages
 * Use with useTranslations('validation') hook in client components
 */

/**
 * Create church info form validation schema with translations
 * @param t - Translation function from useTranslations('validation')
 */
export function createClientChurchInfoSchema(t: any) {
  return z.object({
    churchName: z
      .string()
      .min(1, { error: t("churchNameRequired") })
      .regex(/^[^0-9]*$/, { error: t("invalidChurchName") }),
    email: z.email({ error: t("invalidEmailFormat") }),
    contactNumber: z
      .string()
      .min(1, { error: t("contactNumberRequired") })
      .length(10, { error: t("contactNumberLength") })
      .regex(/^[0-9]+$/, { error: t("contactNumberDigits") }),
    address: z.string().min(1, { error: t("addressRequired") }),
  });
}

/**
 * Create site URL form validation schema with translations
 * @param t - Translation function from useTranslations('validation')
 */
export function createClientSiteUrlSchema(t: any) {
  return z.object({
    siteUrl: z
      .string()
      .min(1, { error: t("siteUrlRequired") })
      .pipe(z.url({ error: t("invalidUrlFormat") }))
      .refine((url) => url.startsWith("http://") || url.startsWith("https://"), {
        error: t("urlMustStartWithProtocol"),
      })
      .refine((url) => !url.endsWith("/"), {
        error: t("urlNoTrailingSlash"),
      }),
  });
}

/**
 * Create homepage announcement form validation schema with translations
 * @param t - Translation function from useTranslations('validation')
 */
export function createClientHomepageAnnouncementSchema(t: any) {
  return z.object({
    enabled: z.boolean(),
    text: z.string().max(1000, { error: t("announcementTextMaxLength") }),
    type: z.enum(["info", "warning", "success", "destructive"]),
  });
}

/**
 * Create rate limit settings form validation schema with translations
 * @param t - Translation function from useTranslations('validation')
 */
export function createClientRateLimitSchema(t: any) {
  return z
    .object({
      maxAttempts: z.coerce
        .number()
        .int({ error: t("maxAttemptsInteger") })
        .min(1, { error: t("maxAttemptsMinimum") })
        .max(20, { error: t("maxAttemptsMaximum") }),
      lockoutMinutes: z.coerce
        .number()
        .int({ error: t("lockoutDurationInteger") })
        .min(1, { error: t("lockoutDurationMinimum") })
        .max(1440, { error: t("lockoutDurationMaximum") }),
      escalationMinutes: z.coerce
        .number()
        .int({ error: t("escalationIncrementInteger") })
        .min(0, { error: t("escalationIncrementMinimum") })
        .max(120, { error: t("escalationIncrementMaximum") }),
    })
    .refine(
      (data) => {
        // Validate that escalation doesn't exceed 24 hours
        const maxLockoutMinutes = 1440; // 24 hours

        // Check escalation levels 0-5 (matching the actual implementation)
        for (let level = 0; level <= 5; level++) {
          const lockoutDuration = data.lockoutMinutes + level * data.escalationMinutes;
          if (lockoutDuration > maxLockoutMinutes) {
            return false; // Validation failed
          }
        }
        return true; // Validation passed
      },
      {
        message: t("escalationSettingsExceed24Hours"),
        path: ["escalationMinutes"],
      },
    );
}

/**
 * Create census year settings form validation schema with translations
 * @param t - Translation function from useTranslations('validation')
 */
export function createClientCensusYearSchema(t: any) {
  return z.object({
    year: z.coerce
      .number()
      .int({ error: t("censusYearInteger") })
      .min(2000, { error: t("censusYearMinimum") })
      .max(2100, { error: t("censusYearMaximum") }),
    description: z
      .string()
      .min(1, { error: t("censusYearDescriptionRequired") })
      .max(255, { error: t("censusYearDescriptionMaxLength") }),
  });
}

// Type exports for client-side forms
export type ClientChurchInfoFormValues = z.infer<ReturnType<typeof createClientChurchInfoSchema>>;
export type ClientSiteUrlFormValues = z.infer<ReturnType<typeof createClientSiteUrlSchema>>;
export type ClientRateLimitFormValues = z.infer<ReturnType<typeof createClientRateLimitSchema>>;
export type ClientCensusYearFormValues = z.infer<ReturnType<typeof createClientCensusYearSchema>>;
