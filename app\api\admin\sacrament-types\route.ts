import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { authOptions } from "@/lib/auth/auth-options";
import { prisma } from "@/lib/db/prisma";
import { getLocaleFromCookies } from "@/lib/utils/server-messages";

/**
 * GET /api/admin/sacrament-types
 * Get all sacrament types
 *
 * Following professional SSR validation pattern:
 * - Server-side: Static English messages for API security
 * - Client-side: Translated messages for user experience
 */
export async function GET(_request: Request) {
	// Get locale from centralized utility at the beginning
	const locale = await getLocaleFromCookies();
	const t = await getTranslations({ locale, namespace: "admin" });

	try {
		// Check authentication
		const session = await getServerSession(authOptions);

		if (!session || session.user.role !== "admin") {
			return NextResponse.json({ error: t("unauthorized") }, { status: 401 });
		}

		// Get all sacrament types
		const sacramentTypes = await prisma.sacramentType.findMany({
			orderBy: { id: "asc" },
		});

		return NextResponse.json({
			success: true,
			sacramentTypes,
		});
	} catch (_error) {
		return NextResponse.json(
			{ error: t("sacramentTypesFetchFailed") },
			{ status: 500 },
		);
	}
}
