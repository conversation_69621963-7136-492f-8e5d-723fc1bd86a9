/**
 * Utility functions for the AI Analytics Chatbot
 */

export interface ChatbotMessage {
	id: string;
	type: "user" | "assistant";
	content: string;
	timestamp: Date;
	status?: "sending" | "sent" | "error";
	queryType?: "members" | "households" | "unique_codes" | "general";
}

export interface ChatbotResponse {
	response: string;
	queryType: "members" | "households" | "unique_codes" | "general";
	executedQuery: boolean;
	timestamp: string;
}

/**
 * Format database query results for display
 */
export function formatQueryResults(results: Record<string, unknown>[]): string {
	if (!Array.isArray(results) || results.length === 0) {
		return "No results found.";
	}

	if (results.length === 1 && typeof results[0] === "object") {
		// Single result object (like counts)
		const result = results[0];
		return Object.entries(result)
			.map(
				([key, value]) => `${formatFieldName(key)}: ${formatFieldValue(value)}`,
			)
			.join("\n");
	}

	// Multiple results
	let formatted = `Found ${results.length} results:\n\n`;
	results
		.slice(0, 10)
		.forEach((row: Record<string, unknown>, index: number) => {
			formatted += `${index + 1}. `;
			formatted += Object.entries(row)
				.map(
					([key, value]) =>
						`${formatFieldName(key)}: ${formatFieldValue(value)}`,
				)
				.join(", ");
			formatted += "\n";
		});

	if (results.length > 10) {
		formatted += `\n... and ${results.length - 10} more results.`;
	}

	return formatted;
}

/**
 * Format field names for better readability
 */
function formatFieldName(fieldName: string): string {
	return fieldName.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
}

/**
 * Format field values for better display
 */
function formatFieldValue(value: unknown): string {
	if (value === null || value === undefined) {
		return "N/A";
	}

	if (typeof value === "boolean") {
		return value ? "Yes" : "No";
	}

	if (value instanceof Date) {
		return value.toLocaleDateString();
	}

	if (typeof value === "string" && value.includes("T") && value.includes("Z")) {
		// Likely an ISO date string
		try {
			const date = new Date(value);
			return date.toLocaleDateString();
		} catch {
			return value;
		}
	}

	return String(value);
}

/**
 * Generate a unique message ID
 */
export function generateMessageId(): string {
	return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Validate chatbot message content
 */
export function validateMessage(content: string): {
	isValid: boolean;
	error?: string;
} {
	if (!content || content.trim().length === 0) {
		return { isValid: false, error: "Message cannot be empty" };
	}

	if (content.length > 8000) {
		return {
			isValid: false,
			error: "Message is too long (max 8000 characters)",
		};
	}

	return { isValid: true };
}

/**
 * Extract query type from message content
 */
export function detectQueryType(
	content: string,
): "members" | "households" | "unique_codes" | "general" {
	const lowerContent = content.toLowerCase();

	if (
		lowerContent.includes("member") ||
		lowerContent.includes("person") ||
		lowerContent.includes("people") ||
		lowerContent.includes("individual")
	) {
		return "members";
	}

	if (
		lowerContent.includes("household") ||
		lowerContent.includes("family") ||
		lowerContent.includes("home") ||
		lowerContent.includes("address")
	) {
		return "households";
	}

	if (
		lowerContent.includes("code") ||
		lowerContent.includes("login") ||
		lowerContent.includes("access") ||
		lowerContent.includes("unique")
	) {
		return "unique_codes";
	}

	return "general";
}

/**
 * Get appropriate icon for query type
 */
export function getQueryTypeIcon(queryType: string): string {
	switch (queryType) {
		case "members":
			return "👥";
		case "households":
			return "🏠";
		case "unique_codes":
			return "🎫";
		case "analytics":
			return "📊";
		default:
			return "💬";
	}
}

/**
 * Get colour scheme for query type
 */
export function getQueryTypeColor(queryType: string): {
	bg: string;
	text: string;
	border: string;
} {
	switch (queryType) {
		case "members":
			return {
				bg: "bg-[#3B82F6]/10",
				text: "text-[#3B82F6]",
				border: "border-[#3B82F6]/20",
			};
		case "households":
			return {
				bg: "bg-[#10B981]/10",
				text: "text-[#10B981]",
				border: "border-[#10B981]/20",
			};
		case "unique_codes":
			return {
				bg: "bg-[#FF6308]/10",
				text: "text-[#FF6308]",
				border: "border-[#FF6308]/20",
			};
		case "analytics":
			return {
				bg: "bg-[#97A4FF]/10",
				text: "text-[#97A4FF]",
				border: "border-[#97A4FF]/20",
			};
		default:
			return {
				bg: "bg-slate-100",
				text: "text-slate-600",
				border: "border-slate-200",
			};
	}
}
