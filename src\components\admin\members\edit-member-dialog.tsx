"use client";

import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import type { z } from "zod/v4";
import { EditableMemberSacraments } from "@/components/admin/members/editable-member-sacraments";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { useIsMobile } from "@/hooks/use-mobile";
import { useMessage } from "@/hooks/useMessage";
import { cn, devLog } from "@/lib/utils";
import { formatDateForDatabase } from "@/lib/utils/date-time";
import { zodResolver } from "@/lib/utils/zod-resolver-compat";
import { createClientUpdateMemberSchema } from "@/lib/validation/client/members-client";
import type { ICensusYear, IMemberWithDetails } from "@/types";

interface EditMemberDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  member: IMemberWithDetails;
  censusYears: ICensusYear[];
  onMemberUpdated: () => void;
}

export function EditMemberDialog({
  open,
  onOpenChange,
  member,
  onMemberUpdated,
}: EditMemberDialogProps) {
  const { showError } = useMessage();
  const isMobile = useIsMobile();
  const t = useTranslations("admin");
  const tForms = useTranslations("forms");
  const tCommon = useTranslations("common");
  const tValidation = useTranslations("validation");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [dobPopoverOpen, setDobPopoverOpen] = useState(false);

  // Create client schema with translations
  const updateMemberSchema = createClientUpdateMemberSchema(tValidation);
  type UpdateMemberFormValues = z.infer<typeof updateMemberSchema>;

  // Initialize form with member data
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<UpdateMemberFormValues>({
    resolver: zodResolver(updateMemberSchema),
    defaultValues: {
      firstName: member.firstName || "",
      lastName: member.lastName || "",
      dateOfBirth: member.dateOfBirth || "",
      gender: member.gender || "other",
      mobilePhone: member.mobilePhone || "",
      hobby: member.hobby || undefined,
      relationship: member.relationship || "other",
    },
  });

  // Watch form values for controlled components
  const watchedGender = watch("gender");
  const watchedRelationship = watch("relationship");
  const watchedDateOfBirth = watch("dateOfBirth");

  // Check if the member is a household head (relationship field should be disabled)
  const isHouseholdHead = member.relationship === "head";

  // Reset form when member changes
  useEffect(() => {
    if (member) {
      reset({
        firstName: member.firstName || "",
        lastName: member.lastName || "",
        dateOfBirth: member.dateOfBirth || "",
        gender: member.gender || "other",
        mobilePhone: member.mobilePhone || "",
        hobby: member.hobby || undefined,
        relationship: member.relationship || "other",
      });
    }
  }, [member, reset]);

  // Handle form submission
  const onSubmit = async (data: UpdateMemberFormValues) => {
    try {
      setIsSubmitting(true);

      // Submit form data to API
      const response = await fetch(`/api/admin/members/${member.memberId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || tCommon("failedToUpdateMember"));
      }

      // Success
      onMemberUpdated();
    } catch (error) {
      devLog.error("Error updating member:", error);
      showError("failedToUpdateMember");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle dialog close
  const handleDialogClose = (open: boolean) => {
    if (!(isSubmitting && open)) {
      reset();
      setDobPopoverOpen(false); // Reset popover state
      onOpenChange(open);
    }
  };

  // Handle date selection (preserving local date to prevent timezone shifts)
  const handleDateSelect = (date: Date) => {
    // Format date properly to prevent off-by-one errors due to timezone conversion
    const formattedDate = formatDateForDatabase(date);
    setValue("dateOfBirth", formattedDate);
  };

  // Shared form content component for both mobile and desktop
  const FormContent = () => (
    <div className="grid gap-4 py-4">
      {/* Personal Information */}
      <div className="mb-2">
        <h3 className="font-medium text-sm">{tCommon("personalInformation")}</h3>
      </div>

      {/* Name fields */}
      <div className="grid grid-cols-2 gap-4">
        <div className="grid gap-2">
          <Label className={errors.firstName ? "text-destructive" : ""} htmlFor="firstName">
            {tForms("firstName")} <span className="text-destructive">*</span>
          </Label>
          <Input
            id="firstName"
            {...register("firstName")}
            className={errors.firstName ? "border-destructive" : ""}
            placeholder={tForms("enterFirstName")}
          />
          {errors.firstName && (
            <p className="text-destructive text-xs">{errors.firstName.message}</p>
          )}
        </div>

        <div className="grid gap-2">
          <Label className={errors.lastName ? "text-destructive" : ""} htmlFor="lastName">
            {tForms("lastName")} <span className="text-destructive">*</span>
          </Label>
          <Input
            id="lastName"
            {...register("lastName")}
            className={errors.lastName ? "border-destructive" : ""}
            placeholder={tForms("enterLastName")}
          />
          {errors.lastName && <p className="text-destructive text-xs">{errors.lastName.message}</p>}
        </div>
      </div>

      {/* Date of Birth and Mobile Phone */}
      <div className="grid grid-cols-2 gap-4">
        <div className="grid gap-2">
          <Label className={errors.dateOfBirth ? "text-destructive" : ""}>
            {tForms("dateOfBirth")} <span className="text-destructive">*</span>
          </Label>
          <Popover onOpenChange={setDobPopoverOpen} open={dobPopoverOpen}>
            <PopoverTrigger asChild>
              <Button
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !watchedDateOfBirth && "text-muted-foreground",
                  errors.dateOfBirth && "border-destructive",
                )}
                type="button"
                variant="outline"
              >
                <CalendarIcon className="mr-2 h-4 w-4 flex-shrink-0" />
                <span className="truncate">
                  {watchedDateOfBirth ? (
                    format(new Date(watchedDateOfBirth), "dd/MM/yyyy")
                  ) : (
                    <span>{tCommon("pickADate")}</span>
                  )}
                </span>
              </Button>
            </PopoverTrigger>
            <PopoverContent
              align="start"
              className="w-auto p-0"
              onWheel={(e) => {
                // Allow wheel events to propagate to calendar for proper scrolling
                e.stopPropagation();
              }}
            >
              <Calendar
                date={watchedDateOfBirth ? new Date(watchedDateOfBirth) : null}
                onDateChange={handleDateSelect}
                onDone={() => setDobPopoverOpen(false)} // Hide the footer in Calendar to match census portal behavior
                preventFutureDates={true} // Hide the Today button in Calendar
                showFooter={false} // This prevents auto-scrolling and allows mouse wheel scrolling
                showTodayButton={false} // Prevent future dates for date of birth
                standalone={false}
              />
              {/* Custom footer with Today and Done buttons - matches census portal pattern */}
              <div className="flex justify-between border-t p-3">
                <Button
                  className="cursor-pointer"
                  onClick={() => {
                    const today = new Date();
                    const formattedDate = formatDateForDatabase(today);
                    setValue("dateOfBirth", formattedDate);
                  }}
                  size="sm"
                  variant="ghost"
                >
                  {tCommon("today")}
                </Button>
                <Button
                  className="cursor-pointer"
                  onClick={() => setDobPopoverOpen(false)}
                  size="sm"
                >
                  {tCommon("done")}
                </Button>
              </div>
            </PopoverContent>
          </Popover>
          {errors.dateOfBirth && (
            <p className="text-destructive text-xs">{errors.dateOfBirth.message}</p>
          )}
        </div>

        <div className="grid gap-2">
          <Label className={errors.mobilePhone ? "text-destructive" : ""} htmlFor="mobilePhone">
            {tForms("mobilePhone")} <span className="text-destructive">*</span>
          </Label>
          <Input
            id="mobilePhone"
            {...register("mobilePhone")}
            className={errors.mobilePhone ? "border-destructive" : ""}
            maxLength={10}
            placeholder={tForms("enterMobileNumber")}
          />
          {errors.mobilePhone && (
            <p className="text-destructive text-xs">{errors.mobilePhone.message}</p>
          )}
        </div>
      </div>

      {/* Gender and Relationship */}
      <div className="grid grid-cols-2 gap-4">
        <div className="grid gap-2">
          <Label className={errors.gender ? "text-destructive" : ""}>
            {tForms("gender")} <span className="text-destructive">*</span>
          </Label>
          <Select
            onValueChange={(value) => setValue("gender", value as "male" | "female" | "other")}
            value={watchedGender}
          >
            <SelectTrigger className={errors.gender ? "border-destructive" : ""}>
              <SelectValue placeholder={tForms("selectGender")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="male">{tForms("male")}</SelectItem>
              <SelectItem value="female">{tForms("female")}</SelectItem>
              <SelectItem value="other">{tForms("other")}</SelectItem>
            </SelectContent>
          </Select>
          {errors.gender && <p className="text-destructive text-xs">{errors.gender.message}</p>}
        </div>

        <div className="grid gap-2">
          <Label className={errors.relationship ? "text-destructive" : ""}>
            {tForms("relationship")} <span className="text-destructive">*</span>
          </Label>
          <Select
            disabled={isHouseholdHead}
            onValueChange={(value) =>
              setValue(
                "relationship",
                value as "head" | "spouse" | "child" | "parent" | "relative" | "other",
              )
            }
            value={watchedRelationship}
          >
            <SelectTrigger
              className={cn(
                errors.relationship ? "border-destructive" : "",
                isHouseholdHead ? "cursor-not-allowed opacity-50" : "",
              )}
            >
              <SelectValue placeholder={tForms("selectRelationship")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="head">{tForms("head")}</SelectItem>
              <SelectItem value="spouse">{tForms("spouse")}</SelectItem>
              <SelectItem value="child">{tForms("child")}</SelectItem>
              <SelectItem value="parent">{tForms("parent")}</SelectItem>
              <SelectItem value="relative">{tForms("relative")}</SelectItem>
              <SelectItem value="other">{tForms("other")}</SelectItem>
            </SelectContent>
          </Select>
          {errors.relationship && (
            <p className="text-destructive text-xs">{errors.relationship.message}</p>
          )}
        </div>
      </div>

      {/* Hobby */}
      <div className="grid gap-2">
        <Label className={errors.hobby ? "text-destructive" : ""} htmlFor="hobby">
          {tForms("hobbyOptional")}
        </Label>
        <Textarea
          id="hobby"
          {...register("hobby")}
          className={errors.hobby ? "border-destructive" : ""}
          placeholder={tForms("enterHobbyOrInterests")}
          rows={3}
        />
        {errors.hobby && <p className="text-destructive text-xs">{errors.hobby.message}</p>}
      </div>

      {/* Divider */}
      <Separator className="my-4" />

      {/* Sacraments Section */}
      <div className="space-y-4">
        <EditableMemberSacraments
          memberId={member.memberId}
          memberName={`${member.firstName} ${member.lastName}`}
        />
      </div>
    </div>
  );

  // Action buttons component for both mobile and desktop
  const ActionButtons = () => (
    <>
      <Button
        disabled={isSubmitting}
        onClick={() => handleDialogClose(false)}
        type="button"
        variant="outline"
      >
        {tCommon("cancel")}
      </Button>
      <Button disabled={isSubmitting} type="submit">
        {isSubmitting ? (
          <>
            <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
            {t("updating")}
          </>
        ) : (
          t("updateMember")
        )}
      </Button>
    </>
  );

  // Mobile implementation using Drawer (native swipe-to-dismiss from bottom)
  // Features: Visual handle bar, smooth swipe gestures, and hidden scrollbar
  if (isMobile) {
    return (
      <Drawer onOpenChange={handleDialogClose} open={open}>
        <DrawerContent className="flex max-h-[80vh] min-h-[60vh] flex-col">
          <DrawerHeader className="pb-4 text-left">
            <DrawerTitle className="font-semibold text-lg">{tCommon("editMember")}</DrawerTitle>
            <DrawerDescription className="text-muted-foreground">
              {t("updateMemberInformationFor", {
                memberName: `${member.firstName} ${member.lastName}`,
              })}
            </DrawerDescription>
          </DrawerHeader>

          <form className="flex flex-1 flex-col" onSubmit={handleSubmit(onSubmit)}>
            <div className="scrollbar-hide flex-1 overflow-y-auto px-4">
              <FormContent />
            </div>

            <div className="border-t px-4 pt-4 pb-4">
              <div className="flex justify-end gap-2">
                <ActionButtons />
              </div>
            </div>
          </form>
        </DrawerContent>
      </Drawer>
    );
  }

  // Desktop implementation using Dialog (unchanged)
  return (
    <Dialog onOpenChange={handleDialogClose} open={open}>
      <DialogContent className="max-h-[80vh] overflow-y-auto sm:max-w-[700px]">
        <DialogHeader>
          <DialogTitle>{tCommon("editMember")}</DialogTitle>
          <DialogDescription>
            {t("updateMemberInformationFor", {
              memberName: `${member.firstName} ${member.lastName}`,
            })}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)}>
          <FormContent />

          <DialogFooter>
            <ActionButtons />
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
