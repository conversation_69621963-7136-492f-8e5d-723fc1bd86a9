import { cookies } from "next/headers";
import Link from "next/link";
import { getServerSession } from "next-auth";
import { getTranslations } from "next-intl/server";
import { CensusClosedMessage } from "@/components/home/<USER>";
import { CensusWelcomeBack } from "@/components/home/<USER>";
import { HomeToastHandler } from "@/components/home/<USER>";
import { HomepageCoverImage } from "@/components/home/<USER>";
import { UniqueCodeEntry } from "@/components/home/<USER>";
import { UrlParamHandler } from "@/components/home/<USER>";
import { isCensusOpen } from "@/lib/census/census-availability";
import { censusAuthOptions } from "@/lib/census-auth/census-auth-options";
import { prisma } from "@/lib/db/prisma";
import type { CensusData } from "@/lib/homepage/placeholder-processor";

// Elegant component selection using functional approach
const selectHomeComponent = (
	censusOpen: boolean,
	isAuthenticated: boolean,
	urlCode: string | null,
	sessionCode: string | undefined,
	sessionUser: any,
	censusData: CensusData | null,
	locale: string,
) => {
	// Early return for census closed (highest priority)
	if (!censusOpen) {
		return (
			<CensusClosedMessage
				censusData={censusData || undefined}
				locale={locale}
			/>
		);
	}

	// Early return for unauthenticated users
	if (!isAuthenticated) {
		return (
			<UniqueCodeEntry censusData={censusData || undefined} locale={locale} />
		);
	}

	// Handle authenticated users: check for QR code workflow
	const hasUrlCode = Boolean(urlCode);
	const isDifferentCode = hasUrlCode && urlCode !== sessionCode;

	// QR code workflow: different code in URL means new login needed
	return isDifferentCode ? (
		<UniqueCodeEntry censusData={censusData || undefined} locale={locale} />
	) : (
		<CensusWelcomeBack
			censusData={censusData || undefined}
			locale={locale}
			userCode={sessionUser?.code}
			userName={sessionUser?.name}
		/>
	);
};

// Force dynamic rendering to prevent DYNAMIC_SERVER_USAGE errors
export const dynamic = "force-dynamic";

// This is a Server Component that fetches data on the server
export default async function Home({
	searchParams,
	params,
}: {
	searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
	params: Promise<{ locale: string }>;
}) {
	// Get locale from params
	const { locale } = await params;

	// Get translations for server component
	const tBrand = await getTranslations("brand");

	// Check if census is open on the server before rendering
	const censusStatus = await isCensusOpen();

	// Fetch census data for announcement placeholders
	let censusData = null;
	try {
		const censusSettings = await prisma.systemSettings.findMany({
			where: {
				settingKey: {
					in: [
						"system_open",
						"census_start_date",
						"census_end_date",
						"church_name",
					],
				},
			},
		});

		const censusMap = censusSettings.reduce(
			(acc, setting) => {
				acc[setting.settingKey] = setting.settingValue;
				return acc;
			},
			{} as Record<string, string | null>,
		);

		// Import date parser for safe date handling
		const { parseDate } = await import("@/lib/homepage/placeholder-processor");

		censusData = {
			isOpen: censusMap.system_open === "true",
			startDate: censusMap.census_start_date
				? parseDate(censusMap.census_start_date) || undefined
				: undefined,
			endDate: censusMap.census_end_date
				? parseDate(censusMap.census_end_date) || undefined
				: undefined,
			churchName: censusMap.church_name || "",
		};
	} catch (_error) {
		// Graceful fallback: use default census data
		if (process.env.NODE_ENV === "development") {
		}
		censusData = {
			isOpen: censusStatus.isOpen,
			churchName: "",
		};
	}

	// Check census authentication server-side (lightweight check)
	let isLoggedInToCensus = false;
	let censusSession = null;

	try {
		censusSession = await getServerSession(censusAuthOptions);
		isLoggedInToCensus = !!censusSession?.user;
	} catch (_error) {
		// Graceful fallback: treat as not logged in if session check fails
		if (process.env.NODE_ENV === "development") {
		}
		isLoggedInToCensus = false;
	}

	// Handle URL parameters (for QR code workflow)
	const resolvedSearchParams = await searchParams;
	const urlCode =
		typeof resolvedSearchParams.code === "string"
			? resolvedSearchParams.code
			: null;

	// Read the toast cookie - need to await cookies() in Next.js 15.3.1
	const cookieStore = await cookies();
	const toastCookie = cookieStore.get("census_toast");

	let initialToast = null;

	if (toastCookie) {
		try {
			initialToast = JSON.parse(toastCookie.value);
			// Note: Cookie deletion will be handled by the client-side toast handler
			// using a Server Action (cookies can only be modified in Server Actions or Route Handlers)
		} catch (_error) {}
	}

	// Render the appropriate content based on census status
	return (
		<>
			{/* Toast handler component to display notifications */}
			<HomeToastHandler toast={initialToast} />

			{/* Handle URL parameters for backward compatibility */}
			<UrlParamHandler />

			<div className="grid flex-1 items-stretch lg:grid-cols-2">
				{/* Left Column - Census Entry Form or Closed Message */}
				<div className="flex flex-col gap-4 p-6 md:p-10">
					<div className="flex justify-center gap-2 md:justify-start">
						<Link className="flex items-center gap-2 font-medium" href="/">
							<div className="flex h-6 w-6 items-center justify-center rounded-md bg-primary text-primary-foreground">
								<svg
									fill="none"
									height="16"
									stroke="currentColor"
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth="2"
									viewBox="0 0 24 24"
									width="16"
									xmlns="http://www.w3.org/2000/svg"
								>
									<path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z" />
									<path d="M3 9V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v4" />
									<path d="M13 13h4" />
									<path d="M13 17h4" />
									<path d="M7 13h2v4H7z" />
								</svg>
							</div>
							{tBrand("name")}
						</Link>
					</div>
					<div className="flex flex-1 flex-col items-center justify-center">
						{/* Elegant component selection with QR code workflow support */}
						{selectHomeComponent(
							censusStatus.isOpen,
							isLoggedInToCensus && !!censusSession?.user,
							urlCode,
							censusSession?.user?.code,
							censusSession?.user,
							censusData,
							locale,
						)}
					</div>
				</div>

				{/* Right Column - Cover Image */}
				<HomepageCoverImage />
			</div>
		</>
	);
}
