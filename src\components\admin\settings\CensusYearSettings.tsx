"use client";

import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod/v4";
import { FormFieldWithTooltip } from "@/components/admin/settings/FormFieldWithTooltip";
import { SettingsCard } from "@/components/admin/settings/SettingsCard";
import { Input } from "@/components/ui/input";
import { useFormSubmit } from "@/hooks/useFormSubmit";
import { useMessage } from "@/hooks/useMessage";
import { zodResolver } from "@/lib/utils/zod-resolver-compat";

// Define the schema for census year settings
const createCensusYearSchema = (lastCensusYear: string, tValidation: any) =>
	z.object({
		currentCensusYear: z
			.string()
			.min(1, { message: tValidation("currentCensusYearRequired") })
			.refine((val) => !isNaN(Number.parseInt(val)), {
				message: tValidation("firstCensusYearMustBeNumber"),
			})
			.refine(
				(val) => Number.parseInt(val) >= 2000 && Number.parseInt(val) <= 2100,
				{ message: tValidation("censusYearMinimum") },
			)
			.refine((val) => Number.parseInt(val) > Number.parseInt(lastCensusYear), {
				message: tValidation("currentCensusYearMustBeGreater", {
					lastYear: lastCensusYear,
				}),
			}),
	});

type CensusYearFormValues = z.infer<ReturnType<typeof createCensusYearSchema>>;

export function CensusYearSettings() {
	const t = useTranslations("admin");
	const tValidation = useTranslations("validation");
	const [lastCensusYear, setLastCensusYear] = useState("2022");
	const { showSuccess, showError, showWarning } = useMessage();

	// Initialize form with validation
	const form = useForm<CensusYearFormValues>({
		resolver: zodResolver(createCensusYearSchema(lastCensusYear, tValidation)),
		defaultValues: {
			currentCensusYear: "2023",
		},
	});

	// Update form validation when lastCensusYear changes
	useEffect(() => {
		form.clearErrors();
		form.trigger("currentCensusYear");
	}, [lastCensusYear, form]);

	// Fetch census year settings from the API
	useEffect(() => {
		const fetchCensusYearSettings = async () => {
			try {
				const response = await fetch("/api/settings/census-year");

				// Handle API errors
				if (!response.ok) {
					console.warn(`API call failed with status ${response.status}`);

					// If unauthorized, it means we're not logged in
					if (response.status === 401) {
						console.warn("Not logged in as admin");
						showWarning("needAdminLoginToViewSettings");
					} else {
						showError("failedToLoadCensusYearSettings");
					}

					// Set placeholder values
					form.reset({
						currentCensusYear: "2023",
					});
					setLastCensusYear("2022");
					return;
				}

				const data = await response.json();

				// Update form values
				form.reset({
					currentCensusYear: data.currentCensusYear || "2023",
				});

				// Set last census year from database
				setLastCensusYear(data.lastCensusYear || "2022");
			} catch (error) {
				console.error("Error fetching census year settings:", error);
				// For development purposes, use default values if the API call fails
				form.reset({
					currentCensusYear: "2023",
				});
				setLastCensusYear("2022");
				showWarning("usingDefaultValuesDatabaseUnavailable");
			}
		};

		fetchCensusYearSettings();
	}, [form, showWarning, showError, t]);

	// Use our custom hook for form submission
	const { handleSubmit: submitCensusYear, isSubmitting } =
		useFormSubmit<CensusYearFormValues>({
			onSubmit: async (data) => {
				try {
					const response = await fetch("/api/settings/census-year", {
						method: "PUT",
						headers: {
							"Content-Type": "application/json",
						},
						body: JSON.stringify(data),
					});

					const responseData = await response.json();

					if (!response.ok) {
						// If unauthorized, return a specific message
						if (response.status === 401) {
							return {
								success: false,
								message: t("needAdminLoginToSaveSettings"),
							};
						}

						if (responseData.details) {
							// Handle validation errors
							showError("InvalidInput", "settings");
							return {
								success: false,
								message: tValidation("checkFormErrors"),
							};
						}

						showError("UpdateFailed", "settings");
						return {
							success: false,
							message: t("failedToUpdateCensusYear"),
						};
					}

					// Check if this is a development message about database connection
					if (
						responseData.message &&
						responseData.message.includes("would be saved in production")
					) {
						return {
							success: true,
							message: responseData.message,
							data: responseData.censusYear,
						};
					}

					// Success - use centralized alert system
					showSuccess("censusYearUpdated");
					return {
						success: true,
						suppressAlert: true,
						data: responseData.censusYear,
					};
				} catch (error) {
					console.error("Error updating census year:", error);

					if (error instanceof Error && error.message === "ValidationError") {
						showError("InvalidInput", "settings");
					} else {
						showError("UpdateFailed", "settings");
					}

					return {
						success: false,
						suppressAlert: true,
					};
				}
			},
		});

	return (
		<SettingsCard
			description={t("configureCensusYearDescription")}
			form={form}
			isSubmitting={isSubmitting}
			onFormSubmit={submitCensusYear}
			title={t("censusYear")}
		>
			<div className="space-y-6">
				<FormFieldWithTooltip
					error={form.formState.errors.currentCensusYear}
					helperText={t("censusYearHelperText")}
					id="currentCensusYear"
					label={t("currentCensusYear")}
					placeholder={t("enterCurrentCensusYear")}
					register={form.register}
					required
					type="number"
				/>

				<div className="border-t pt-2">
					<FormFieldWithTooltip
						helperText={t("lastCensusYearHelperText")}
						id="last-census-year"
						label={t("lastCensusYear")}
					>
						<Input
							aria-readonly="true"
							className="cursor-not-allowed bg-muted opacity-70"
							disabled
							id="last-census-year"
							readOnly
							type="number"
							value={lastCensusYear}
						/>
					</FormFieldWithTooltip>
				</div>
			</div>
		</SettingsCard>
	);
}
