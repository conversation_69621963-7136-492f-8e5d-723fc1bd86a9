"use client";

import { useC<PERSON>back, useEffect, useRef, useState } from "react";

// Proper TypeScript interfaces
export interface SuburbOption {
	id: number;
	displayName: string;
	suburbName: string;
	stateCode: string;
}

interface SuburbSearchResponse {
	success: boolean;
	results: SuburbOption[];
	count: number;
	query: string;
}

interface UseAdminSuburbSearchOptions {
	minSearchLength?: number;
	debounceDelay?: number;
}

interface UseAdminSuburbSearchReturn {
	suburbs: SuburbOption[];
	isLoading: boolean;
	searchError: string | null;
	searchSuburbs: (query: string) => void;
	clearResults: () => void;
}

/**
 * Custom hook for admin suburb search functionality
 * Separates business logic from UI components
 * Follows established patterns from the codebase
 */
export function useAdminSuburbSearch({
	minSearchLength = 2,
	debounceDelay = 300,
}: UseAdminSuburbSearchOptions = {}): UseAdminSuburbSearchReturn {
	const [suburbs, setSuburbs] = useState<SuburbOption[]>([]);
	const [isLoading, setIsLoading] = useState(false);
	const [searchError, setSearchError] = useState<string | null>(null);

	// Refs for cleanup (following existing patterns)
	const abortControllerRef = useRef<AbortController | null>(null);
	const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

	/**
	 * Perform the actual API search
	 */
	const performSearch = useCallback(
		async (query: string) => {
			if (query.length < minSearchLength) {
				setSuburbs([]);
				setSearchError(null);
				return;
			}

			try {
				// Cancel previous request
				if (abortControllerRef.current) {
					abortControllerRef.current.abort();
				}

				// Create new abort controller
				abortControllerRef.current = new AbortController();

				setIsLoading(true);
				setSearchError(null);

				const response = await fetch(
					`/api/admin/suburbs/search?q=${encodeURIComponent(query)}`,
					{
						signal: abortControllerRef.current.signal,
						headers: {
							"Content-Type": "application/json",
						},
					},
				);

				if (!response.ok) {
					const errorData = await response.json().catch(() => ({}));
					const errorMessage =
						errorData.details || errorData.error || `HTTP ${response.status}`;
					throw new Error(errorMessage);
				}

				const data: SuburbSearchResponse = await response.json();

				if (data.success) {
					setSuburbs(data.results);
				} else {
					throw new Error("Search failed");
				}
			} catch (error) {
				if (error instanceof Error && error.name === "AbortError") {
					// Request was cancelled, ignore
					return;
				}

				console.error("Admin suburb search error:", error);
				setSearchError(
					error instanceof Error ? error.message : "Search failed",
				);
				setSuburbs([]);
			} finally {
				setIsLoading(false);
			}
		},
		[minSearchLength],
	);

	/**
	 * Debounced search function
	 */
	const searchSuburbs = useCallback(
		(query: string) => {
			// Clear previous timeout
			if (debounceTimeoutRef.current) {
				clearTimeout(debounceTimeoutRef.current);
			}

			// Set new timeout for debounced search
			debounceTimeoutRef.current = setTimeout(() => {
				performSearch(query);
			}, debounceDelay);
		},
		[performSearch, debounceDelay],
	);

	/**
	 * Clear search results
	 */
	const clearResults = useCallback(() => {
		setSuburbs([]);
		setSearchError(null);
		setIsLoading(false);
	}, []);

	// Cleanup on unmount
	useEffect(() => {
		return () => {
			if (abortControllerRef.current) {
				abortControllerRef.current.abort();
			}
			if (debounceTimeoutRef.current) {
				clearTimeout(debounceTimeoutRef.current);
			}
		};
	}, []);

	return {
		suburbs,
		isLoading,
		searchError,
		searchSuburbs,
		clearResults,
	};
}
