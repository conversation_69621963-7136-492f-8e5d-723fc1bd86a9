/**
 * Efficient key generation utilities for React components
 * Uses modern crypto APIs and optimised hashing for better performance
 */

/**
 * Simple hash function for string inputs (djb2 algorithm)
 * Much faster than crypto for simple use cases
 */
function simpleHash(str: string): string {
  let hash = 5381;
  for (let i = 0; i < str.length; i++) {
    hash = (hash << 5) + hash + str.charCodeAt(i);
  }
  return (hash >>> 0).toString(36); // Convert to base36 for shorter strings
}

/**
 * Generate efficient key for chart components
 */
export function generateChartKey(
  data?: {
    type: string;
    data: Array<Record<string, unknown>>;
    title?: string;
  } | null,
): string {
  // Handle invalid or missing data
  if (!(data && data.data && Array.isArray(data.data))) {
    return `chart-${Date.now()}`;
  }

  // Create a lightweight signature of the data
  const signature = [
    data.type,
    data.data.length.toString(),
    data.title || "untitled",
    // Sample first and last items for uniqueness without processing all data
    data.data.length > 0 ? JSON.stringify(data.data[0]) : "",
    data.data.length > 1 ? JSON.stringify(data.data[data.data.length - 1]) : "",
  ].join("|");

  return `chart-${simpleHash(signature)}`;
}

/**
 * Generate efficient key for table components
 */
export function generateTableKey(children: React.ReactNode): string {
  // Create a simple structural hash without deep traversal
  const childrenArray = React.Children.toArray(children);
  const signature = [
    "table",
    childrenArray.length.toString(),
    // Use component types and basic structure with improved fingerprinting
    childrenArray
      .slice(0, 3)
      .map((child, index) => {
        if (React.isValidElement(child)) {
          if (typeof child.type === "string") {
            // Native element, e.g. "td", "tr", "th"
            return `${child.type}-${index}`;
          }
          // For functional/class components, use displayName or name for better distinction
          const name =
            // Try displayName first – often set in libraries / HOCs
            // fall back to the function/class name
            (child.type as React.ComponentType).displayName ??
            (child.type as React.ComponentType).name ??
            "anon";
          return `${name}-${index}`;
        }
        return `text-${index}`;
      })
      .join(","),
  ].join("|");

  return `table-${simpleHash(signature)}`;
}

/**
 * Generate key for any collapsible component with optional prefix
 */
export function generateCollapsibleKey(
  prefix: string,
  identifier: string | number,
  additionalData?: string,
): string {
  const parts = [prefix, identifier.toString()];
  if (additionalData) {
    parts.push(additionalData);
  }

  return `${prefix}-${simpleHash(parts.join("|"))}`;
}

/**
 * Memoized key generation for expensive operations
 */
const keyCache = new Map<string, string>();
const MAX_CACHE_SIZE = 500;

export function getMemoizedKey(generator: () => string, cacheKey: string): string {
  // Check cache first
  if (keyCache.has(cacheKey)) {
    return keyCache.get(cacheKey)!;
  }

  // Generate new key
  const key = generator();

  // Manage cache size
  if (keyCache.size >= MAX_CACHE_SIZE) {
    // Remove oldest entries (simple FIFO)
    const firstKey = keyCache.keys().next().value;
    if (firstKey !== undefined) {
      keyCache.delete(firstKey);
    }
  }

  keyCache.set(cacheKey, key);
  return key;
}

// Import React for type checking
import React from "react";
